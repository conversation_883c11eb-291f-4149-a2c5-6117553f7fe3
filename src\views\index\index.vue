<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #left>
          <div class="left-section" @click="show = true">
            <span class="iconfont icon-ren-copy" style="color: #fff"></span>
            <div v-if="factoryDisplayName" class="factory-display-left">{{ factoryDisplayName }}</div>
          </div>
        </template>
        <template #title>
          <div class="" style="display: flex; align-items: center;">
            <img src="@/assets/logo/homeLogo.png" alt="" class="logo-img" />
            <div class="global-hfont">IMOM-PDA</div>
          </div>
        </template>
        <template #right>
          <div class="" @click="layout">
            <span class="iconfont icon-tuichu" style="color: #fff"></span>
          </div>
        </template>
      </van-nav-bar>
    </van-sticky>

    <div class="page-box">
      <van-grid :border="false" :column-num="3">
        <van-grid-item v-for="(item, index) in gridData" :key="index" @click="goItemUrl(item.url)" v-if="item.isShow">
          <img :src="require(`@/assets/grid/${item.img}`)" class="index-grid-img" />
          <div class="index-media-body">{{ item.text }}</div>
        </van-grid-item>
      </van-grid>
    </div>

    <van-popup v-model="show" position="left" class="pop-content">
      <div class="info-content">
        <div class="info-des" @click="goOrganization">
          <span>账 套: {{ segName }}</span>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>
        <div class="info-des">
          <span>工 号: {{ userId }}</span>
        </div>
        <div class="info-flex" @click="chooseWarehouse">
          <div class="des">仓 库:</div>
          <div class="com-name" v-if="warehouseName">{{ warehouseName }}</div>
          <div class="com-name" v-else>请选择仓库</div>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>

        <div class="info-des" @click="getVersion">
          <span>检查新版本</span>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>
        <div class="info-des">
          <span>版本信息</span>
          <div class="com-name" v-if="thisVerion">{{ thisVerion }}</div>
        </div>

      </div>
    </van-popup>
  </div>
</template>

<script>
import { Dialog } from "vant";
import { post } from "../../api/base-service";
import {
  compare
} from '@/utils/tools';
import userPreferencesManager from '../../utils/userPreferences';

export default {
  data() {
    return {
      show: false,
      segName: localStorage.getItem("segName"),
      warehouseName: localStorage.getItem("warehouseName"),
      userId: localStorage.getItem("userId"),
      thisVerion: "",
      factoryDisplayName: localStorage.getItem("factoryDisplayName"),
    };
  },
  computed: {
    gridData() {
      return [
        {
          img: "load.png",
          text: "精细化",
          url: "/unload",
          isShow: localStorage.getItem('segNo') == 'KF000000',
        },
        {
          img: "驳回.png",
          text: "登记驳回",
          url: "/regis-reject",
          isShow: localStorage.getItem('segNo') == 'KF000000',
        },
        {
          img: "invertedStorehouse.png",
          text: "倒库",
          url: "/inverted-storehouse",
          isShow: localStorage.getItem('segNo') == 'KF000000',
        },
        {
          img: "panku.png",
          text: "盘库",
          url: "/checkStorage",
          isShow: localStorage.getItem('segNo') == 'KF000000',
          // isShow: false,
        },
        {
          img: "物流.svg",
          text: "精细化",
          url: "/unload-cq",
          // , "JD000000", "JE000000" JC是重庆
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("精细化"),
        },
        {
          img: "正向.svg",
          text: "车辆排队",
          url: "/vehicle-queue",
          isShow: localStorage.getItem("segNo") == "JC000000" && this.hasMenuPermission("车辆排队"),
        },
        {
          img: "多维度看板.svg",
          text: "装卸示图",
          url: "/loading-unload-diagram",
          // , "JD000000", "JE000000"
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("装卸示图"),
        },
        {
          img: "排期.svg",
          text: "作业清单",
          url: "/crane-order-list",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("作业清单"),
        },
        {
          img: "备货管理.svg",
          text: "实绩补录",
          url: "/record-actual",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("实绩补录"),
        },
        {
          img: "入库管理.svg",
          text: "库位变更",
          url: "/scan-pack-store",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("库位变更"),
        },
        {
          img: "调拨.svg",
          text: "车辆排序",
          url: "/vehicle-sorting",
          // , "JD000000", "JE000000"
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("车辆排序"),
        },
        {
          img: "盘货.svg",
          text: "找料",
          url: "/find-materials",
          // , "JD000000", "JE000000"
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("找料"),
        },
        {
          img: "入库.svg",
          text: "模具入库",
          url: "/mold-storage",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("模具入库"),
        },
        {
          img: "定位.svg",
          text: "区域定位",
          url: "/regional-position",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("区域定位"),
        },

        {
          img: "风控.svg",
          text: "设备点检",
          url: "/spot-check",
          // JE是西安
          isShow: ["JE000000", "JC000000"].includes(localStorage.getItem("segNo")) &&
            (localStorage.getItem("segNo") === "JE000000" || this.hasMenuPermission("设备点检")),
        },
        {
          img: "优化计划.svg",
          text: "检修实绩",
          url: "/overhaul",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("检修实绩"),
        },
        {
          img: "优化计划.svg",
          text: "设备故障",
          url: "/fault",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("设备故障"),
        }, {
          img: "排期.svg",
          text: "上料补录",
          url: "/up-pack",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("上料补录"),
        }, {
          img: "吊运管理.svg",
          text: "上料吊运",
          url: "/unload-cq/hoisting-info",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("上料吊运"),
        },
        {
          img: "配件更换.svg",
          text: "吊具更换",
          url: "/sling",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("吊具更换"),
        },
        {
          img: "入库打印.svg",
          text: "入库打印",
          url: "/print-inbound",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("入库打印"),
        },
        {
          img: "查询知识库.svg",
          text: "捆包查询",
          url: "/unload-cq/seach-pack",
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("捆包查询"),
        },
        {
          img: "定位.svg",
          text: "捆包定位",
          url: "/init-pack",
          // isShow: true,
          isShow: ["JC000000"].includes(localStorage.getItem("segNo")) && this.hasMenuPermission("捆包定位"),
        },
      ];
    },

    formatFactoryName() {
      const factoryBuildingName = localStorage.getItem("factoryBuildingName");
      if (!factoryBuildingName) return "";

      // 格式化厂区名称，提取"一期"、"二期"等
      // 匹配中文数字+期的模式
      const match = factoryBuildingName.match(/([一二三四五六七八九十\d]+期)/);
      if (match) {
        return match[1];
      }

      // 如果没有匹配到期数，返回完整的厂房名称但去掉"厂房"字样
      return factoryBuildingName.replace(/厂房$/, '');
    }
  },
  async created() {
    this.thisVerion = localStorage.getItem("thisVerion") || "";

    this.updateFactoryDisplayName();

    // 监听localStorage变化，当厂区信息更新时重新显示
    window.addEventListener('storage', this.handleStorageChange);
  },
  beforeDestroy() {
    window.removeEventListener('storage', this.handleStorageChange);
  },
  beforeRouteLeave(to, from, next) {
    console.log(to);
    if (to.name == 'record-actual') {
      to.meta.keepAlive = false;

    }
    next(0);
  },
  methods: {
    handleStorageChange(event) {
      if (event.key === 'factoryBuildingName') {
        this.updateFactoryDisplayName();
      }
    },
    updateFactoryDisplayName() {
      this.factoryDisplayName = this.formatFactoryName;
    },

    // 检查是否有指定菜单的权限（只针对JC000000账套）
    hasMenuPermission(menuName) {
      const segNo = localStorage.getItem("segNo");

      // 只有JC000000账套才需要权限控制，其他账套直接返回true
      if (segNo != "JC000000") {
        return true;
      }

      // 检查权限是否成功加载
      const permissionsLoaded = localStorage.getItem("menuPermissionsLoaded");

      // 只有权限确实加载成功才继续，否则不显示菜单 (更健壮的检查)
      if (permissionsLoaded !== "true" && permissionsLoaded !== true && String(permissionsLoaded).trim() !== "true") {
        return false;
      }

      try {
        const menuPermissions = localStorage.getItem("menuPermissions");
        if (!menuPermissions) {
          return false;
        }
        const permissions = JSON.parse(menuPermissions);
        return permissions.includes(menuName);
      } catch (error) {
        console.error('解析菜单权限失败:', error);
        return false;
      }
    },
    goItemUrl(url) {
      this.$router.togo(url);
    },
    layout() {
      Dialog.confirm({
        title: "提示",
        message: "是否要退出",
      })
        .then(() => {
          // 保存当前用户的偏好设置
          const currentUserId = localStorage.getItem("userId");
          if (currentUserId) {
            userPreferencesManager.saveCurrentPreferencesToUser(currentUserId);
          }

          // 清除登录相关的localStorage数据，但保留所有用户的偏好设置
          const keysToRemove = [
            "userId", "userName", "accessToken", "thisVerion",
            "segNo", "segName", "factoryName", "factoryArea", "factoryAreaTrue",
            "factoryBuilding", "factoryBuildingName", "warehouseCode", "warehouseName",
            "menuPermissions", "menuPermissionsLoaded"
          ];

          keysToRemove.forEach(key => {
            localStorage.removeItem(key);
          });

          sessionStorage.clear();
          this.$router.push("/");
        })
        .catch(() => { });
    },

    /**
     * 重新选择账套
     */
    goOrganization() {
      this.$router.togo("/organization");
    },

    /**
     * 重新选择仓库
     */
    chooseWarehouse() {
      this.$router.togo("/select-factory");
    },

    // 重写activated生命周期以便从厂区选择页面返回时更新显示
    activated() {
      this.updateFactoryDisplayName();
    },

    async getVersion() {
      var that = this;
      var wgtVer;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, function (inf) {
        console.log("当前版本号", inf.version);
        wgtVer = inf.version; // 当前版本号
        localStorage.setItem("thisVerion", inf.version);
      });
      that.thisVerion = localStorage.getItem("thisVerion") || "";
      const params = {
        serviceId: "S_LI_RL_0112",
      };
      let res = await post(params);
      if (res.__sys__.status != -1) {
        let newUrl = res.result.downUrl;
        let newVersion = res.result.version;
        localStorage.setItem("newVerion", newVersion);
        let flag = compare(newVersion, wgtVer)
        if (flag == 1) {
          plus.nativeUI.confirm(
            `检测到新版本(${newVersion})，是否更新`,
            function (e) {
              if (e.index == 0) {
                var wgtUrl = newUrl;
                var downloadTask = plus.downloader.createDownload(wgtUrl, { //拿到下载任务的对象
                  filename: '_doc/update/'
                }, function (d, status) {
                  plus.nativeUI.closeWaiting();
                  if (status == 200) { //在回调中根据状态 进行操作
                    var path = d.filename; //下载apk
                    plus.runtime.install(
                      path, {},
                      function () {
                        plus.nativeUI.alert(
                          "更新完成",
                          function () {
                            plus.runtime.restart();
                          }
                        );
                      },
                      function (e) {
                        console.log(
                          "安装wgt文件失败[" +
                          e.code +
                          "]：" +
                          e.message
                        );
                        plus.nativeUI.alert(
                          "版本更新失败:" +
                          "[" +
                          e.code +
                          "]：" +
                          e.message
                        );
                      }
                    ); // 自动安装apk文件

                  } else {
                    console.log("下载更新失败！");
                    plus.nativeUI.toast("下载更新失败！");
                  }
                });
                // 开始下载
                downloadTask.start()
                //显示下载状态显示框
                var waiting = plus.nativeUI.showWaiting("正在下载 - 0%", {
                  back: "none"
                });
                var pre_percent = 0;
                //监听下载
                downloadTask.addEventListener('statechanged', function (download, status) {
                  //显示loading和进度
                  switch (download.state) {
                    case 0:
                      //下载任务处于可调度下载状态
                      break;
                    case 1:
                      //下载任务建立网络连接，发送请求到服务器并等待服务器的响应
                      break;
                    case 2:
                      // 下载任务网络连接已建立，服务器返回响应，准备传输数据内容
                      break;
                    case 3:
                      // 下载任务接收数据，计算下载进度
                      let percent = parseInt(parseFloat(download.downloadedSize) / parseFloat(download
                        .totalSize) * 100)
                      //取整的情况下会出现同一个百分比出现多次的情况，每次都会执行waiting.setTitle()
                      //有时会出现percent无法正常显示的情况，可能是因为频繁执行waiting.setTitle()导致堆栈内存溢出的问题
                      //增加判断，当percent变化时才执行waiting.setTitle()，以减少函数执行次数，目测有效果
                      if (percent > pre_percent) {
                        waiting.setTitle("正在下载 - " + percent + "%");
                        pre_percent = percent
                      }
                      //经测试，并没有返回状态4，所以自行执行关闭弹窗代码
                      //当已经下载的文件大小等于总文件大小时，执行关闭
                      if (download.downloadedSize == download.totalSize) {
                        plus.nativeUI.closeWaiting();
                      }
                      break;
                    case 4:
                      // 下载任务已完成
                      plus.nativeUI.closeWaiting();
                      break;
                  }
                })
              }
            },
            "检测到新版本",
            ["确定", "取消"]
          );
        } else {
          that.$toast("当前为最新版本");
          console.log("已经是最新版本");
        }
      } else {
        plus.nativeUI.toast(res.data.__sys__.msg);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page-box {
  background-color: #fff;
  //height: 100vh; //若页面占不满时加
}

.left-section {
  display: flex;
  align-items: center;
}

.factory-display {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  margin-top: 2px;
  text-align: center;
}

.factory-display-left {
  margin-left: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1;
}

.logo-img {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.ic-color {
  color: #696969;
}

.global_left_icon {
  position: absolute;
  left: 16px;
}

.index-content {
  margin-top: 32px;
  margin-left: 28px;
  margin-right: 28px;
}

.index-grid-img {
  width: 68px;
  height: 68px;
  // background: #1580f4;
  box-shadow: 1px 5px 9px -1px rgba(69, 93, 222, 0.26);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.index-media-body {
  font-size: 18px;
  //font-family: Noto Sans SC;
  font-weight: 550;
  color: #3d3d3d;
  line-height: 25px;
  letter-spacing: 7px;
}

// 弹出层
.pop-content {
  width: 80%;
  height: 100%;
}

.info-content {
  margin: 20px;
}

.info-des {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  //font-family: Noto Sans SC;
  font-weight: 550;
  color: #3d3d3d;
  line-height: 35px;
  margin-bottom: 8px;
  border-bottom: 1px solid #dcdcdc;
}

.info-right {
  margin-top: 8px;
  //float: right;
  margin-left: 120px;
}

.info-flex {
  font-size: 15px;
  // font-family: Noto Sans SC;
  font-weight: 550;
  color: #000;
  line-height: 30px;
  margin-bottom: 8px;
  border-bottom: 1px solid #dcdcdc;
  height: 60px;
  display: flex;
  align-items: center;

  .des {
    color: #3d3d3d;
    width: 60px;
  }
}
</style>
