<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">吊具更换</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
                </template>
                <!-- <template #right>
                    <span class="iconfont icon-sousuo" @click="onClickRight"></span>
                </template> -->
            </van-nav-bar>
        </div>

        <div>
            <div>
                <van-field required label="行车" is-link readonly placeholder="请选择行车" v-model="craneName"
                    @click="showCrane" />
                <van-field required label="原吊具" is-link readonly v-model="oldSling.hoistType" @click="showSling('old')"
                    placeholder="请选择原吊具" />
                <van-field label="原吊具重量" readonly v-model="oldSling.hoistWeight" />
                <van-field required label="新吊具" is-link readonly v-model="newSling.hoistType" @click="showSling('new')"
                    placeholder="请选择新吊具" />
                <van-field label="新吊具重量" readonly v-model="newSling.hoistWeight" />

            </div>

            <van-button type="info" size="large" class="foot-sticky-btn" @click="changeSling()">更换吊具</van-button>

            <van-dialog v-model="isShowCrane" title="行车" show-cancel-button :beforeClose="confirmCrane">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="craneRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.craneName" v-for="(item, index) in craneList" :key="index"
                                @click="craneRadio = item.craneId">
                                <template #right-icon>
                                    <van-radio :name="item.craneId" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>

            <van-dialog v-model="isShowSling" title="吊具" show-cancel-button :beforeClose="confirmSling">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="slingRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.hoistType" :label="item.hoistWeight"
                                v-for="(item, index) in slingList" :key="index" @click="slingRadio = item.hoistId">
                                <template #right-icon>
                                    <van-radio :name="item.hoistId" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog } from 'vant';

export default {
    data() {
        return {
            craneId: '',
            craneName: '',
            isShowCrane: false,
            craneRadio: '',
            craneList: [],
            isShowSling: false,
            slingRadio: '',
            slingList: [],
            oldSling: {},
            newSling: {},
            slingType: '',
        };
    },

    methods: {

        onClickLeft() {
            this.$router.replace({ name: 'index' });
        },

        // 确认行车
        async confirmCrane(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }
            if (!this.craneRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }
            const currentData = this.craneList.find(p => p.craneId == this.craneRadio);
            if (!currentData) {
                done();
                return;
            }
            this.craneName = currentData.craneName;
            this.craneId = currentData.craneId;
            done(true);
        },

        // 获取行车
        async getCraneList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }
            this.craneList = result.list;
        },

        // 获取机组
        async getSlingList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0025',
            };
            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                this.$toast(result.__sys__.msg);
                return;
            }
            if (!result.result || result.result.length == 0) {
                this.$toast('没有找到吊具');
                return;
            }
            this.slingList = result.result;
        },
        // 确认吊具
        async confirmSling(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }
            if (!this.slingRadio) {
                Dialog.alert({ message: '请选择吊具' });
                done(false);
                return;
            }
            const currentData = this.slingList.find(p => p.hoistId == this.slingRadio);
            if (!currentData) {
                done();
                return;
            }
            if (this.slingType == 'old') {
                this.oldSling = currentData;
            } else {
                this.newSling = currentData;
            }
            done(true);
        },

        // 显示行车
        showCrane() {
            this.isShowCrane = true;
        },
        // 显示吊具
        showSling(type) {
            this.isShowSling = true;
            this.slingType = type;
        },
        // 更换吊具
        async changeSling() {
            if (!this.craneId) {
                Dialog.alert({ message: '请选择行车' });
                return;
            }
            if (!this.oldSling.hoistId || !this.newSling.hoistId) {
                Dialog.alert({ message: '请选择吊具' });
                return;
            }
            let queryMap = {
                serviceId: 'S_LI_DS_0026',
                craneId: this.craneId,
                hoistId: this.newSling.hoistId,
                hoistWeight: this.newSling.hoistWeight,
            };
            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                this.$toast(result.__sys__.msg);
                return;
            }
            this.$toast('更换吊具成功');
        },
    },

    mounted() {
        this.craneId = '';
        this.craneName = '';
        this.getCraneList();
        this.getSlingList();
    }
};
</script>

<style scoped>
.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>