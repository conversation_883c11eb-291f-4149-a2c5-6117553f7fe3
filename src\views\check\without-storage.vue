<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">无单盘库</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <van-field label="库位" @keyup.enter.native="searchLocationName" :rules="[{ required: true, message: '请输入或扫描库位' }]"
      class="all-font-size">
      <template #input>
        <input type="search" class="new-field" :disabled="isItDisable" placeholder="请输入或扫描库位" v-model="locationId" />
      </template>
      <template #button>
        <van-button size="small" :type="isItDisable ? dangerType : infoType" @click="lockIt">
          {{ isItDisable ? "已锁定" : "未锁定" }}
        </van-button>
      </template>
    </van-field>
    <van-field label="库位名称" class="all-font-size" placeholder="库位名称" v-model="locationName" readonly />
    <van-field label="捆包" @keyup.enter.native="getBaleByPackId" :rules="[{ required: true, message: '请输入或扫描捆包号' }]"
      class="all-font-size">
      <template #input>
        <input type="search" class="new-field" placeholder="请输入或扫描捆包号" v-model="packId" ref="labelsecond" />
      </template>
    </van-field>
    <van-field label="净重" class="all-font-size" placeholder="净重" v-model="netWeight"
      :readonly="isDisable ? true : false" />
    <van-field label="毛重" class="all-font-size" placeholder="毛重" v-model="grossWeight"
      :readonly="isDisable ? true : false" />
    <van-field label="件数" class="all-font-size" placeholder="件数" v-model="pieceNum"
      :readonly="isDisable ? true : false" />
    <div class="in-content">
      <div class="detail_textarea">
        <div class="detail_text" style="padding-bottom: 10px">
          <div class="fourline-blue"></div>
          <div class="baletext2" style="margin-left: 0; margin-top: 14px">
            已扫捆包合计 : <span class="span-count">{{ packList.length }}</span>
          </div>
        </div>
      </div>

      <div class="search-list" v-if="packList && packList.length != 0">
        <van-swipe-cell v-for="(item, index) in packList" :key="index">
          <van-cell :border="false">
            <!-- @click="alertItemDialog(item.packId)" -->
            <template #title>
              <div class="custom-title show-text">
                <div>
                  {{ item.labelId }}
                </div>
                <!-- 不在库显示 -->
                <div class="to-text" v-if="item.instockFlag != 0">
                  <span>不在库</span>
                </div>
              </div>
              <div class="custom-title">{{ item.packId }}</div>
            </template>
            <template #label>
              <div class="custom-title">规格： {{ item.specsDesc }}</div>
              <div class="custom-title">库位名称： {{ item.locationName }}</div>
              <div class="custom-title">
                重/件：{{ item.netWeight }}/{{ item.pieceNum }}
              </div>
            </template>
          </van-cell>
          <template #right>
           <!-- <button class="swiper-btn-update" @click="updateListItem(item,index)">
              <span class="swiper-text">修改</span>
            </button> -->
<!--            <van-button class="swiper-btn-update" type="info" square text="修改" @click="updateListItem(item,index)" /> -->
            <van-button class="swiper-btn-delete" square type="danger" text="删除" @click="deleteListItem(index)" />
          </template>
        </van-swipe-cell>
      </div>
      <div v-else></div>
    </div>
    <div class="mui-input-row3" v-show="isShowBottom">
      <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
        确&nbsp; &nbsp; &nbsp;&nbsp; 认
      </button>
      <button id="block_button" type="button" class="mui-btn3" v-preventReClick="3000" @click="generateInventoryLst">
        上&nbsp; &nbsp; &nbsp;&nbsp; 传
      </button>
    </div>
    <HmPopup v-if="showPop" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  <van-dialog v-model="showField" title="修改信息" :beforeClose="beforeCloseField" show-cancel-button>
      <van-field clickable v-model="updateSpecsDesc" label="规格" placeholder="请填写" />
      <van-field clickable v-model="updateNetWeight" label="净重" placeholder="请填写" />

    </van-dialog>
    <!-- <van-dialog v-model="isShowPackInfo" title="捆包详情">
      <van-field label="捆包号" class="all-font-size" v-model="packObj.packId" readonly />
      <van-field label="标签号" class="all-font-size" v-model="packObj.labelId" readonly />
      <van-field label="库位名称" class="all-font-size" v-model="packObj.locationName" readonly />
      <van-field label="规格" class="all-font-size" v-model="packObj.specsDesc" readonly />
      <van-field label="重量" class="all-font-size" v-model="packObj.netWeight" readonly />
      <van-field label="件数" class="all-font-size" v-model="packObj.pieceNum" readonly />
    </van-dialog> -->
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import HmPopup from "@/components/HmPopup.vue";
  import {
    Dialog
  } from "vant";
  import {
    isEmpty
  } from '@/utils/tools';
  export default {
    data() {
      return {
        fieldIndex:-1,
        showField: false,
        updateSpecsDesc:'',//规格
        updateNetWeight:'',//净重
        showOverlay: false,
        scanList: [],
        testList: [],
        baleList: {},
        packList: [],
        show: false,
        showPop: false,
        infoType: "info",
        dangerType: "danger",
        packId: "",
        labelId: "",
        warehouseName: "",
        locationName: "",
        locationId: "",
        matInnerId: "",
        netWeight: "",
        grossWeight: "",
        pieceNum: "",
        isDisable: false,
        isItDisable: false,
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight
        // packObj: {
        //   packId: '',
        // },
        // isShowPackInfo: false,
      };
    },
    components: {
      HmPopup
    },
    created() {
      this.warehouseName = localStorage.getItem("warehouseName");
      this.getStorageCache();
      // let packListStr = localStorage.getItem('packList');
      // this.packList = packListStr.split(',').map(s => ({packId: s}));
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
      if (window.history && window.history.pushState) {
        // 往历史记录里面添加一条新的当前页面的url
        history.pushState(null, null, document.URL);
        // 给 popstate 绑定一个方法 监听页面刷新
        window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
      }
    },
    watch: {
      show(oldValue, newValue) {
        if (oldValue) {
          this.packId = "";
          this.locationId = "";
          this.netWeight = "";
          this.labelId = "";
          this.grossWeight = "";
          this.pieceNum = "";
          this.baleList = {};
          this.chooseList = [];
          this.testList = [];
        }
      },
      locationId: {
        handler: function() {
          this.locationName = "";
        }
      }
    },
    methods: {
      showtest() {
        console.log("点击了关闭弹窗");
        this.showPop = !this.showPop;
      },

      changeFocus() {
        this.$nextTick(() => {
          this.$refs.labelsecond.focus();
        });
      },
      lockIt() {
        this.isItDisable = !this.isItDisable;
      },
      getCheckInfo(val) {
        console.log("接收到了传过来的值", val);
        this.showPop = !this.showPop;
        this.scanList = val;
        this.baleList = {
          ...val[0]
        };
        // this.locationName = this.baleList.locationName;
        // this.locationId = this.baleList.locationId;
        this.matInnerId = this.baleList.matInnerId;
        this.netWeight = this.baleList.netWeight;
        this.grossWeight = this.baleList.grossWeight;
        this.pieceNum = this.baleList.pieceNum;
        this.packId = this.baleList.packId;
        this.labelId = this.baleList.labelId;
      },
      updateListItem(item, index) {
        this.showField = true;
        this.updateNetWeight = item.netWeight;
        this.updateSpecsDesc = item.specsDesc;
        this.fieldIndex = index;
        //this.$router.togo("/instorage");
      },
      beforeCloseField(action, done) {
        if (action === "confirm") {
          //添加前进判断调用哪个方法
          this.packList[this.fieldIndex].netWeight = this.updateNetWeight;
          this.packList[this.fieldIndex].specsDesc = this.updateSpecsDesc;
          this.packList[this.fieldIndex].specDesc = this.updateSpecsDesc;
          console.log(this.packList[this.fieldIndex]);
          //  this.$refs.child.saveBale();
          this.fieldIndex = -1;
          done();
          //  setTimeout(done, 1000);
        } else {
          this.fieldIndex = -1;
          this.updateNetWeight = '';
          this.updateSpecsDesc = '';
          done();
        }
      },
      onConfirm() {
        if (this.isItDisable) {
          if (this.scanList.length > 0) {
            this.getScanInfo();
          } else {
            this.$toast("捆包号不能为空!");
          }
        } else {
          if (this.scanList.length > 0) {
            if ( isEmpty(this.locationName) ) {
              this.$toast("库位库位名称不能为空!");
            } else {
              this.getScanInfo();
            }
          } else {
            if ( isEmpty(this.locationName)) {
              this.$toast("库位库位名称不能为空!");
            } else {
              if (
                this.packId == "" ||
                this.packId == null ||
                this.packId == undefined
              ) {
                this.$toast("捆包号不能为空!");
              } else {
                this.getScanInfo();
              }
            }
          }
        }
      },

      beforeClose(action, done) {
        if (action === "confirm") {
          if (this.scanList.length > 0) {
            if (
              this.locationId == "" ||
              this.locationId == null ||
              this.locationId == undefined
            ) {
              this.$toast("库位不能为空!");
            } else {
              this.getScanInfo();
            }
          } else {
            this.$toast("未查到捆包信息!");
          }
          // this.getScanInfo();
          done();
        } else {
          done();
        }
      },

      getScanInfo() {
        const val = [{
          matInnerId: this.matInnerId,
          packId: this.baleList.packId ? this.baleList.packId : this.packId,
          locationId: this.isItDisable ? this.baleList.locationId : this.locationId,
          locationName: this.isItDisable ? this.baleList.locationName : this.locationName,
          grossWeight: Object.keys(this.baleList).length != 0 ? this.baleList.grossWeight : "",
          labelId: this.labelId,
          specsDesc: Object.keys(this.baleList).length != 0 ? this.baleList.specDesc : "",
          netWeight: Object.keys(this.baleList).length != 0 ? this.baleList.netWeight : "",
          pieceNum: Object.keys(this.baleList).length != 0 ? this.baleList.pieceNum : "",
          instockFlag: Object.keys(this.baleList).length != 0 ? this.baleList.instockFlag : "",
          tradeCode: Object.keys(this.baleList).length != 0 ? this.baleList.tradeCode : "",
          settleUserNum: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserNum : "",
          settleUserName: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserName : "",
        }];
        console.log("val",val);
        let flag = this.packList.some(item => {
          return item.packId === val[0].packId;
        });
        if (flag) {
          this.$toast("此捆包号已添加，请重新扫描其他捆包号");
          this.clearList();
        } else {
          this.packList = [...val, ...this.packList];
          // localStorage.setItem('packList', this.packList.map(p => p.packId));
          this.clearList();
        }
      },

      clearList() {
        this.packId = "";
        this.labelId = "";
        this.netWeight = "";
        this.grossWeight = "";
        this.pieceNum = "";
        this.matInnerId = "";
        this.baleList = {};
        this.scanList = [];
        this.testList = [];
      },

      onClickLeft() {
        if (this.packList.length == 0) {
          this.$router.goBack();
          return;
        }
        Dialog.confirm({
            title: "提示",
            message: "清单里有数据未提交确认退出？"
          })
          .then(() => this.$router.push('/checkStorage'))
          .catch(() => {});
      },
      backChange() {
        // 手机自带返回
        if (this.packList.length == 0) {
          this.$router.goBack();
          return;
        }
        Dialog.confirm({
            title: "提示",
            message: "清单里有数据未提交确认退出？"
          })
          .then(() => {
            this.$router.goBack();
          })
          .catch(() => {
            if (window.history && window.history.pushState) {
              // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
              history.pushState(null, null, document.URL);
            }
          });
      },

      showScan() {
        this.show = true;
      },

      deleteListItem(index) {
        //删除数组中值
        this.$delete(this.packList, index);
      },

      async getBaleByPackId() {
        const params = {
          serviceId: "S_LI_RL_0135",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          factoryAreaName: localStorage.getItem("factoryName"),
         // warehouseCode: localStorage.getItem("warehouseCode"),
          packId: this.packId
        };
        await baseApi.baseService(params).then(res => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1
                }
              };
            }
            if (dataObj) {
              if (dataObj.result.length == 0) {
                this.$toast("没有查询到相应捆包信息！");
                this.isDisable = false;
                this.netWeight = "";
                this.grossWeight = "";
                this.pieceNum = "";
                this.matInnerId = "";
                this.scanList = [];
              } else {
                // 不在库, 则弹框提示; 0为在库
                let dataObjList = Array.from(dataObj.result).filter(
                  d => d.instockFlag == "0"
                );
                // 没有在库的捆包
                if (dataObjList.length == 0) {
                  this.scanList = dataObj.result[0];
                  this.baleList = {
                    ...dataObj.result[0]
                  };
                } else {
                  this.scanList = dataObjList[0];
                  this.baleList = {
                    ...dataObjList[0]
                  };
                }


                this.labelId = this.baleList.labelId;
                this.matInnerId = this.baleList.matInnerId;
                this.netWeight = this.baleList.netWeight;
                this.grossWeight = this.baleList.grossWeight;
                this.pieceNum = this.baleList.pieceNum;
                this.isDisable = true;
                if (this.isItDisable) {
                  this.getScanInfo();
                } else {
                  if ( isEmpty(this.locationName)) {
                    this.$toast("库位库位名称不能为空!");
                  } else {
                    this.getScanInfo();
                  }
                }
              }
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        });

      },

      /** 上传 */
      async generateInventoryLst() {
        const params = {
          serviceId: "S_LI_RL_0136",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          factoryAreaName: localStorage.getItem("factoryName"),
          rowList: this.packList
        };
        await baseApi.baseService(params).then(res => {
          if (res.data.__sys__.status != -1) {
            this.$toast(res.data.__sys__.msg);
            this.packList = [];
            this.locationId = "";
            this.locationName = "";
            let _this = this;
            setTimeout(function() {
              _this.deleteStorageCache();
              // 需要执行的代码
            }, 1000);

            // localStorage.removeItem('packList');
          } else {
            this.$toast("调用失败");
          }
        });
      },

      //查询库位名称
      async searchLocationName() {
        const params = {
          serviceId: "S_LI_RL_0125",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          locationId: this.locationId
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                this.locationId = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
                this.$nextTick(() => {
                  this.$refs.labelsecond.focus();
                });
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            } else {
              this.$toast(res.data.__sys__.msg);
              this.locationId = "";
              this.locationName = "";
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async addStorageCache() {
        const params = {
          serviceId: "S_LI_RL_0131",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "01",
          rowList: this.packList
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data.__sys__.status == "-1") {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async getStorageCache() {
        const params = {
          serviceId: "S_LI_RL_0132",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "01"
        };
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == "-1") {
          this.$toast(res.data.__sys__.msg);
          this.packList = [];
          return;
        }

        if (res.data.result.length == 0) {
          // 本次没有数据
          this.packList = [];
        } else {
          // 本次有数据
          this.packList = res.data.result;
        }
      },
      async deleteStorageCache() {
        const params = {
          serviceId: "S_LI_RL_0133",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryAreaTrue"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "01"
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data.__sys__.status == "-1") {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch(err => {
            console.log(err);
          });
      }
    },
    beforeDestroy() {
      window.removeEventListener("popstate", this.backChange);
      if (this.packList.length > 0) {
        this.addStorageCache();
      }else{
         this.deleteStorageCache();
      }

    }
    // destroyed() {
    //   console.log("----destroyed-----");
    // }
  };
</script>

<style lang="less" scoped>
  .download-des {
    font-size: 20px;
    font-family: Noto Sans SC;
    font-weight: 500;
    line-height: 22px;
    margin-top: 20px;
    text-align: center;
  }

  .swiper-btn-delete {
    height: 100%;
  }
  .swiper-btn-update{
     height: 100%;
     background-color: cornflowerblue;
  }
  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .search-list {
    width: 100%;
    height: 26vh;
    border: 1px solid #dcdcdc;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }

    .show-text {
      display: flex;
      justify-content: space-between;

      .to-text {
        border: solid 1px red;
        background: red;
        color: white;
      }
    }
  }
</style>
