<template>
  <div class="qr-scanner">
    <!-- 扫码按钮 -->
    <van-button 
      type="primary" 
      icon="scan" 
      size="small"
      @click="startScan"
      :loading="scanning"
      class="scan-button"
      :disabled="!isSupported"
    ></van-button>

    <!-- 不支持扫码时显示的提示 -->
    <van-dialog
      v-model="showUnsupportedDialog"
      title="提示"
      message="您的设备不支持扫码功能，请手动输入"
      show-cancel-button
      @confirm="showUnsupportedDialog = false"
      @cancel="showUnsupportedDialog = false"
    />

    <!-- 扫码弹窗 -->
    <van-popup 
      v-model="showScanner" 
      position="center" 
      :style="{ width: '90%', height: '70%' }"
      closeable
      @close="stopScan"
    >
      <div class="scanner-container">
        <div class="scanner-header">
          <h3>扫描二维码/条形码</h3>
          <p>请将二维码/条形码放入扫描框内</p>
        </div>
        
        <!-- 摄像头视频区域 -->
        <div class="camera-container">
          <div id="qr-reader" ref="qrReader"></div>
          <div class="scan-line" v-if="scanning"></div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="scanner-actions">
          <van-button @click="stopScan" type="default">取消</van-button>
          <van-button @click="toggleCamera" type="primary" v-if="hasMultipleCamera">
            切换摄像头
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Dialog, Toast } from 'vant';

export default {
  name: 'QRScanner',
  props: {
    // 扫码成功后是否自动关闭扫描器
    autoClose: {
      type: Boolean,
      default: true
    },
    // 支持的扫码格式
    formats: {
      type: Array,
      default: function() {
        return [
          'QR_CODE',
          'CODE_128',
          'CODE_39',
          'EAN_13',
          'EAN_8',
          'UPC_A',
          'UPC_E'
        ];
      }
    }
  },
  data: function() {
    return {
      showScanner: false,
      scanning: false,
      html5QrCode: null,
      cameras: [],
      currentCameraId: null,
      hasMultipleCamera: false,
      isSupported: false,
      showUnsupportedDialog: false,
      Html5Qrcode: null,
      permissionStatus: null,
      hasPermission: false,
      isCheckingPermission: false,
      // 监听器标识，防止重复添加
      hasSetupPermissionMonitoring: false,
      hasSetupVisibilityListener: false,
      // 保存监听器引用以便清理
      visibilityChangeHandler: null,
      windowFocusHandler: null,
      // 组件销毁标识
      isDestroyed: false
    };
  },
  mounted: function() {
    this.checkSupport();
    this.setupPermissionMonitoring();
    this.setupVisibilityChangeListener();
  },
  methods: {
    // 检查设备兼容性
    checkSupport: function() {
      var self = this;
      try {
        // 检查基础API支持
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          this.isSupported = false;
          return;
        }

        // 检查Promise支持
        if (typeof Promise === 'undefined') {
          this.isSupported = false;
          return;
        }

        // 尝试动态加载html5-qrcode库
        this.loadQrCodeLibrary();
        
        // 异步检查摄像头权限状态（不影响基础支持判断）
        this.checkCameraPermission().catch(function(error) {
          if (!self.isDestroyed) {
            console.error('权限检查失败:', error);
          }
        });
      } catch (error) {
        console.error('兼容性检查失败:', error);
        this.isSupported = false;
      }
    },

    // 检查摄像头权限状态
    checkCameraPermission: function() {
      var self = this;
      
      if (this.isCheckingPermission) {
        return Promise.resolve();
      }
      
      this.isCheckingPermission = true;
      
      return new Promise(function(resolve) {
        try {
          // 检查是否支持permissions API
          if (navigator.permissions && navigator.permissions.query) {
            navigator.permissions.query({ name: 'camera' }).then(function(result) {
              if (!self.isDestroyed) {
                self.permissionStatus = result;
                self.hasPermission = result.state === 'granted';
                self.isCheckingPermission = false;
              }
              resolve();
            }).catch(function(error) {
              if (!self.isDestroyed) {
                console.log('权限查询失败，尝试其他方法:', error);
              }
              self.testCameraAccess().then(function() {
                if (!self.isDestroyed) {
                  self.isCheckingPermission = false;
                }
                resolve();
              });
            });
          } else {
            // 如果不支持permissions API，尝试直接访问摄像头来检测权限
            self.testCameraAccess().then(function() {
              if (!self.isDestroyed) {
                self.isCheckingPermission = false;
              }
              resolve();
            });
          }
        } catch (error) {
          console.error('权限检查失败:', error);
          self.isCheckingPermission = false;
          resolve();
        }
      });
    },

    // 测试摄像头访问权限
    testCameraAccess: function() {
      var self = this;
      return new Promise(function(resolve) {
        try {
          navigator.mediaDevices.getUserMedia({ 
            video: { 
              width: { min: 1 }, 
              height: { min: 1 } 
            } 
          }).then(function(stream) {
            // 获得权限后立即停止摄像头
            stream.getTracks().forEach(function(track) {
              track.stop();
            });
            if (!self.isDestroyed) {
              self.hasPermission = true;
            }
            resolve();
          }).catch(function(error) {
            if (!self.isDestroyed) {
              console.log('摄像头访问被拒绝:', error);
              self.hasPermission = false;
            }
            resolve();
          });
        } catch (error) {
          if (!self.isDestroyed) {
            console.error('测试摄像头访问失败:', error);
            self.hasPermission = false;
          }
          resolve();
        }
      });
    },

    // 设置权限监控
    setupPermissionMonitoring: function() {
      var self = this;
      
      // 防止重复设置
      if (this.hasSetupPermissionMonitoring) {
        return;
      }
      
      try {
        // 如果支持permissions API，监听权限变化
        if (navigator.permissions && navigator.permissions.query) {
          navigator.permissions.query({ name: 'camera' }).then(function(result) {
            self.permissionStatus = result;
            self.hasPermission = result.state === 'granted';
            
            // 监听权限变化
            self.onPermissionChange = function() {
              if (self.isDestroyed) {
                return;
              }
              
              console.log('摄像头权限状态变化:', result.state);
              var newHasPermission = result.state === 'granted';
              
              // 只有权限状态真正变化时才处理
              if (self.hasPermission !== newHasPermission) {
                self.hasPermission = newHasPermission;
                
                if (result.state === 'granted') {
                  // 权限被授予时，确保基础功能可用（但还需要检查QR码库是否加载）
                  if (self.Html5Qrcode) {
                    self.isSupported = true;
                  }
                  Toast.success('摄像头权限已授权，可以使用扫码功能了');
                } else if (result.state === 'denied') {
                  Toast.fail('摄像头权限被拒绝，扫码功能不可用');
                }
              }
            };
            
            result.addEventListener('change', self.onPermissionChange);
            self.hasSetupPermissionMonitoring = true;
          }).catch(function(error) {
            console.log('权限监听设置失败:', error);
          });
        }
      } catch (error) {
        console.error('设置权限监控失败:', error);
      }
    },

    // 设置页面可见性变化监听
    setupVisibilityChangeListener: function() {
      var self = this;
      
      // 防止重复设置
      if (this.hasSetupVisibilityListener) {
        return;
      }
      
      try {
        // 创建监听器函数并保存引用
        this.visibilityChangeHandler = function() {
          if (!document.hidden) {
            // 页面变为可见时，重新检查权限状态
            setTimeout(function() {
              if (self.checkCameraPermission && !self.isDestroyed) {
                self.checkCameraPermission();
              }
            }, 500);
          }
        };

        this.windowFocusHandler = function() {
          setTimeout(function() {
            if (self.checkCameraPermission && !self.isDestroyed) {
              self.checkCameraPermission();
            }
          }, 500);
        };

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', this.visibilityChangeHandler);

        // 监听窗口焦点变化
        window.addEventListener('focus', this.windowFocusHandler);
        
        this.hasSetupVisibilityListener = true;
      } catch (error) {
        console.error('设置页面监听失败:', error);
      }
    },

    // 动态加载QR码库
    loadQrCodeLibrary: function() {
      var self = this;
      try {
        // 假设基础环境支持，先设置为true，避免按钮被禁用
        this.isSupported = true;
        
        // 使用require或import动态加载
        Promise.resolve().then(function() {
          return import('html5-qrcode');
        }).then(function(module) {
          if (!self.isDestroyed) {
            self.Html5Qrcode = module.Html5Qrcode;
            self.isSupported = true;
          }
        }).catch(function(error) {
          if (!self.isDestroyed) {
            console.error('QR码库加载失败:', error);
            self.isSupported = false;
          }
        });
      } catch (error) {
        console.error('动态加载失败:', error);
        this.isSupported = false;
      }
    },

    // 开始扫描
    startScan: function() {
      var self = this;
      
      console.log('startScan 被调用，当前状态:', {
        isSupported: this.isSupported,
        hasPermission: this.hasPermission,
        Html5Qrcode: !!this.Html5Qrcode,
        scanning: this.scanning
      });
      
      if (!this.isSupported) {
        this.showUnsupportedDialog = true;
        return;
      }

      try {
        // 检查是否支持摄像头
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          Toast.fail('您的设备不支持摄像头功能');
          return;
        }

        // 检查QR码库是否已加载
        if (!this.Html5Qrcode) {
          Toast.fail('扫码功能正在加载中，请稍后再试');
          return;
        }

        // 在开始扫描前重新检查权限状态
        this.checkCameraPermission().then(function() {
          if (!self.hasPermission) {
            // 如果没有权限，尝试请求权限
            return self.requestCameraPermission();
          }
          return Promise.resolve();
        }).then(function() {
          if (!self.hasPermission) {
            Toast.fail('请在手机设置中授权摄像头权限后重试');
            return Promise.reject(new Error('权限被拒绝'));
          }

          self.showScanner = true;
          self.scanning = true;

          // 获取摄像头列表并启动扫码
          return self.getCameras().then(function() {
            return self.startScanning();
          });
        }).catch(function(error) {
          console.error('启动扫描失败:', error);
          
          // 确保重置扫描状态
          self.scanning = false;
          self.showScanner = false;
          
          if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            Toast.fail('摄像头权限被拒绝，请在设置中开启权限后重试');
          } else if (error.name === 'NotFoundError') {
            Toast.fail('未找到摄像头设备');
          } else if (error.message === '权限被拒绝') {
            // 已经显示过提示，不再重复显示
          } else {
            Toast.fail('启动摄像头失败，请检查权限设置');
          }
          
          // 确保停止扫描
          self.stopScan().catch(function(stopError) {
            console.error('停止扫描失败:', stopError);
          });
        });
        
      } catch (error) {
        console.error('启动扫描失败:', error);
        Toast.fail('启动摄像头失败，请检查权限设置');
        this.stopScan();
      }
    },

    // 请求摄像头权限
    requestCameraPermission: function() {
      var self = this;
      return new Promise(function(resolve, reject) {
        try {
          navigator.mediaDevices.getUserMedia({ 
            video: { 
              facingMode: 'environment',  // 优先使用后置摄像头
              width: { ideal: 1280 },
              height: { ideal: 720 }
            } 
          }).then(function(stream) {
            // 获得权限后立即停止摄像头
            stream.getTracks().forEach(function(track) {
              track.stop();
            });
            self.hasPermission = true;
            Toast.success('摄像头权限获取成功');
            resolve();
          }).catch(function(error) {
            console.error('请求摄像头权限失败:', error);
            self.hasPermission = false;
            reject(error);
          });
        } catch (error) {
          console.error('请求摄像头权限异常:', error);
          self.hasPermission = false;
          reject(error);
        }
      });
    },

    // 获取摄像头列表
    getCameras: function() {
      var self = this;
      return new Promise(function(resolve, reject) {
        try {
          if (!self.Html5Qrcode) {
            reject(new Error('QR码库未加载'));
            return;
          }

          self.Html5Qrcode.getCameras().then(function(devices) {
            self.cameras = devices;
            self.hasMultipleCamera = devices.length > 1;
            
            if (devices.length > 0) {
              // 优先使用后置摄像头
              var backCamera = devices.find(function(device) {
                return device.label.toLowerCase().includes('back') || 
                       device.label.toLowerCase().includes('rear');
              });
              self.currentCameraId = backCamera ? backCamera.id : devices[0].id;
            }
            resolve();
          }).catch(function(error) {
            console.error('获取摄像头列表失败:', error);
            reject(error);
          });
        } catch (error) {
          console.error('获取摄像头列表失败:', error);
          reject(error);
        }
      });
    },

    // 开始扫描
    startScanning: function() {
      var self = this;
      return new Promise(function(resolve, reject) {
        try {
          if (!self.Html5Qrcode) {
            reject(new Error('QR码库未加载'));
            return;
          }

          self.html5QrCode = new self.Html5Qrcode("qr-reader");
          
          var config = {
            fps: 10,
            qrbox: {
              width: 250,
              height: 250
            },
            aspectRatio: 1.0,
            showTorchButtonIfSupported: true,
            formatsToSupport: self.formats
          };

          self.html5QrCode.start(
            self.currentCameraId,
            config,
            function(decodedText, decodedResult) {
              self.onScanSuccess(decodedText, decodedResult);
            },
            function(error) {
              self.onScanFailure(error);
            }
          ).then(function() {
            resolve();
          }).catch(function(error) {
            console.error('启动扫描失败:', error);
            reject(error);
          });
        } catch (error) {
          console.error('启动扫描失败:', error);
          reject(error);
        }
      });
    },

    // 扫描成功回调
    onScanSuccess: function(decodedText, decodedResult) {
      console.log('扫码成功:', decodedText);
      
      // 触发扫码成功事件
      this.$emit('scan-success', {
        text: decodedText,
        result: decodedResult
      });

      // 显示成功提示
      Toast.success('扫码成功');

      // 自动关闭扫描器
      if (this.autoClose) {
        this.stopScan();
      }
    },

    // 扫描失败回调
    onScanFailure: function(error) {
      // 扫描失败是正常的，不需要处理
      // console.log('扫描中...', error);
    },

    // 停止扫描
    stopScan: function() {
      var self = this;
      return new Promise(function(resolve) {
        try {
          if (self.html5QrCode) {
            self.html5QrCode.stop().then(function() {
              if (self.html5QrCode && self.html5QrCode.clear) {
                self.html5QrCode.clear();
              }
              self.html5QrCode = null;
              resolve();
            }).catch(function(error) {
              console.error('停止扫描失败:', error);
              resolve();
            });
          } else {
            resolve();
          }
        } catch (error) {
          console.error('停止扫描失败:', error);
          resolve();
        }
        
        self.showScanner = false;
        self.scanning = false;
      });
    },

    // 切换摄像头
    toggleCamera: function() {
      var self = this;
      
      if (this.cameras.length <= 1) return;
      
      // 确保扫描器是开启状态才允许切换
      if (!this.showScanner) {
        Toast.fail('请先打开扫描器');
        return;
      }
      
      this.stopScan().then(function() {
        // 检查扫描器是否仍然打开（用户可能在切换过程中关闭了）
        if (!self.showScanner) {
          return;
        }
        
        // 切换到下一个摄像头
        var currentIndex = self.cameras.findIndex(function(camera) {
          return camera.id === self.currentCameraId;
        });
        var nextIndex = (currentIndex + 1) % self.cameras.length;
        self.currentCameraId = self.cameras[nextIndex].id;
        
        // 重新开始扫描
        self.scanning = true;
        self.showScanner = true;
        return self.startScanning();
      }).catch(function(error) {
        console.error('切换摄像头失败:', error);
        Toast.fail('切换摄像头失败');
        self.scanning = false;
      });
    },

    // 清理资源和监听器
    cleanup: function() {
      try {
        // 移除权限状态监听器
        if (this.permissionStatus && this.permissionStatus.removeEventListener && this.onPermissionChange) {
          this.permissionStatus.removeEventListener('change', this.onPermissionChange);
        }
        
        // 移除页面可见性监听器
        if (this.visibilityChangeHandler) {
          document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
          this.visibilityChangeHandler = null;
        }
        
        // 移除窗口焦点监听器
        if (this.windowFocusHandler) {
          window.removeEventListener('focus', this.windowFocusHandler);
          this.windowFocusHandler = null;
        }
        
        // 重置状态
        this.permissionStatus = null;
        this.hasPermission = false;
        this.isCheckingPermission = false;
        this.hasSetupPermissionMonitoring = false;
        this.hasSetupVisibilityListener = false;
        this.onPermissionChange = null;
      } catch (error) {
        console.error('清理资源失败:', error);
      }
    }
  },

  beforeDestroy: function() {
    this.isDestroyed = true;
    this.stopScan();
    this.cleanup();
  }
};
</script>

<style scoped>
.qr-scanner {
  display: inline-block;
}

/* 扫码按钮样式 */
.scan-button {
  background-color: #1989fa !important;
  border-color: #1989fa !important;
  width: 40px !important;
  height: 32px !important;
  min-width: 40px !important;
  padding: 0 !important;
}

.scan-button:active {
  background-color: #0570d9 !important;
  border-color: #0570d9 !important;
}

.scanner-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
}

.scanner-header {
  padding: 20px;
  text-align: center;
  background: #1989fa;
}

.scanner-header h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #fff;
}

.scanner-header p {
  margin: 0;
  font-size: 14px;
  color: #e8f4ff;
}

.camera-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

#qr-reader {
  width: 100%;
  height: 100%;
}

.scan-line {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 2px;
  background: #1989fa;
  animation: scan 2s infinite;
  box-shadow: 0 0 10px #1989fa;
}

@keyframes scan {
  0% {
    transform: translate(-50%, -50%) translateY(-125px);
  }
  50% {
    transform: translate(-50%, -50%) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateY(125px);
  }
}

.scanner-actions {
  padding: 20px;
  display: flex;
  gap: 15px;
  background: #f7f8fa;
}

.scanner-actions .van-button {
  flex: 1;
}

.scanner-actions .van-button--primary {
  background-color: #1989fa;
  border-color: #1989fa;
}

.scanner-actions .van-button--default {
  background-color: #fff;
  border-color: #dcdee0;
  color: #646566;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .scanner-header,
  .scanner-actions {
    background: #1a1a1a;
  }
  
  .scanner-header h3 {
    color: #fff;
  }
  
  .scanner-header p {
    color: #ccc;
  }
}
</style> 