<template>
  <div class="qr-scanner">
    <!-- 扫码按钮 -->
    <van-button 
      type="primary" 
      icon="scan" 
      size="small"
      @click="startScan"
      :loading="scanning"
      class="scan-button"
      :disabled="!isSupported"
    ></van-button>

    <!-- 不支持扫码时显示的提示 -->
    <van-dialog
      v-model="showUnsupportedDialog"
      title="提示"
      message="您的设备不支持扫码功能，请手动输入"
      show-cancel-button
      @confirm="showUnsupportedDialog = false"
      @cancel="showUnsupportedDialog = false"
    />

    <!-- 扫码弹窗 -->
    <van-popup 
      v-model="showScanner" 
      position="center" 
      :style="{ width: '90%', height: '70%' }"
      closeable
      @close="stopScan"
    >
      <div class="scanner-container">
        <div class="scanner-header">
          <h3>扫描二维码/条形码</h3>
          <p>请将二维码/条形码放入扫描框内</p>
        </div>
        
        <!-- 摄像头视频区域 -->
        <div class="camera-container">
          <div id="qr-reader" ref="qrReader"></div>
          <div class="scan-line" v-if="scanning"></div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="scanner-actions">
          <van-button @click="stopScan" type="default">取消</van-button>
          <van-button @click="toggleCamera" type="primary" v-if="hasMultipleCamera">
            切换摄像头
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Dialog, Toast } from 'vant';

export default {
  name: 'QRScanner',
  props: {
    // 扫码成功后是否自动关闭扫描器
    autoClose: {
      type: Boolean,
      default: true
    },
    // 支持的扫码格式
    formats: {
      type: Array,
      default: function() {
        return [
          'QR_CODE',
          'CODE_128',
          'CODE_39',
          'EAN_13',
          'EAN_8',
          'UPC_A',
          'UPC_E'
        ];
      }
    }
  },
  data: function() {
    return {
      showScanner: false,
      scanning: false,
      html5QrCode: null,
      cameras: [],
      currentCameraId: null,
      hasMultipleCamera: false,
      isSupported: false,
      showUnsupportedDialog: false,
      Html5Qrcode: null
    };
  },
  mounted: function() {
    this.checkSupport();
  },
  methods: {
    // 检查设备兼容性
    checkSupport: function() {
      try {
        // 检查基础API支持
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          this.isSupported = false;
          return;
        }

        // 检查Promise支持
        if (typeof Promise === 'undefined') {
          this.isSupported = false;
          return;
        }

        // 尝试动态加载html5-qrcode库
        this.loadQrCodeLibrary();
      } catch (error) {
        console.error('兼容性检查失败:', error);
        this.isSupported = false;
      }
    },

    // 动态加载QR码库
    loadQrCodeLibrary: function() {
      var self = this;
      try {
        // 使用require或import动态加载
        Promise.resolve().then(function() {
          return import('html5-qrcode');
        }).then(function(module) {
          self.Html5Qrcode = module.Html5Qrcode;
          self.isSupported = true;
        }).catch(function(error) {
          console.error('QR码库加载失败:', error);
          self.isSupported = false;
        });
      } catch (error) {
        console.error('动态加载失败:', error);
        this.isSupported = false;
      }
    },

    // 开始扫描
    startScan: function() {
      var self = this;
      
      if (!this.isSupported) {
        this.showUnsupportedDialog = true;
        return;
      }

      try {
        // 检查是否支持摄像头
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          Toast.fail('您的设备不支持摄像头功能');
          return;
        }

        this.showScanner = true;
        this.scanning = true;

        // 获取摄像头列表并启动扫码
        this.getCameras().then(function() {
          return self.startScanning();
        }).catch(function(error) {
          console.error('启动扫描失败:', error);
          Toast.fail('启动摄像头失败，请检查权限设置');
          self.stopScan();
        });
        
      } catch (error) {
        console.error('启动扫描失败:', error);
        Toast.fail('启动摄像头失败，请检查权限设置');
        this.stopScan();
      }
    },

    // 获取摄像头列表
    getCameras: function() {
      var self = this;
      return new Promise(function(resolve, reject) {
        try {
          if (!self.Html5Qrcode) {
            reject(new Error('QR码库未加载'));
            return;
          }

          self.Html5Qrcode.getCameras().then(function(devices) {
            self.cameras = devices;
            self.hasMultipleCamera = devices.length > 1;
            
            if (devices.length > 0) {
              // 优先使用后置摄像头
              var backCamera = devices.find(function(device) {
                return device.label.toLowerCase().includes('back') || 
                       device.label.toLowerCase().includes('rear');
              });
              self.currentCameraId = backCamera ? backCamera.id : devices[0].id;
            }
            resolve();
          }).catch(function(error) {
            console.error('获取摄像头列表失败:', error);
            reject(error);
          });
        } catch (error) {
          console.error('获取摄像头列表失败:', error);
          reject(error);
        }
      });
    },

    // 开始扫描
    startScanning: function() {
      var self = this;
      return new Promise(function(resolve, reject) {
        try {
          if (!self.Html5Qrcode) {
            reject(new Error('QR码库未加载'));
            return;
          }

          self.html5QrCode = new self.Html5Qrcode("qr-reader");
          
          var config = {
            fps: 10,
            qrbox: {
              width: 250,
              height: 250
            },
            aspectRatio: 1.0,
            showTorchButtonIfSupported: true,
            formatsToSupport: self.formats
          };

          self.html5QrCode.start(
            self.currentCameraId,
            config,
            function(decodedText, decodedResult) {
              self.onScanSuccess(decodedText, decodedResult);
            },
            function(error) {
              self.onScanFailure(error);
            }
          ).then(function() {
            resolve();
          }).catch(function(error) {
            console.error('启动扫描失败:', error);
            reject(error);
          });
        } catch (error) {
          console.error('启动扫描失败:', error);
          reject(error);
        }
      });
    },

    // 扫描成功回调
    onScanSuccess: function(decodedText, decodedResult) {
      console.log('扫码成功:', decodedText);
      
      // 触发扫码成功事件
      this.$emit('scan-success', {
        text: decodedText,
        result: decodedResult
      });

      // 显示成功提示
      Toast.success('扫码成功');

      // 自动关闭扫描器
      if (this.autoClose) {
        this.stopScan();
      }
    },

    // 扫描失败回调
    onScanFailure: function(error) {
      // 扫描失败是正常的，不需要处理
      // console.log('扫描中...', error);
    },

    // 停止扫描
    stopScan: function() {
      var self = this;
      return new Promise(function(resolve) {
        try {
          if (self.html5QrCode) {
            self.html5QrCode.stop().then(function() {
              if (self.html5QrCode && self.html5QrCode.clear) {
                self.html5QrCode.clear();
              }
              self.html5QrCode = null;
              resolve();
            }).catch(function(error) {
              console.error('停止扫描失败:', error);
              resolve();
            });
          } else {
            resolve();
          }
        } catch (error) {
          console.error('停止扫描失败:', error);
          resolve();
        }
        
        self.showScanner = false;
        self.scanning = false;
      });
    },

    // 切换摄像头
    toggleCamera: function() {
      var self = this;
      
      if (this.cameras.length <= 1) return;
      
      this.stopScan().then(function() {
        // 切换到下一个摄像头
        var currentIndex = self.cameras.findIndex(function(camera) {
          return camera.id === self.currentCameraId;
        });
        var nextIndex = (currentIndex + 1) % self.cameras.length;
        self.currentCameraId = self.cameras[nextIndex].id;
        
        // 重新开始扫描
        self.scanning = true;
        return self.startScanning();
      }).catch(function(error) {
        console.error('切换摄像头失败:', error);
        Toast.fail('切换摄像头失败');
      });
    }
  },

  beforeDestroy: function() {
    this.stopScan();
  }
};
</script>

<style scoped>
.qr-scanner {
  display: inline-block;
}

/* 扫码按钮样式 */
.scan-button {
  background-color: #1989fa !important;
  border-color: #1989fa !important;
  width: 40px !important;
  height: 32px !important;
  min-width: 40px !important;
  padding: 0 !important;
}

.scan-button:active {
  background-color: #0570d9 !important;
  border-color: #0570d9 !important;
}

.scanner-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
}

.scanner-header {
  padding: 20px;
  text-align: center;
  background: #1989fa;
}

.scanner-header h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #fff;
}

.scanner-header p {
  margin: 0;
  font-size: 14px;
  color: #e8f4ff;
}

.camera-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

#qr-reader {
  width: 100%;
  height: 100%;
}

.scan-line {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 2px;
  background: #1989fa;
  animation: scan 2s infinite;
  box-shadow: 0 0 10px #1989fa;
}

@keyframes scan {
  0% {
    transform: translate(-50%, -50%) translateY(-125px);
  }
  50% {
    transform: translate(-50%, -50%) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateY(125px);
  }
}

.scanner-actions {
  padding: 20px;
  display: flex;
  gap: 15px;
  background: #f7f8fa;
}

.scanner-actions .van-button {
  flex: 1;
}

.scanner-actions .van-button--primary {
  background-color: #1989fa;
  border-color: #1989fa;
}

.scanner-actions .van-button--default {
  background-color: #fff;
  border-color: #dcdee0;
  color: #646566;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .scanner-header,
  .scanner-actions {
    background: #1a1a1a;
  }
  
  .scanner-header h3 {
    color: #fff;
  }
  
  .scanner-header p {
    color: #ccc;
  }
}
</style> 