<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库捆包清单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="" style="background: white">
      <van-form>
        <van-field v-model="storeNumber" label="捆包" ref="baleRef" class="all-font-size" clearable
          placeholder="请输入或扫描捆包号" @keyup.enter.native="getBaleByPackId" :required="true" enterkeyhint="enter" />
        <van-field label="车牌号" placeholder="车牌号" class="all-font-size" v-model="carNumber" readonly />

        <div class="detail_row">
          <div class="fourtext">已/未扫</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="scanned" readonly />
            <input class="weight_input" type="text" v-model="notScanned" readonly />
          </div>
        </div>
        <van-field label="规格" placeholder="规格" class="all-font-size" v-model="specsDesc" readonly />
        <div class="detail_row">
          <div class="fourtext">重/件</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" :value="formatNumber(allNetWeight)" readonly />
            <input class="weight_input" type="text" :value="allPieceNum" readonly />
          </div>
        </div>
        <div class="detail_row">
          <div class="fourtext">总重/件</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" :value="formatNumber(remainingNetWeight)" readonly />
            <input class="weight_input" type="text" :value="remainingPieceNum" readonly />
          </div>
        </div>
        <div class="detail_row">
          <div class="fourtext">总捆包</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="allPackQty" readonly />
          </div>
        </div>
        <van-field v-model="storeName" class="all-font-size" label="仓库" readonly />
        <van-field v-show="remark" label="备注" type="textarea" placeholder="备注" class="all-font-size" v-model="remark"
          autosize readonly />
      </van-form>
    </div>

    <van-tabs v-model="active" color="#007aff" style="width: 100%; background: white" line-width="60px"
      offset-top="44px" title-active-color="#007aff" sticky>
      <van-tab title="已扫捆包">
        <div v-if="packList && packList.length > 0">
          <div class="inlist-content">
            <div class="detail_textarea">
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  已扫捆包合计 :
                  <span class="span-count">{{ packList.length }}</span>
                </div>
              </div>
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  总件数 :
                  <span class="span-count">{{ allPieceNum }}</span>
                  总吨数 :
                  <span class="span-count">{{
                    formatNumber(allNetWeight)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="content-cell">
              <van-swipe-cell v-for="(item, index) in packList" :key="index">
                <div style="text-align: left">
                  <div class="detail_textarea">
                    <div class="detail_text">
                      <div class="fourtext3">{{ item.packId }}</div>
                    </div>
                    <div class="border_top">
                      <div>
                        <div>
                          <span class="check-spec">母捆包：</span>
                          <span class="check-val">{{ item.mPackId }}</span>
                        </div>
                        <div v-if="item.locationId">
                          <span class="check-spec">库位：</span>
                          <span class="check-val">{{ item.locationId }}/{{ item.locationName }}</span>
                        </div>
                        <div>
                          <span class="check-spec">标签：</span>
                          <span class="check-val">{{ item.labelId }}</span>
                        </div>
                        <div v-if="segNo !== 'KF000000'">
                          <span class="check-spec">是否已配单：</span>
                          <span class="check-val" :style="item.isAlready == '未配单' ? 'color: red;' : ''">{{
                            item.isAlready
                          }}</span>
                        </div>
                        <div>
                          <span class="check-spec">规：</span>
                          <span class="check-val">{{ item.specsDesc }}</span>
                        </div>
                        <div>
                          <span class="check-spec">重/件：</span>
                          <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum }}</span>
                        </div>
                        <div>
                          <span class="check-spec">客：</span>
                          <span class="check-val">{{
                            item.settleUserName
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <template #right>
                  <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                    <span class="swiper-text">删除</span>
                  </button>
                </template>
              </van-swipe-cell>
            </div>
          </div>
        </div>
        <div>
          <div class="mui-input-row" v-show="isShowBottom">
            <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm" v-if="packList.length > 0">
              出&nbsp; &nbsp; &nbsp;&nbsp; 库
            </button>


          </div>
        </div>
      </van-tab>

      <van-tab title="未扫捆包">
        <div v-for="(group, specsDesc) in groupedNoScanPackList" :key="specsDesc" class="specs-group">
          <!-- 粘性标题 -->
          <van-sticky :offset-top="88" z-index="99">
            <div class="collapse-title-sticky" @click="toggleGroup(specsDesc)">
              <span class="specs-title">{{ specsDesc }}</span>
              <div class="title-right">
                <span class="pack-count">{{ group.length }}个捆包</span>
                <van-icon :name="expandedGroups[specsDesc] ? 'arrow-up' : 'arrow-down'" class="expand-icon" />
              </div>
            </div>
          </van-sticky>

          <!-- 折叠内容 -->
          <div v-show="expandedGroups[specsDesc]" class="group-content">
            <van-swipe-cell v-for="(item, index) in group" :key="index">
              <div style="text-align: left">
                <div class="detail_textarea">
                  <div class="detail_text">
                    <div class="fourtext3">{{ item.packId }}</div>
                  </div>
                  <div class="border_top">
                    <div>
                      <div>
                        <span class="check-spec">母捆包：</span>
                        <span class="check-val">{{ item.mPackId }}</span>
                      </div>
                      <div v-if="item.locationId">
                        <span class="check-spec">库位：</span>
                        <span class="check-val">{{ item.locationId }}/{{ item.locationName }}</span>
                      </div>
                      <div>
                        <span class="check-spec">标签：</span>
                        <span class="check-val">{{ item.labelId }}</span>
                      </div>
                      <div v-if="segNo !== 'KF000000'">
                        <span class="check-spec">是否已配单：</span>
                        <span class="check-val" :style="item.isAlready == '未配单' ? 'color: red;' : ''">{{ item.isAlready
                        }}</span>
                      </div>
                      <div>
                        <span class="check-spec">规：</span>
                        <span class="check-val">{{ item.specsDesc }}</span>
                      </div>
                      <div>
                        <span class="check-spec">重/件：</span>
                        <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum }}</span>
                      </div>
                      <div>
                        <span class="check-spec">客：</span>
                        <span class="check-val">{{ item.settleUserName }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </van-swipe-cell>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { post } from "../../api/base-service";
import { Dialog } from "vant";
export default {
  data() {
    return {
      packList: [], //已扫捆包
      noScanPackList: [], //未扫捆包
      storeNumber: "", //捆包号
      carNumber: "", //车牌号
      baleList: {}, //捆包信息
      baleListNetWeight: 0, //捆包重量
      baleListPieceNum: 0, //捆包件数
      specsDesc: "", //规格
      storeName: localStorage.getItem("warehouseName"), //仓库名称
      remark: "", //备注
      active: 2,
      isShowBottom: true, //显示或者隐藏footer
      signShow: false,
      billLading: [], //提货单
      carTraceNo: "", //车辆跟踪号
      handPointId: "", //装卸点id
      rowList: [], //出库参数
      allocateVehicleNo: '',
      factoryBuilding: localStorage.getItem('factoryBuilding'),
      aleradyList: [],// 已配单捆包
      currentHandPointId: '',
      loadId: '',
      totalNetWeight: 0, // 总重量
      totalPieceNum: 0, // 总件数
      totalPackQty: 0, // 总捆包数
      expandedGroups: {}, // 记录每个规格组的展开状态
      segNo: '', // 段号

      // 新增：缓存相关
      cacheKey: '', // 当前的缓存键
      lastCacheKey: '', // 上次的缓存键，用于检测变化
    };
  },
  computed: {
    scanned() {
      //已扫捆包数量
      return this.packList.length;
    },
    notScanned() {
      //未扫捆包数量
      return this.noScanPackList.length;
    },
    allPackQty() {
      //总捆包
      return this.totalPackQty;
    },
    allNetWeight() {
      //合计重量
      return this.packList.reduce((accumulator, currentValue) => {
        return (accumulator * 1000 + currentValue.netWeight * 1000) / 1000;
      }, 0);
    },
    allPieceNum() {
      //合计件数
      return this.packList.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.pieceNum;
      }, 0);
    },
    // 剩余重量 = 总重量 - 已扫描重量
    remainingNetWeight() {
      return this.totalNetWeight - this.allNetWeight;
    },
    // 剩余件数 = 总件数 - 已扫描件数  
    remainingPieceNum() {
      return this.totalPieceNum - this.allPieceNum;
    },
    // 按规格分组未扫捆包
    groupedNoScanPackList() {
      const groups = {};
      this.noScanPackList.forEach(item => {
        const specs = item.specsDesc || '未知规格';
        if (!groups[specs]) {
          groups[specs] = [];
        }
        groups[specs].push(item);
      });
      
      // 对每个规格组内的捆包进行排序
      Object.keys(groups).forEach(specs => {
        groups[specs].sort((a, b) => {
          // 1. 首先按配单状态排序：已配单在前，未配单在后
          if (a.isAlready !== b.isAlready) {
            if (a.isAlready === '已配单') return -1;
            if (b.isAlready === '已配单') return 1;
          }
          
          // 2. 在相同配单状态内，按库位排序
          return this.compareLocation(a.locationId, b.locationId);
        });
      });
      
      return groups;
    },
  },

  async created() {
    this.billLading = [];
    this.carNumber = localStorage.getItem('carNumber');
    this.carTraceNo = localStorage.getItem('carTraceNo');
    this.handPointId = localStorage.getItem('handPointId');
    this.billLading = localStorage.getItem('billLading').split(',');
    this.allocateVehicleNo = localStorage.getItem('allocateVehicleNo');
    this.currentHandPointId = localStorage.getItem('currentHandPointId');
    this.loadId = localStorage.getItem('loadId');
    this.segNo = localStorage.getItem('segNo');

    // 生成当前缓存键
    this.generateCacheKey();

    // 页面初始化时清理不匹配的缓存
    this.clearMismatchedCaches();

    // 检查并恢复缓存数据
    this.restoreCacheData();

    await this.openCarAction();
    this.$nextTick((x) => {
      this.$refs.baleRef.focus();
    });
  },

  methods: {
    formatNumber: (val) => Math.round(val * 1000) / 1000,

    /**
     * 比较库位排序
     * 先按字母排序，再按数字排序
     */
    compareLocation(locationA, locationB) {
      // 处理空值情况
      if (!locationA && !locationB) return 0;
      if (!locationA) return 1;
      if (!locationB) return -1;
      
      // 解析库位格式，例如："A01" -> {letter: "A", number: 1}
      const parseLocation = (location) => {
        const match = location.match(/^([A-Za-z]+)(\d+)$/);
        if (match) {
          return {
            letter: match[1].toUpperCase(),
            number: parseInt(match[2], 10)
          };
        }
        // 如果格式不匹配，直接返回原字符串用于字符串比较
        return { letter: location, number: 0 };
      };
      
      const locA = parseLocation(locationA);
      const locB = parseLocation(locationB);
      
      // 首先按字母排序
      if (locA.letter !== locB.letter) {
        return locA.letter.localeCompare(locB.letter);
      }
      
      // 字母相同时按数字排序
      return locA.number - locB.number;
    },

    /**
     * 生成缓存键
     */
    generateCacheKey() {
      // 记录旧的缓存键用于比较
      const oldCacheKey = this.cacheKey;

      // 基于车辆跟踪号生成唯一缓存键
      this.cacheKey = `bundle_cache_${this.carTraceNo}`;

      // 如果缓存键发生了变化，说明车辆跟踪号发生了变化
      if (oldCacheKey && oldCacheKey !== this.cacheKey) {
        // 清除旧的缓存
        this.clearCacheData(oldCacheKey);

        // 额外清理：清除其他车辆跟踪号的缓存
        this.clearOtherVehicleCaches();
      }
    },

    /**
     * 清除其他车辆跟踪号的缓存
     */
    clearOtherVehicleCaches() {
      try {
        if (!this.carTraceNo) return;

        const keys = Object.keys(localStorage);
        const currentCacheKey = this.cacheKey;

        // 找到所有其他车辆跟踪号的缓存
        const otherVehicleCaches = keys.filter(key => {
          return key.startsWith('bundle_cache_') &&
            key !== currentCacheKey;
        });

        // 清除这些缓存
        otherVehicleCaches.forEach(key => {
          localStorage.removeItem(key);
        });

        if (otherVehicleCaches.length > 0) {
          // 可以在这里添加日志，但为了保持生产环境的清洁，暂时注释
          // console.log(`清除车辆跟踪号 ${this.carTraceNo} 以外的其他缓存:`, otherVehicleCaches.length, '个');
        }

        return true;
      } catch (error) {
        console.error('清除其他车辆缓存失败:', error);
        return false;
      }
    },

    /**
     * 获取缓存数据
     */
    getCacheData() {
      try {
        const cacheData = localStorage.getItem(this.cacheKey);
        return cacheData ? JSON.parse(cacheData) : null;
      } catch (error) {
        console.error('读取缓存失败:', error);
        return null;
      }
    },

    /**
     * 保存缓存数据
     */
    saveCacheData() {
      try {
        const cacheData = {
          packList: this.packList,
          specsDesc: this.specsDesc,
          remark: this.remark,
          timestamp: Date.now(),
          // 缓存验证信息
          carTraceNo: this.carTraceNo,
        };

        localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
        return true;
      } catch (error) {
        console.error('保存缓存失败:', error);
        return false;
      }
    },

    /**
     * 清除缓存数据
     */
    clearCacheData(cacheKey = null) {
      try {
        const keyToClear = cacheKey || this.cacheKey;
        localStorage.removeItem(keyToClear);
        return true;
      } catch (error) {
        console.error('清除缓存失败:', error);
        return false;
      }
    },

    /**
     * 清除所有捆包相关缓存
     */
    clearAllBundleCache() {
      try {
        const keys = Object.keys(localStorage);
        const bundleCacheKeys = keys.filter(key => key.startsWith('bundle_cache_'));

        bundleCacheKeys.forEach(key => {
          localStorage.removeItem(key);
        });

        return true;
      } catch (error) {
        console.error('清除所有捆包缓存失败:', error);
        return false;
      }
    },

    /**
     * 检查缓存数据是否有效
     */
    isCacheValid(cacheData) {
      if (!cacheData) return false;

      // 检查关键字段是否匹配
      const isCarTraceNoMatch = cacheData.carTraceNo === this.carTraceNo;

      // 检查缓存时间（可选：24小时过期）
      const isNotExpired = (Date.now() - cacheData.timestamp) < 24 * 60 * 60 * 1000;

      return isCarTraceNoMatch && isNotExpired;
    },

    /**
     * 恢复缓存数据
     */
    restoreCacheData() {
      const cacheData = this.getCacheData();

      if (!this.isCacheValid(cacheData)) {
        // 缓存无效，清除该缓存
        if (cacheData) {
          this.clearCacheData();
        }
        return false;
      }

      try {
        // 恢复已扫描的捆包数据
        this.packList = cacheData.packList || [];
        this.specsDesc = cacheData.specsDesc || "";
        this.remark = cacheData.remark || "";

        // 如果有缓存的捆包，则切换到已扫捆包标签
        if (this.packList.length > 0) {
          this.active = 0;
        }

        return true;
      } catch (error) {
        console.error('恢复缓存失败:', error);
        this.clearCacheData();
        return false;
      }
    },

    /**
     * 检查并清理过期缓存
     */
    cleanExpiredCache() {
      try {
        const keys = Object.keys(localStorage);
        const bundleCacheKeys = keys.filter(key => key.startsWith('bundle_cache_'));
        let cleanedCount = 0;

        bundleCacheKeys.forEach(key => {
          try {
            const cacheData = JSON.parse(localStorage.getItem(key));
            // 清理超过24小时的缓存
            if (cacheData && cacheData.timestamp && (Date.now() - cacheData.timestamp) > 24 * 60 * 60 * 1000) {
              localStorage.removeItem(key);
              cleanedCount++;
            }
          } catch (error) {
            // 缓存数据格式错误，直接删除
            localStorage.removeItem(key);
            cleanedCount++;
          }
        });


      } catch (error) {
        console.error('清理过期缓存失败:', error);
      }
    },

    onClickLeft() {
      if (this.packList.length > 0 || this.noScanPackList.length > 0) {
        Dialog.confirm({
          title: "提示",
          message: "清单里有数据未提交确认退出？",
        })
          .then(() => {
            // 退出时保存缓存
            if (this.packList.length > 0) {
              this.saveCacheData();
            }
            this.$router.goBack();
          })
          .catch(() => { });
        return;
      }
      this.$router.goBack();
    },

    deleteListItem(index, item) {
      //删除数组中值
      this.$delete(this.packList, index);
      this.noScanPackList.unshift(item);
      this.storeNumber = "";

      // 如果还有其他捆包，显示最后一个捆包的规格信息，否则清空
      if (this.packList.length > 0) {
        const lastPack = this.packList[0]; // 最新扫描的捆包
        this.specsDesc = lastPack.specsDesc;
        this.remark = lastPack.remark || "";
      } else {
        this.specsDesc = "";
        this.remark = "";
      }

      // 更新缓存
      this.saveCacheData();
    },

    onConfirm() {
      if (this.noScanPackList.length && this.packList.length > 0) {
        Dialog.confirm({
          title: "提示",
          message: `还有捆包未扫描，是否继续出库?`,
        })
          .then(() => {
            this.startOutbound();
          })
          .catch(() => {
            // on cancel
          });
      } else if (this.packList.length > 0) {
        this.startOutbound();
      }
      return;
    },

    toEnd() {
      this.$router.togo({
        name: "end-load",
        params: {
          vehicleNo: this.carNumber,
          carTraceNo: this.carTraceNo,
          handPointId: this.handPointId,
        },
      });
    },

    // 查询提单下的捆包
    async openCarAction() {
      const params = {
        serviceId: "S_LI_RL_0041",
        ladingBillIdList: this.billLading,
        warehouseCode: localStorage.getItem("warehouseCode"),
      };
      const result = await post(params);
      if (!result.result) {
        Dialog.alert({
          message: "网络异常, 请稍后重试!",
        });
        return;
      }
      if (result.result.length == 0) {
        Dialog.alert({
          message: "未查询到车牌号信息",
        });
        return;
      }

      // 保存原始数据
      const originalNoScanPackList = result.result.packList;
      this.totalNetWeight = result.result.totalNetWeight;
      this.totalPieceNum = result.result.totalPieceNum;
      this.totalPackQty = result.result.totalNum;

      // 如果有缓存数据，需要从原始数据中排除已扫描的捆包
      if (this.packList.length > 0) {
        const scannedPackIds = this.packList.map(pack => pack.packId);
        this.noScanPackList = originalNoScanPackList.filter(pack => !scannedPackIds.includes(pack.packId));
      } else {
        this.noScanPackList = originalNoScanPackList;
      }

      // 查询捆包是否已配单
      await this.getPackAlreadyOrdered();

      // 清理过期缓存（在数据加载完成后进行）
      this.cleanExpiredCache();

      // if (this.factoryBuilding == "F1") {
      //   // 根据提单查询行车作业实绩
      //   await this.getCraneWorkPackList();
      // }
    },

    // 扫描捆包
    async getBaleByPackId() {
      // 检查输入的捆包号是否为空
      if (!this.storeNumber.trim()) {
        Dialog.alert({
          message: "请输入捆包号",
        });
        return;
      }

      // 检查是否已经扫描过此捆包
      const labelIdExists = this.packList.some(
        (item) => item.packId == this.storeNumber
      );

      if (labelIdExists) {
        Dialog.alert({
          title: "提示",
          message: `此捆包已添加, 请勿重复扫描`,
        });
        return;
      }

      if (!this.noScanPackList || this.noScanPackList.length == 0) {
        Dialog.alert({
          message: "此捆包不在发货计划中",
        });
        return;
      }
      const packObj = this.noScanPackList.find(ns => ns.packId == this.storeNumber);
      const params = {
        serviceId: "S_LI_RL_0042",
        warehouseCode: localStorage.getItem("warehouseCode"),
        packId: this.storeNumber,
        packIdListString: this.packList.filter(item => item.planParticle == '20' || item.planParticle == '30')
          .map(item => item.packId).join(','),
        ladingBillIdList: this.billLading,
        packList: this.packList.map(item => ({
          ladingBillId: item.voucherNum,
          packId: item.packId
        })),
        planParticle: packObj ? packObj.planParticle : '10',
      };
      const result = await post(params);
      if (!result.result) {
        return;
      }
      if (result.result.length == 0) {
        Dialog.alert({
          message: "未查询到捆包信息",
        });
        return;
      }

      let curPack = result.result[0];
      curPack['isAlready'] = this.aleradyList.find(a => a.packId == curPack.packId) ? '已配单' : '未配单';
      this.packList.unshift(curPack);

      this.specsDesc = result.result[0].specsDesc;
      this.remark = result.result[0].remark;
      this.noScanPackList = this.noScanPackList.filter(
        (item) => item.packId != curPack.packId
      );
      this.storeNumber = '';

      // 扫描成功后保存缓存
      this.saveCacheData();

    },

    // 开始出库记录
    async startOutbound() {
      let that = this;
      const perName = localStorage.getItem("perName");
      const perNo = localStorage.getItem("perNo");
      const segNo = localStorage.getItem('segNo');
      const params = {
        serviceId: segNo.includes('KF') ? "S_LI_RL_0038" : 'S_LI_RL_0152',
        warehouseCode: localStorage.getItem("warehouseCode"), //仓库编码
        warehouseName: localStorage.getItem("warehouseName"), //仓库编码名称
        vehicleNo: that.carNumber, //车牌号
        factoryBuilding: localStorage.getItem('factoryBuilding'),
        teamId: "10",
        workingShift: "10",
        userName: localStorage.getItem("userName"),
        rowList: that.packList.map((item) => ({
          ...item,
          warehouseCode: localStorage.getItem("warehouseCode"), // 仓库编码
          warehouseName: localStorage.getItem("warehouseName"), // 仓库编码名称
          carTraceNo: that.carTraceNo, //车辆跟踪号
          perName,
          perNo,
          currentHandPointId: this.currentHandPointId,
          loadId: this.loadId,
        })),
        factoryArea: localStorage.getItem('factoryArea'),
        allocateVehicleNo: localStorage.getItem('allocateVehicleNo'), // 配单号
      };
      const result = await post(params);
      if (!result || result.__sys__.status == 1) {
        // 出库成功后清除当前缓存
        this.clearCacheData();

        that.$router.togo({
          name: "end-load",
          params: {
            vehicleNo: that.carNumber,
            carTraceNo: that.carTraceNo,
            handPointId: that.handPointId,
          },
        });
      }
      return;
    },

    /**
     * 查询捆包是否已配单
     */
    async getPackAlreadyOrdered() {
      const params = {
        serviceId: 'S_LI_RL_0163',
        voucherNumList: this.billLading,
        vehicleNo: this.carNumber,
        carTraceNo: this.carTraceNo,
        allocateVehicleNo: this.allocateVehicleNo,
        "factoryArea": localStorage.getItem('factoryAreaTrue'),
        "factoryBuilding": localStorage.getItem('factoryBuilding'),
      };
      const res = await post(params);

      if (!res || !res.list) {
        return;
      }

      const aleradyList = res.list;
      this.aleradyList = aleradyList;

      // 将S_LI_RL_0163返回的list合并到未扫列表中，并去重处理
      if (res.list && res.list.length > 0) {
        // 创建一个新的捆包列表，包含S_LI_RL_0041和S_LI_RL_0163的数据
        const existingPackIds = this.noScanPackList.map(item => item.packId);
        const newPacks = res.list.filter(item => !existingPackIds.includes(item.packId));

        // 合并新的捆包数据到未扫列表
        this.noScanPackList = [...this.noScanPackList, ...newPacks];
      }

      // 为所有捆包添加配单状态
      this.noScanPackList = this.noScanPackList.map(n => {
        const aleradyPack = aleradyList.find(a => a.packId == n.packId);
        return {
          ...n,
          isAlready: aleradyPack ? '已配单' : '未配单',
        }
      });

      // 过滤掉已扫描的捆包（缓存数据）
      if (this.packList.length > 0) {
        const scannedPackIds = this.packList.map(pack => pack.packId);
        this.noScanPackList = this.noScanPackList.filter(pack => !scannedPackIds.includes(pack.packId));
      }

      if (res.listH && res.listH.length > 0) {
        const listH = res.listH;
        // 如果没有缓存数据，才使用接口返回的已扫描数据
        if (this.packList.length === 0) {
          this.packList = listH;
          this.noScanPackList = this.noScanPackList.filter(ap => !listH.find(a => a.packId == ap.packId));
          // 保存从接口恢复的数据到缓存
          if (this.packList.length > 0) {
            this.saveCacheData();
          }
        }
      }

      if (res.listYS && res.listYS.length > 0) {
        const listYS = res.listYS;

        // 找到已出库的捆包详细信息，用于计算需要从总数中减去的重量和件数
        const outboundPackages = this.noScanPackList.filter(pack => listYS.find(outboundId => outboundId == pack.packId));

        const outboundNetWeight = outboundPackages.reduce((total, pack) => total + (pack.netWeight || 0), 0);
        const outboundPieceNum = outboundPackages.reduce((total, pack) => total + (pack.pieceNum || 0), 0);
        const outboundPackCount = outboundPackages.length;

        // 从总数中减去已出库的捆包数据
        this.totalNetWeight -= outboundNetWeight;
        this.totalPieceNum -= outboundPieceNum;
        this.totalPackQty -= outboundPackCount;

        // 确保总数不会小于0
        this.totalNetWeight = Math.max(0, this.totalNetWeight);
        this.totalPieceNum = Math.max(0, this.totalPieceNum);
        this.totalPackQty = Math.max(0, this.totalPackQty);

        // 从未扫描列表中移除已出库的捆包
        this.noScanPackList = this.noScanPackList.filter(ap => !listYS.find(a => a == ap.packId));

        // 弹框提示已出库捆包过滤情况
        if (outboundPackCount > 0) {
          const outboundPackIds = outboundPackages.map(pack => pack.packId).join('、');
          Dialog.alert({
            title: "提示",
            message: `以下 ${outboundPackCount} 个捆包已出库，已自动过滤：\n${outboundPackIds}`,
          });
        }
      }
    },

    /**
     * 查询行车实绩
     */
    async getCraneWorkPackList() {
    },

    /**
     * 切换规格组的展开状态
     */
    toggleGroup(specsDesc) {
      this.$set(this.expandedGroups, specsDesc, !this.expandedGroups[specsDesc]);
    },

    /**
     * 页面初始化时清理不匹配的缓存
     */
    clearMismatchedCaches() {
      try {
        if (!this.carTraceNo) return;

        const keys = Object.keys(localStorage);
        const currentCacheKey = this.cacheKey;

        // 找到所有捆包缓存
        const bundleCacheKeys = keys.filter(key => key.startsWith('bundle_cache_'));

        bundleCacheKeys.forEach(key => {
          if (key === currentCacheKey) {
            // 当前缓存键，不删除
            return;
          }

          try {
            // 解析缓存键格式：bundle_cache_{车辆跟踪号}
            const keyParts = key.split('_');
            if (keyParts.length >= 3) {
              const cacheCarTraceNo = keyParts.slice(2).join('_'); // 车辆跟踪号可能包含下划线

              // 如果不是当前车辆跟踪号，删除缓存
              if (cacheCarTraceNo !== this.carTraceNo) {
                localStorage.removeItem(key);
              }
            }
          } catch (error) {
            // 如果解析失败，删除这个异常的缓存
            localStorage.removeItem(key);
          }
        });

        return true;
      } catch (error) {
        console.error('清理不匹配缓存失败:', error);
        return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.inlist-content {
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

/deep/ .van-dialog {
  position: fixed;
  top: 60%;
  left: 50%;
  width: 320px;
  overflow: hidden;
  font-size: 16px;
  background-color: #fff;
  border-radius: 16px;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
}

.title-add {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 25px;
}

.content-cell {
  margin-bottom: 100px;
}

.check-spec {
  font-size: 15px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 21px;
}

.check-val {
  font-size: 16px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}

.content-spec {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.div-flex {
  display: flex;
  justify-content: space-between;
}

.swiper-text {
  letter-spacing: 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
}

.swiper-btn-update {
  width: 56px;
  height: 97px;
  background: #0000ff;
  opacity: 1;
}

.swiper-btn-delete {
  width: 56px;
  height: 100%;
  background: #d33017;
  opacity: 1;
}

.collapse-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.specs-group {
  margin-bottom: 8px;
}

.collapse-title-sticky {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  padding: 12px 16px;
  border-bottom: 1px solid #ebedf0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: background-color 0.2s;
}

.collapse-title-sticky:active {
  background-color: #f5f5f5;
}

.title-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.specs-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.pack-count {
  font-size: 14px;
  color: #999999;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 12px;
}

.expand-icon {
  font-size: 16px;
  color: #666666;
  transition: transform 0.2s;
}

.group-content {
  background-color: #ffffff;
}

/* 限制van-sticky组件的宽度 */
/deep/ .van-sticky {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/deep/ .van-sticky--fixed {
  width: 100%;
  max-width: 100vw;
  left: 0 !important;
  right: 0 !important;
}
</style>
