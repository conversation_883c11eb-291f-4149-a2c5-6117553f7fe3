/**
 * @param src 图片路径
 * @param rotate 旋转角度
 * @returns {Promise<base64>}
 */
export function rotateImg(src, rotate) {
  return new Promise(((resolve, reject) => {
    let img = new Image()
    img.src = src
    img.setAttribute('crossOrigin', 'Anonymous')
    img.onload = () => {
      let canvas = document.createElement('canvas')
      let context = canvas.getContext('2d')
      if (rotate > 45 && rotate <= 135) { // 90 宽高颠倒
        canvas.width = img.height
        canvas.height = img.width
      } else if (rotate > 225 && rotate <= 315) { // 270 宽高颠倒
        canvas.width = img.height
        canvas.height = img.width
      } else {
        canvas.width = img.width
        canvas.height = img.height
      }
      context.clearRect(0, 0, canvas.width, canvas.height)
      context.translate(canvas.width / 2, canvas.height / 2)
      context.rotate(rotate * Math.PI / 180)
      context.translate(-canvas.width / 2, -canvas.height / 2)
      context.drawImage(img, canvas.width / 2 - img.width / 2, canvas.height / 2 - img.height / 2, img.width, img.height)
      context.translate(canvas.width / 2, canvas.height / 2)
      context.rotate(-rotate * Math.PI / 180)
      context.translate(-canvas.width / 2, -canvas.height / 2)
      context.restore()
      let base64 = canvas.toDataURL()
      resolve(base64)
    }
    img.onerror = reject
  }))
}