<template>
    <div class="print-inbound">
        <van-nav-bar title="入库打印" left-arrow @click-left="goBack" class="nav-bar" />

        <div class="content">
            <!-- 车辆列表 -->
            <div class="vehicle-list" v-if="vehicleList.length > 0">
                <van-radio-group v-model="selectedVehicleId">
                    <van-cell-group>
                        <van-cell v-for="(item, index) in vehicleList" :key="index" clickable
                            @click="selectVehicle(item)" class="vehicle-item">
                            <template #title>
                                <div class="vehicle-info">
                                    <van-radio :name="item.allocateVehicleNo" @click.stop class="vehicle-radio" />
                                    <div class="vehicle-details">
                                        <div class="vehicle-no">车辆号: {{ item.allocateVehicleNo }}</div>
                                        <div class="trace-no">跟踪号: {{ item.carTraceNo }}</div>
                                        <div class="factory-building" v-if="item.factoryBuilding">
                                            厂房: {{ item.factoryBuilding }}
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>

            <!-- 空状态 -->
            <div v-else-if="!loading" class="empty-state">
                <div class="empty-icon">📋</div>
                <div class="empty-text">暂无车辆信息</div>
            </div>

            <!-- 加载状态 -->
            <!-- <van-loading v-if="loading" type="spinner" color="#1989fa" class="loading">
            加载中...
        </van-loading> -->

        </div>

        <!-- 底部操作按钮 -->
        <div class="bottom-action" v-if="vehicleList.length > 0">
            <van-button type="primary" size="large" @click="handlePrint" :disabled="!selectedVehicleId"
                :loading="printing" class="print-btn">
                打印
            </van-button>
        </div>
    </div>
</template>

<script>
import { post } from "@/api/base-service";
import { Dialog } from "vant";

export default {
    name: "PrintInbound",
    data() {
        return {
            vehicleList: [],
            selectedVehicleId: '',
            loading: false,
            printing: false,
            segNo: localStorage.getItem("segNo") || "",
        };
    },
    created() {
        this.getVehicleList();
    },
    methods: {
        /**
         * 返回上一页
         */
        goBack() {
            this.$router.go(-1);
        },

        /**
         * 获取车辆信息列表
         */
        async getVehicleList() {
            this.loading = true;
            try {
                const params = {
                    serviceId: "S_LI_RL_0187",
                    segNo: this.segNo,
                };

                const res = await post(params);

                if (res.__sys__.status !== -1) {
                    this.vehicleList = res.list || [];
                } else {
                    this.$toast(res.__sys__.msg || "获取车辆信息失败");
                }
            } catch (error) {
                console.error("获取车辆信息错误:", error);
                this.$toast("获取车辆信息失败");
            } finally {
                this.loading = false;
            }
        },

        /**
         * 选择车辆
         */
        selectVehicle(vehicle) {
            this.selectedVehicleId = vehicle.allocateVehicleNo;
        },

        /**
         * 执行打印操作
         */
        async handlePrint() {
            if (!this.selectedVehicleId) {
                this.$toast("请选择要打印的车辆");
                return;
            }

            this.printing = true;

            try {
                // 找到选中的车辆对象
                const vehicle = this.vehicleList.find(item => item.allocateVehicleNo === this.selectedVehicleId);

                if (vehicle) {
                    await this.printVehicle(vehicle);
                    // 重新获取车辆列表
                    await this.getVehicleList();
                    Dialog.alert({
                        message: '打印成功',
                    });
                } else {
                    this.$toast("未找到选中的车辆信息");
                }
            } catch (error) {
                console.error("打印操作错误:", error);
                this.$toast("打印失败");
            } finally {
                this.printing = false;
            }
        },

        /**
         * 打印单个车辆
         */
        async printVehicle(vehicle) {
            const params = {
                serviceId: "S_LI_RL_0183",
                segNo: this.segNo,
                allocateVehicleNo: vehicle.allocateVehicleNo,
                carTraceNo: vehicle.carTraceNo,
                factoryBuilding: localStorage.getItem("factoryBuilding"),
            };

            const res = await post(params);

            if (res.__sys__.status === -1) {
                throw new Error(res.__sys__.msg || "打印失败");
            }

            return res.docUrlList;
        },
    },
};
</script>

<style lang="less" scoped>
.print-inbound {
    min-height: 100vh;
    background-color: #f7f8fa;

    .nav-bar {
        background-color: #1989fa;

        /deep/ .van-nav-bar__title {
            color: #fff;
        }

        /deep/ .van-nav-bar__arrow {
            color: #fff;
        }
    }

    .content {
        padding: 16px;
        min-height: calc(100vh - 46px - 70px); // 减去导航栏和底部按钮高度
    }

    .vehicle-list {
        .vehicle-item {
            margin-bottom: 12px;
            border-radius: 8px;
            overflow: hidden;

            .vehicle-info {
                display: flex;
                align-items: flex-start;

                .vehicle-radio {
                    margin-right: 12px;
                    margin-top: 4px;
                }

                .vehicle-details {
                    flex: 1;

                    .vehicle-no {
                        font-size: 16px;
                        font-weight: 600;
                        color: #323233;
                        margin-bottom: 4px;
                    }

                    .trace-no {
                        font-size: 14px;
                        color: #646566;
                        margin-bottom: 4px;
                    }

                    .factory-building {
                        font-size: 14px;
                        color: #969799;
                    }
                }
            }
        }
    }

    .loading {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .empty-text {
            font-size: 16px;
            color: #969799;
            text-align: center;
        }
    }

    .bottom-action {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px;
        background-color: #fff;
        border-top: 1px solid #ebedf0;

        .print-btn {
            width: 100%;
            background-color: #1989fa !important;
            border-color: #1989fa !important;
        }
    }
}
</style>
