<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">找料</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <van-cell
      is-link
      title="车牌号"
      size="large"
      v-model="carNumber"
      @click="openCarAction"
    />
    <van-field
      v-model="storeNumber"
      label="捆包"
      class="all-font-size"
      clearable
      placeholder="请输入或扫描提单号"
      @keyup.enter.native="getBaleByPackId"
    />
    <div class="bottom">
      <div class="title">捆包明细</div>
      <div class="foot">
        <div class="head">
          <div style="margin: 0;width: 45%;">捆包号</div>
          <div style="width: 22.5%;">库位</div>
          <div style="width: 22.5%;">IMC库位</div>
        </div>
        <div
          v-for="(item, index) in storeList"
          :key="index"
          class="foot-list"
          @click="storeSelect(item)"
        >
          <div class="cell-box">
            <div style="margin: 0;width: 50%;">{{ item.packId }}</div>
            <div style="width: 25%;">{{ item.locationId }}</div>
            <div style="width: 25%;">{{ item.imcLocationId }}</div>
          </div>
          <van-checkbox
            :name="item.packId"
            v-model="item.checked"
          ></van-checkbox>
        </div>
      </div>
    </div>
    <van-button
      type="info"
      size="large"
      class="foot-sticky-btn"
      @click="toUnload()"
      >确认</van-button
    >
    <van-dialog
      v-model="isShowPonit"
      title="选择车牌号"
      show-cancel-button
      :beforeClose="closeHandPonit"
    >
      <div style="max-height: 320px; overflow-y: auto">
        <van-radio-group v-model="carNumberRadio">
          <van-cell-group>
            <van-cell
              clickable
              v-for="(item, index) in pointList"
              :key="index"
              :title="item.vehicleNo"
              @click="carNumberRadio = item.vehicleNo"
            >
              <template #right-icon>
                <van-radio :name="item.vehicleNo" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { post } from "../../api/base-service";
import uploadIcon from "@/assets/imgs/upload-icon.png";
import { Dialog } from "vant";
export default {
  data() {
    return {
      carNumber: "",
      carNumberSelect: {},
      isShowPonit: false,
      carNumberRadio: "",
      pointList: [],
      storeNumber: "",
      storeList: [],
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    /**
     * 确定选择车牌号
     * @param action
     * @param done
     */
    async closeHandPonit(action, done) {
      if (action != "confirm") {
        done();
        return;
      }

      if (!this.carNumberRadio) {
        Dialog.alert({ message: "请选择车牌号" });
        done(false);
        return;
      }
      this.carNumber = this.carNumberRadio;
      this.carNumberSelect = this.pointList.filter((item) => {
        return item.vehicleNo == this.carNumber;
      })[0];
      await this.getBaleByPackId();
      done(true);
    },
    /**
     * 查询车牌号
     */
    async openCarAction() {
      this.isShowPonit = true;
      const params = {
        serviceId: "S_LI_RL_0085",
        segNo: localStorage.getItem("segNo"),
      };
      let result = await post(params);
      if (!result.list || result.list.length == 0) {
        this.carNumber = "";
        Dialog.alert({
          message:
            "该车辆本次未查询到装车信息，请联系业务人员检查配单信息是否完成",
        });
        return;
      }
      this.storeList = [];
      this.storeNumber = "";
      this.pointList = result.list;
      return;
    },
    // 选择捆包
    storeSelect(item) {
      item.checked = !item.checked;
    },
    // 扫描提单
    async getBaleByPackId() {
      this.storeList = [];
      const params = {
        serviceId: "S_LI_RL_0074",
        segNo: localStorage.getItem("segNo"),
        vehicleNo: this.carNumberSelect.vehicleNo || "",
        voucherNum: this.storeNumber || "",
        allocateVehicleNo: this.carNumberSelect.allocateVehicleNo || "",
      };
      // BL2412000003
      const result = await post(params);
      if (!result.list || result.list.length == 0) {
        this.carNumber = "";
        Dialog.alert({
          message:
            "该车辆本次未查询到装车信息，请联系业务人员检查是否生成配单信息",
        });
        return;
      }
      result.list.forEach((item) => {
        item.checked = false;
      });
      this.storeList = result.list;
    },
    // 确认按钮
    toUnload() {
      let list = this.storeList.filter((item) => {
        return item.checked == true;
      });
    },
  },
  mounted() {},
  watch: {},
};
</script>
<style scoped>
.bottom {
  height: calc(100% - 154px);
  background: #ffffff;
  padding: 20px 20px 0;
}
.title {
  font-size: 18px;
}
.foot {
  margin-top: 20px;
}
.head {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  /* justify-content: space-around; */
  font-size: 16px;
}
.foot-list {
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cell-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
}
.cell-box div {
  font-size: 16px;
  text-align: center;
}
.head div {
  text-align: center;
}
.foot-sticky-btn {
  width: 90%;
  position: fixed;
  bottom: 20px;
  left: 5%;
}
</style>
