<template>
  <div class="page-container">
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">车辆排序</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>

    <div class="list-container">
      <van-radio-group v-model="selectVeicleNo">
        <div v-for="(item, index) in dataList" :key="index" class="vehicle-card"
          @click="selectVeicleNo = item.vehicleNo">
          <div class="card-header">
            <div class="header-content">
              <!-- 序号行 -->
              <div class="sequence-row">
                <span class="sequence-number">#{{ index + 1 }}</span>
              </div>

              <!-- 车辆信息行 -->
              <div class="vehicle-info-row">
                <div class="vehicle-meta">
                  <span class="license">{{ item.vehicleNo }}</span>
                  <span class="status-badge" :class="getStatusClass(item.statusName)">
                    {{ item.statusName }}
                  </span>
                </div>
                <van-radio :name="item.vehicleNo" checked-color="#3B82F6" class="check-icon" style="height: 30px;" />
              </div>
            </div>
          </div>

          <div class="card-body">
            <div class="info-row">
              <div class="info-item">
                <span class="label">排队时长</span>
                <span class="value">{{ item.waitingTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">送货时间</span>
                <span class="value">{{ formatDateString(item.emergencyDeliveryTime) }}</span>
              </div>

            </div>


            <div class="info-row">
              <div class="info-item full-width">
                <span class="label">装卸点</span>
                <span class="value time">{{ item.handPointName }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-radio-group>
    </div>

    <div class="foot-sticky">
      <van-button type="info" size="large" class="foot-sticky-btn" @click="toUnload()">
        调整排队序号
      </van-button>
    </div>

    <van-popup v-model="show" round position="center" class="adjust-popup" :style="{ width: '80%' }">
      <div class="popup-content">
        <h3 class="popup-title">调整排队序号</h3>
        <van-field v-model="selectIndex" type="number" placeholder="请输入目标位置" input-align="center"
          class="number-input" />
        <div class="popup-btns">
          <van-button round block @click="cancle">取消</van-button>
          <van-button type="info" round block @click="define">确定</van-button>
        </div>
      </div>
    </van-popup>

    <van-dialog v-model="isShowPonit" title="装卸点" show-cancel-button :beforeClose="closeHandPonit">
      <div style="max-height: 320px; overflow-y: auto;">
        <van-radio-group v-model="handPonitRadio">
          <van-cell-group>
            <van-cell clickable :title="item.handPointId" :label="item.handPointName" v-for="(item, index) in pointList"
              :key="index" @click="handPonitRadio = item.handPointId">
              <template #right-icon>
                <van-radio :name="item.handPointId" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { post } from "../../api/base-service";
import { Dialog } from "vant";
export default {
  data() {
    return {
      targetHandPointId: "", // 装卸点
      isShowPonit: false,
      handPonitRadio: "",
      pointList: [],
      dataList: [],
      selectVeicleNo: "",
      show: false,
      selectIndex: "",
      sortList: [],
      curCarData: '',
    };
  },
  created() {
    this.openCarAction();
  },

  methods: {
    getStatusClass(status) {
      return {
        'queue': status === '排队',
        'calling': status === '叫号',
        'working': status === '正在作业',
        'no-plan': status === '无计划'
      }
    },
    // 转换时间格式
    formatDateString: (dateString) => dateString ? dateString.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6') : '',

    onClickLeft() {
      this.$router.goBack();
    },

    // 选择调整数据
    select(item) {
      console.log(item);
      if (this.selectVeicleNo == item.vehicleNo) {
        this.selectVeicleNo = "";
      } else {
        this.selectVeicleNo = item.vehicleNo;
      }
    },

    // 调整排队序号按钮
    toUnload() {
      if (this.selectVeicleNo == "") {
        Dialog.alert({
          message: "请选择需要调整排序的车辆",
        });
        return;
      }
      this.show = true;
    },

    // 取消
    cancle() {
      this.show = false;
      this.selectIndex = "";
    },

    // 确定
    async define() {
      if (this.selectIndex == "") {
        Dialog.alert({
          message: "请输入需要移动的位置",
        });
        return;
      }

      if (this.selectIndex >= this.dataList.length) {
        this.selectIndex = this.dataList.length;
      }
      if (this.selectIndex <= 0) {
        this.selectIndex = 1;
      }

      // 找到索引
      const indexToRemove = this.dataList.findIndex(
        (item) => item.vehicleNo == this.selectVeicleNo
      );

      if (indexToRemove < 0) {
        Dialog.alert({
          message: "未查询到车辆信息",
        });
        return;
      }

      const removedItem = this.dataList[indexToRemove];
      this.curCarData = removedItem;
      const handIds = removedItem.handPointId.split(',');
      const handNames = removedItem.handPointName.split(',');
      this.pointList = handIds.map((h, index) => ({
        handPointId: h,
        handPointName: handNames[index],
      }));
      if (handIds.length == 1) {
        await this.adjustSorting(this.pointList[0]);
        return;
      }

      this.isShowPonit = true;
    },

    /**
     * 确定选择装卸点
     * @param action
     * @param done
     */
    async closeHandPonit(action, done) {
      if (action != "confirm") {
        this.handPonitRadio = '';
        done();
        return;
      }

      if (!this.handPonitRadio && this.pointList.length > 1) {
        Dialog.alert({ message: "请选择装卸点" });
        done(false);
        return;
      }

      const currentData = this.pointList.find(
        (p) => p.handPointId == this.handPonitRadio
      );

      let isColse = await this.adjustSorting(currentData);
      done(isColse);
    },

    /**
     * 调整排序调用后台接口
     */
    async adjustSorting(handData) {
      this.curCarData = {
        ...this.curCarData,
        handPointId: handData.handPointId,
      }
      const params = {
        serviceId: "S_LI_RL_0098",
        result: this.curCarData,
        sort: this.selectIndex,
      };
      let result = await post(params);
      if (result.__sys__.status == "-1") {
        Dialog.alert({
          message: result.__sys__.msg,
        });
        return false;
      }
      await this.openCarAction();
      this.curCarData = '';
      this.selectIndex = '';
      this.selectVeicleNo = '';
      this.handPonitRadio = '';
      this.show = false;
      return true;
    },

    /**
 * 查询车牌号
 */
    async openCarAction() {
      this.dataList = [];
      const params = {
        serviceId: "S_LI_RL_0097",
        handPointId: this.handPointId,
      };
      let result = await post(params);
      this.selectVeicleNo = "";

      if (!result.list || result.list.length == 0) {
        Dialog.alert({
          message: "未查询到车辆数据",
        });
        return;
      }
      this.dataList = result.list.map(r => {
        let statusName = '';
        switch (r.status) {
          case '10':
            statusName = '排队';
            break;
          case '20':
            statusName = '叫号';
            break;
          case '40':
            statusName = '无计划';
            break;
          default:
            statusName = '正在作业';
            break;
        }
        return {
          ...r,
          // arrivalTimeStr: r.arrivalTime ? `${r.arrivalTime}时` : '',
          statusName,
        }
      });
      this.sortList = [];
      result.list.forEach((element) => {
        this.sortList.push(element.queueNumber);
      });
    },

  },
};
</script>

<style lang="less" scoped>
.page-container {
  background: #f5f7fa;
  min-height: 100vh;
}

.list-container {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  height: 75%;
  overflow-y: auto;
}

.time {
  color: #64748b;
  font-size: 12px;
}

.adjust-popup {
  // 移除顶部圆角设置
  border-radius: 16px; // 统一圆角
  overflow: hidden;

  .popup-content {
    padding: 24px;
    text-align: center;

    .popup-title {
      font-size: 18px;
      color: #1e293b;
      margin-bottom: 20px;
    }

    .number-input {
      margin: 0 auto 24px;
      width: 60%;
      background: #f8fafc;
      border-radius: 8px;

      /deep/ input {
        font-size: 16px;
        color: #3B82F6;
        padding: 12px 0;
      }
    }

    .popup-btns {
      display: flex;
      gap: 12px;
      justify-content: center;

      .van-button {
        flex: 1;
        max-width: 120px;
      }
    }
  }
}

.vehicle-card {
  background: white;
  margin: 8px 16px;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.2s;

  &:active {
    background: #f8fafc;
  }

  .card-header {
    .header-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .sequence-row {
      display: flex;
      align-items: center;

      .sequence-number {
        color: #64748b;
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0.5px;

        &::before {
          content: "NO.";
          margin-right: 4px;
          color: #94a3b8;
        }
      }
    }

    .vehicle-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .vehicle-meta {
      display: flex;
      align-items: center;
      gap: 8px;

      .license {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
      }
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      line-height: 1;

      &.queue {
        background: #f1f5f9;
        color: #64748b;
        border: 1px solid #e2e8f0;
      }

      &.calling {
        background: #eff6ff;
        color: #1d4ed8;
        border: 1px solid #dbeafe;
      }

      &.working {
        background: #f0fdf4;
        color: #166534;
        border: 1px solid #dcfce7;
      }

      &.no-plan {
        background: #fef2f2;
        color: #b91c1c;
        border: 1px solid #fecaca;
      }
    }

    /deep/ .van-radio__icon {
      margin-top: -4px; // 对齐调整
      height: 20px;
    }
  }

  .card-body {
    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 6px;

      &.full-width {
        flex: 0 0 100%;
      }

      .label {
        color: #64748b;
        font-size: 12px;
        white-space: nowrap;
      }

      .value {
        color: #1e293b;
        font-size: 13px;

        &.time {
          color: #3B82F6;
          font-weight: 500;
        }
      }

      .van-tag {
        transform: scale(0.9);
      }
    }
  }
}
</style>