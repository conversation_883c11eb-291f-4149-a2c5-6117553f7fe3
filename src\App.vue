<template>
  <div id="app">
    <transition :name="transitionName">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" class="Router" />
      </keep-alive>
    </transition>
    <transition :name="transitionName">
      <router-view v-if="!$route.meta.keepAlive" class="Router" />
    </transition>
  </div>
</template>

<script>
import axios from 'axios';
export default {
  name: "App",
  data() {
    return {
      transitionName: "slideleft",
    };
  },
  watch: {
    $route(to, from) {
      // 监听路由变化重新赋值
      if (this.$router.isleft) {
        this.transitionName = "slideleft";
      }
      if (this.$router.isright) {
        this.transitionName = "slideright";
      }
    },
  },
  mounted() {
    const that = this;
    if (window.plus) {
      // 在这里调用h5+ API
      that.getVersion();
    } else {
      // 兼容老版本的plusready事件
      document.addEventListener(
        "plusready",
        function () {
          // 在这里调用h5+ API
          that.getVersion();
        },
        false
      );
    }
  },
  methods: {
    async getVersion() {
      let that = this;
      let wgtVer;
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, function (inf) {
        console.log("当前版本号", inf.version);
        wgtVer = inf.version; // 当前版本号
        localStorage.setItem("thisVerion", inf.version);
      });
      let preUrl = 'http://localhost:8200/api/service';
      if (process.env.NODE_ENV == "production") {
        preUrl = 'http://***********:8080/imom/service'; // 正式
      } else if (process.env.NODE_ENV == "test") {
        preUrl = 'http://ims-em-test.baointl.info/service';// 测试
      } else {
        preUrl = 'http://ims-em-test.baointl.info/service';// 测试
      }


      let res = await axios.post(preUrl, {
        serviceId: "S_LI_RL_0112",
        // segNo: 'KF000000', // 安徽宝钢
        // segNo: 'JE000000', // 西安宝钢
        segNo: 'JC000000', // 重庆宝钢
      });
      let resData = res.data;
      if (resData.__sys__.status != -1) {
        let newUrl = resData.result.downUrl;
        let newVersion = resData.result.version;
        localStorage.setItem("newVerion", newVersion);
        let flag = that.compare(newVersion, wgtVer);
        console.log("falg判断", flag);
        if (flag == 1) {
          plus.nativeUI.confirm(
            `检测到新版本(${newVersion})，是否更新`,
            function (e) {
              if (e.index == 0) {
                var wgtUrl = newUrl;

                var downloadTask = plus.downloader.createDownload(wgtUrl, { //拿到下载任务的对象
                  filename: '_doc/update/'
                }, function (d, status) {
                  plus.nativeUI.closeWaiting();
                  if (status == 200) { //在回调中根据状态 进行操作
                    var path = d.filename; //下载apk
                    plus.runtime.install(
                      path, {},
                      function () {
                        plus.nativeUI.alert(
                          "更新完成",
                          function () {
                            plus.runtime.restart();
                          }
                        );
                      },
                      function (e) {
                        console.log(
                          "安装wgt文件失败[" +
                          e.code +
                          "]：" +
                          e.message
                        );
                        plus.nativeUI.alert(
                          "版本更新失败:" +
                          "[" +
                          e.code +
                          "]：" +
                          e.message
                        );
                      }
                    ); // 自动安装apk文件

                  } else {
                    console.log("下载更新失败！");
                    plus.nativeUI.toast("下载更新失败！");
                  }
                });

                // try {
                console.log("----------------进入");
                // 开始下载
                downloadTask.start()
                //显示下载状态显示框
                var waiting = plus.nativeUI.showWaiting("正在下载 - 0%", {
                  back: "none"
                });
                var pre_percent = 0;
                //监听下载
                downloadTask.addEventListener('statechanged', function (download, status) {
                  //显示loading和进度
                  switch (download.state) {
                    case 0:
                      //下载任务处于可调度下载状态
                      break;
                    case 1:
                      //下载任务建立网络连接，发送请求到服务器并等待服务器的响应
                      break;
                    case 2:
                      // 下载任务网络连接已建立，服务器返回响应，准备传输数据内容
                      break;
                    case 3:
                      // 下载任务接收数据，计算下载进度
                      let percent = parseInt(parseFloat(download.downloadedSize) / parseFloat(download
                        .totalSize) * 100)
                      //取整的情况下会出现同一个百分比出现多次的情况，每次都会执行waiting.setTitle()
                      //有时会出现percent无法正常显示的情况，可能是因为频繁执行waiting.setTitle()导致堆栈内存溢出的问题
                      //增加判断，当percent变化时才执行waiting.setTitle()，以减少函数执行次数，目测有效果
                      if (percent > pre_percent) {
                        waiting.setTitle("正在下载 - " + percent + "%");
                        pre_percent = percent
                      }
                      //经测试，并没有返回状态4，所以自行执行关闭弹窗代码
                      //当已经下载的文件大小等于总文件大小时，执行关闭
                      if (download.downloadedSize == download.totalSize) {
                        plus.nativeUI.closeWaiting();
                      }
                      break;
                    case 4:
                      // 下载任务已完成
                      plus.nativeUI.closeWaiting();
                      break;
                  }
                })
              }
            },
            "检测到新版本",
            ["确定", "取消"]
          );
        } else {
          console.log("已经是最新版本");
        }
      } else {
        plus.nativeUI.toast(res.data.__sys__.msg);
      }
    },

    compare(version1, version2) { // 新版本，旧版本
      let v1 = version1.split('.');
      let v2 = version2.split('.');
      v1 = v1.map(item => Number.parseInt(item));
      v2 = v2.map(item => Number.parseInt(item));
      let len = Math.max(v1.length, v2.length);
      for (let i = 0; i < len; i++) {
        v1[i] || v1.push(0);
        v2[i] || v2.push(0);
        if (v1[i] > v2[i]) {
          return 1;
        } else if (v1[i] < v2[i]) {
          return -1;
        }
      }
      return 0;
    },
  },


};
</script>

<style>
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  width: 100vw;
}

.Router {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
}

.slideleft-enter,
.slideright-leave-active {
  opacity: 0;
  -webkit-transform: translate(100%, 0);
  transform: translate(100%, 0);
}

.slideleft-leave-active,
.slideright-enter {
  opacity: 0;
  -webkit-transform: translate(-100%, 0);
  transform: translate(-100%, 0);
}
</style>
