<template>
    <div>
        <van-sticky>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">出库捆包清单</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </van-sticky>
        <div class="" style="background: white">
            <van-form>
                <van-field v-model="storeNumber" label="捆包" ref="baleRef" class="all-font-size" clearable
                    placeholder="请输入或扫描捆包号" @keyup.enter.native="getBaleByPackId" :required="true"
                    enterkeyhint="enter" />
                <van-field label="车牌号" placeholder="车牌号" class="all-font-size" v-model="carNumber" readonly />

                <div class="detail_row">
                    <div class="fourtext">已/未扫</div>
                    <div style="width: 80%">
                        <input class="weight_input" type="text" v-model="scanned" readonly />
                        <input class="weight_input" type="text" v-model="notScanned" readonly />
                    </div>
                </div>
                <van-field label="规格" placeholder="规格" class="all-font-size" v-model="specsDesc" readonly />
                <div class="detail_row">
                    <div class="fourtext">重/件</div>
                    <div style="width: 80%">
                        <input class="weight_input" type="text" :value="formatNumber(allNetWeight)" readonly />
                        <input class="weight_input" type="text" :value="allPieceNum" readonly />
                    </div>
                </div>
                <div class="detail_row">
                    <div class="fourtext">总重/件</div>
                    <div style="width: 80%">
                        <input class="weight_input" type="text" :value="formatNumber(residueNetWeight)" readonly />
                        <input class="weight_input" type="text" :value="residuePieceNum" readonly />
                    </div>
                </div>
                <div class="detail_row">
                    <div class="fourtext">总捆包</div>
                    <div style="width: 80%">
                        <input class="weight_input" type="text" v-model="allPackQty" readonly />
                    </div>
                </div>
                <van-field v-model="storeName" class="all-font-size" label="仓库" readonly />
                <van-field v-show="remark" label="备注" type="textarea" placeholder="备注" class="all-font-size"
                    v-model="remark" autosize readonly />
            </van-form>
        </div>

        <van-tabs v-model="active" color="#007aff" style="width: 100%; background: white" line-width="60px"
            offset-top="44px" title-active-color="#007aff" sticky>
            <van-tab title="已扫捆包">
                <div v-if="packList && packList.length > 0">
                    <div class="inlist-content">
                        <div class="detail_textarea">
                            <div class="detail_text" style="padding-bottom: 10px">
                                <div class="fourline-blue"></div>
                                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                                    已扫捆包合计 :
                                    <span class="span-count">{{ packList.length }}</span>
                                </div>
                            </div>
                            <div class="detail_text" style="padding-bottom: 10px">
                                <div class="fourline-blue"></div>
                                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                                    总件数 :
                                    <span class="span-count">{{ allPieceNum }}</span>
                                    总吨数 :
                                    <span class="span-count">{{
                                        formatNumber(allNetWeight)
                                    }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="content-cell">
                            <van-swipe-cell v-for="(item, index) in packList" :key="index">
                                <div style="text-align: left">
                                    <div class="detail_textarea">
                                        <div class="detail_text">
                                            <div class="fourtext3">{{ item.packId }}</div>
                                        </div>
                                        <div class="border_top">
                                            <div>
                                                <div>
                                                    <span class="check-spec">母捆包：</span>
                                                    <span class="check-val">{{ item.mPackId }}</span>
                                                </div>
                                                <div v-if="item.locationId">
                                                    <span class="check-spec">库位：</span>
                                                    <span class="check-val">{{ item.locationId }}/{{ item.locationName
                                                    }}</span>
                                                </div>
                                                <!-- <div>
                                                    <span class="check-spec">是否已配单：</span>
                                                    <span class="check-val"
                                                        :style="item.isAlready == '未配单' ? 'color: red;' : ''">{{
                                                            item.isAlready
                                                        }}</span>
                                                </div> -->
                                                <div>
                                                    <span class="check-spec">标签：</span>
                                                    <span class="check-val">{{ item.labelId }}</span>
                                                </div>
                                                <div>
                                                    <span class="check-spec">规：</span>
                                                    <span class="check-val">{{ item.specsDesc }}</span>
                                                </div>
                                                <div>
                                                    <span class="check-spec">重/件：</span>
                                                    <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum
                                                    }}</span>
                                                </div>
                                                <div>
                                                    <span class="check-spec">客：</span>
                                                    <span class="check-val">{{
                                                        item.settleUserName
                                                    }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <template #right>
                                    <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                                        <span class="swiper-text">删除</span>
                                    </button>
                                </template>
                            </van-swipe-cell>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="mui-input-row" v-show="isShowBottom">
                        <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm"
                            v-if="packList.length > 0">
                            出&nbsp; &nbsp; &nbsp;&nbsp; 库
                        </button>


                    </div>
                </div>
            </van-tab>

            <van-tab title="未扫捆包">
                <van-swipe-cell v-for="(item, index) in noScanPackList" :key="index">
                    <div style="text-align: left">
                        <div class="detail_textarea">
                            <div class="detail_text">
                                <div class="fourtext3">{{ item.packId }}</div>
                            </div>
                            <div class="border_top">
                                <div>
                                    <div>
                                        <span class="check-spec">母捆包：</span>
                                        <span class="check-val">{{ item.mPackId }}</span>
                                    </div>
                                    <div v-if="item.locationId">
                                        <span class="check-spec">库位：</span>
                                        <span class="check-val">{{ item.locationId }}/{{ item.locationName }}</span>
                                    </div>
                                    <!-- <div>
                                        <span class="check-spec">是否已配单：</span>
                                        <span class="check-val" :style="item.isAlready == '未配单' ? 'color: red;' : ''">{{
                                            item.isAlready }}</span>
                                    </div> -->
                                    <div>
                                        <span class="check-spec">标签：</span>
                                        <span class="check-val">{{ item.labelId }}</span>
                                    </div>
                                    <div>
                                        <span class="check-spec">规：</span>
                                        <span class="check-val">{{ item.specsDesc }}</span>
                                    </div>
                                    <div>
                                        <span class="check-spec">重/件：</span>
                                        <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum }}</span>
                                    </div>
                                    <div>
                                        <span class="check-spec">客：</span>
                                        <span class="check-val">{{ item.settleUserName }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </van-swipe-cell>
            </van-tab>
        </van-tabs>
    </div>
</template>

<script>
import { post } from "../../api/base-service";
import { Dialog } from "vant";
export default {
    data() {
        return {
            packList: [], //已扫捆包
            noScanPackList: [], //未扫捆包
            storeNumber: "", //捆包号
            carNumber: "", //车牌号
            baleList: {}, //捆包信息
            baleListNetWeight: 0, //捆包重量
            baleListPieceNum: 0, //捆包件数
            specsDesc: "", //规格
            storeName: localStorage.getItem("warehouseName"), //仓库名称
            remark: "", //备注
            active: 2,
            isShowBottom: true, //显示或者隐藏footer
            signShow: false,
            billLading: [], //提货单
            carTraceNo: "", //车辆跟踪号
            rowList: [], //出库参数
            aleradyList: [],
            allocateVehicleNo: '',
            totalNetWeight: 0, // 总重量
            totalPieceNum: 0, // 总件数
            totalPackQty: 0, // 总捆包数
        };
    },
    computed: {
        scanned() {
            //已扫捆包数量
            return this.packList.length;
        },
        notScanned() {
            //未扫捆包数量
            return this.noScanPackList.length;
        },
        allPackQty() {
            //总捆包
            return this.totalPackQty;
        },
        allNetWeight() {
            //合计重量
            return this.packList.reduce((accumulator, currentValue) => {
                return (accumulator * 1000 + currentValue.netWeight * 1000) / 1000;
            }, 0);
        },
        allPieceNum() {
            //合计件数
            return this.packList.reduce((accumulator, currentValue) => {
                return accumulator + currentValue.pieceNum;
            }, 0);
        },
        // 剩余重量 = 总重量 - 已扫描重量
        residueNetWeight() {
            return this.totalNetWeight - this.allNetWeight;
        },
        // 剩余件数 = 总件数 - 已扫描件数
        residuePieceNum() {
            return this.totalPieceNum - this.allPieceNum;
        },
    },

    async created() {
        this.billLading = [];
        this.carNumber = localStorage.getItem('carNumber');
        this.carTraceNo = localStorage.getItem('carTraceNo');
        this.allocateVehicleNo = localStorage.getItem('allocateVehicleNo');
        this.billLading = localStorage.getItem('billLading').split(',');
        await this.openCarAction();
        this.$nextTick(() => {
            this.$refs.baleRef.focus();
        });
    },
    // mounted() {
    //     this.openCarAction();
    // },
    methods: {
        // 数组去重辅助方法
        removeDuplicates(array, key) {
            const seen = new Set();
            return array.filter(item => {
                const identifier = item[key];
                if (seen.has(identifier)) {
                    return false;
                }
                seen.add(identifier);
                return true;
            });
        },

        formatNumber(val) {
            return Math.round(val * 1000) / 1000; // 保留3位小数
        },
        onClickLeft() {
            if (this.packList.length > 0 || this.noScanPackList.length > 0) {
                Dialog.confirm({
                    title: "提示",
                    message: "清单里有数据未提交确认退出？",
                })
                    .then(() => {
                        this.$router.goBack();
                    })
                    .catch(() => { });
                return;
            }
            this.$router.goBack();
        },

        /**
 * 查询捆包是否已配单
 */
        async getPackAlreadyOrdered() {
            const params = {
                serviceId: 'S_LI_RL_0164',
                voucherNumList: this.billLading,
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                allocateVehicleNo: this.allocateVehicleNo,
            };
            const res = await post(params);
            if (!res || !res.list || res.list.length == 0) {
                return;
            }

            // 对已配单列表进行去重处理
            const aleradyList = this.removeDuplicates(res.list || [], 'packId');
            this.aleradyList = aleradyList;
            this.noScanPackList = this.noScanPackList.map(n => {
                const aleradyPack = aleradyList.find(a => a.packId == n.packId);
                return {
                    ...n,
                    isAlready: aleradyPack ? '已配单' : '未配单',
                }
            });

        },

        //获取捆包信息
        async openCarAction() {
            const params = {
                serviceId: "S_LI_RL_0041",
                ladingBillIdList: this.billLading,
                warehouseCode: localStorage.getItem("warehouseCode"),
            };
            const result = await post(params);
            console.log(result);
            if (!result.result) {
                Dialog.alert({
                    message: "网络异常, 请稍后重试!",
                });
                return;
            }
            if (result.result.length == 0) {
                Dialog.alert({
                    message: "未查询到车牌号信息",
                });
                return;
            }
            console.log(result, 'result');

            // 对初始数据进行去重处理，防止后端返回重复数据
            const uniquePackList = this.removeDuplicates(result.result.packList || [], 'packId');
            this.noScanPackList = uniquePackList;

            this.totalNetWeight = result.result.totalNetWeight;
            this.totalPieceNum = result.result.totalPieceNum;
            this.totalPackQty = result.result.totalNum;
            this.getPackAlreadyOrdered();
        },

        // 扫描捆包
        async getBaleByPackId() {
            if (!this.noScanPackList || this.noScanPackList.length == 0) {
                Dialog.alert({
                    message: "此捆包不在发货计划中",
                });
                return;
            }

            const packObj = this.noScanPackList.find(ns => ns.packId == this.storeNumber);
            const params = {
                serviceId: "S_LI_RL_0042",
                warehouseCode: localStorage.getItem("warehouseCode"),
                packId: this.storeNumber,
                packIdListString: this.packList.filter(item => item.planParticle == '20' || item.planParticle == '30')
                    .map(item => item.packId).join(','),
                ladingBillIdList: this.billLading,
                packList: this.packList.map(item => ({
                    ladingBillId: item.voucherNum,
                    packId: item.packId
                })),
                planParticle: packObj ? packObj.planParticle : '10',
            };
            const result = await post(params);
            if (!result.result) {
                return;
            }
            if (result.result.length == 0) {
                Dialog.alert({
                    message: "未查询到捆包信息",
                });
                return;
            }
            const labelIdExists = this.packList.some(
                (item) => item.packId == this.storeNumber
            );
            if (!labelIdExists) {
                let curPack = result.result[0];
                curPack['isAlready'] = this.aleradyList.length == 0 ? '' : this.aleradyList.find(a => a.packId == curPack.packId) ? '已配单' : '未配单';
                this.packList.unshift(curPack);
            } else {
                Dialog.confirm({
                    title: "提示",
                    message: `此捆包已添加, 请勿重复扫描`,
                })
                    .then(() => { })
                    .catch(() => {
                    });
            }

            this.specsDesc = result.result[0].specsDesc;
            this.remark = result.result[0].remark;
            // 从未扫描列表中移除该捆包，并确保不会重复
            this.noScanPackList = this.noScanPackList.filter(
                (item) => item.packId != this.storeNumber
            );
            this.storeNumber = '';
        },

        // 跳转到imc出库页面, 将扫描的捆包带过去
        async startOutbound() {
            this.$router.togo({
                name: 'outbound-cq',
                params: {
                    carNumber: this.carNumber,
                    carTraceNo: this.carTraceNo,
                    packList: this.packList,
                },
            });

        },

        deleteListItem(index, item) {
            //删除数组中值
            this.$delete(this.packList, index);

            // 检查是否已存在于未扫描列表中，避免重复
            const exists = this.noScanPackList.some(pack => pack.packId === item.packId);
            if (!exists) {
                this.noScanPackList.unshift(item);
            }
            this.storeNumber = "";

            // 如果还有其他捆包，显示最后一个捆包的规格信息，否则清空
            if (this.packList.length > 0) {
                const lastPack = this.packList[0]; // 最新扫描的捆包
                this.specsDesc = lastPack.specsDesc;
                this.remark = lastPack.remark || "";
            } else {
                this.specsDesc = "";
                this.remark = "";
            }
        },

        onConfirm() {
            if (this.noScanPackList.length && this.packList.length > 0) {
                Dialog.confirm({
                    title: "提示",
                    message: `还有捆包未扫描，是否继续出库?`,
                })
                    .then(() => {
                        this.startOutbound();
                    })
                    .catch(() => {
                        // on cancel
                    });
            } else if (this.packList.length > 0) {
                this.startOutbound();
            }
            return;
        },
    },
};
</script>

<style lang="less" scoped>
.inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
        width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
        background-color: #d9d9d9;
    }
}

/deep/ .van-dialog {
    position: fixed;
    top: 60%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
}

.title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
}

.content-cell {
    margin-bottom: 100px;
}

.check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
}

.check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
}

.content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.div-flex {
    display: flex;
    justify-content: space-between;
}

.swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
}

.swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
}

.swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
}
</style>