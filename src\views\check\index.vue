<template>
  <div>
   <van-nav-bar>
      <template #title>
        <div class="global-hfont">盘点列表</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div>
      <div class="mui-content" style="margin-bottom: 86px">
        <div style="text-align: left">
          <div class="detail_textarea" v-for="item in 2" :key="item">
            <div class="detail_text">
              <div class="fourtext3">14457689403</div>
              <div style="margin-right: 5%">
                <button class="btn_update">修改</button>
              </div>
              <div style="margin-right: 5%">
                <button class="btn_delete">删除</button>
              </div>
            </div>
            <div class="border_top">
              <div class="content_div">
                <div class="check-spec">规格：</div>
                <div class="check-val">0.7*1360*C</div>
              </div>
              <div class="content_div">
                <div class="check-spec">标签：</div>
                <div class="check-val">R1611000009</div>
              </div>
              <div class="content_div2">
                <div class="div-flex">
                  <div class="check-spec">原库：</div>
                  <div class="check-val">test1</div>
                </div>
                <div class="div-flex">
                  <div class="check-spec">新库：</div>
                  <div class="check-val">test2</div>
                </div>
              </div>
              <div class="content_div">
                <div class="check-spec">异议：</div>
                <div class="check-val"></div>
              </div>
              <div class="content_div2">
                <div class="div-flex">
                  <div class="check-spec">重量：</div>
                  <div class="check-val">15.24</div>
                </div>
                <div class="div-flex">
                  <div class="check-spec">数量：</div>
                  <div class="check-val">1</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
  },
};
</script>

<style lang="less" scoped>
.check-spec {
  font-size: 15px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 21px;
}
.check-val {
  font-size: 16px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}
.div-flex {
  display: flex;
  justify-content: space-between;
}
</style>