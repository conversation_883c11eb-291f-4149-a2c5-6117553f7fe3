<template>
    <div>
        <van-sticky>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">选择车牌号</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </van-sticky>

        <van-field v-model="testCar" label="车牌号" enterkeyhint="enter" @keyup.enter.native="getCarNumberList"
            placeholder="请输入车牌号" class="all-font-size">
            <template #button>
                <van-button size="small" type="info" @click="noCarGo" class="all-font-size">确认</van-button>
            </template>
        </van-field>

        <van-cell title="选择车牌号" class="all-font-size" is-link @click="getCarNumberList" />
        <van-cell title="手选车牌" @click="showChooseCar = true" class="all-font-size">
            <template #right-icon>
                <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
            </template>
        </van-cell>


        <van-popup v-model="showChooseCar" position="bottom" :style="{ height: '70%' }">
            <chooseCarNumber @chooseCarGo="chooseCarGo"></chooseCarNumber>
        </van-popup>

        <van-dialog v-model="isShowCar" title="选择车牌号" show-cancel-button :beforeClose="mandatoryCar">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="radio">
                    <van-cell-group>
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in carList" :key="index"
                            :label="item.driverName" @click="radio = item.vehicleNo">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import {
    Dialog
} from "vant";
import chooseCarNumber from "@/components/carNumber.vue";
export default {
    data() {
        return {
            remark: '',//备注信息
            // isVehicleRequired: 0, //0
            showChooseCar: false,
            testCar: "",
            vehicleList: [],
            currentIndex: -1,
            searchHistoryList: [],
            value: "",
            check: false,
            carNumber: "",
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            isShowBottom: true, //显示或者隐藏footer
            documentHeight: document.documentElement.clientHeight,
            isShowCar: false,
            radio: '',
            carTraceNo: '', // 配单号
            carList: [],
        };
    },
    components: {
        chooseCarNumber,
    },
    created() {
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                if (this.documentHeight > document.documentElement.clientHeight) {
                    this.isShowBottom = false;
                } else {
                    this.isShowBottom = true;
                }
            })();
        };
    },
    methods: {
        isVehicleNumber(vehicleNumber) {
            const xxreg =
                /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼港澳使领][A-Z][A-Z0-9]{6}$/; // 2021年新能源车牌不止有DF
            const creg =
                /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
            if (vehicleNumber.length == 7) {
                return creg.test(vehicleNumber);
            } else if (vehicleNumber.length == 8) {
                return xxreg.test(vehicleNumber);
            } else {
                return false;
            }
        },

        onClickLeft() {
            this.$router.replace('/unload-cq');
        },

        noCarGo() {
            if (!this.isVehicleNumber(this.testCar)) {
                this.$toast("请输入正确车牌号");
                return;
            }

            localStorage.setItem("carNumber", this.testCar);
            this.$router.push({ path: '/select-bill-in-imc', query: { carNumber: this.testCar, remark: this.remark } });
        },

        chooseCarGo(val) {
            if (!this.isVehicleNumber(val)) {
                this.$toast("请输入正确车牌号");
                return;
            }

            this.showChooseCar = false;
            localStorage.setItem("carNumber", val);
            this.$router.push({ path: '/select-bill-in-imc', query: { carNumber: val, remark: this.remark } });
        },

        noCar() {
            this.$router.push({ path: '/select-bill-in-imc', query: { carNumber: this.testCar, remark: this.remark } });
        },

        //清空搜索历史
        deleteItem() {
            localStorage.removeItem("vehicleInHistoryList")
            this.searchHistoryList = [];
        },

        onClickItem(item) {
            this.testCar = item;
        },

        isChecked(index, item) {
            if (!this.searchHistoryList.includes(item)) {
                this.searchHistoryList.unshift(item);
                localStorage.setItem("vehicleInHistoryList", JSON.stringify(this.searchHistoryList))
            } else {
                //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
                let i = this.searchHistoryList.indexOf(item);
                this.searchHistoryList.splice(i, 1);
                this.searchHistoryList.unshift(item);
            }
            this.$router.togo("/select-bill-in-imc?carNumber=" + item);
        },

        /**
 * 关闭选择车辆弹出框
 */
        mandatoryCar(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.radio) {
                Dialog.alert({ message: '请选择车辆' });
                done(false);
                return;
            }

            const data = this.carList.find(c => c.vehicleNo == this.radio);
            this.testCar = data.vehicleNo;
            this.carTraceNo = data.carTraceNo;
            this.allocateVehicleNo = data.allocateVehicleNo;
            localStorage.setItem('carTraceNo', data.carTraceNo);
            localStorage.setItem('carNumber', data.vehicleNo);
            localStorage.setItem('allocateVehicleNo', this.allocateVehicleNo);
            done(true);
        },

        /**
         * 查询车牌号
         */
        async getCarNumberList() {
            const params = {
                serviceId: 'S_LI_RL_0087',
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                this.$toast('无车牌号信息');
                return;
            }

            if (result.list.length == 1) {
                this.testCar = result.list[0].vehicleNo;
                this.carTraceNo = result.list[0].carTraceNo;
                this.allocateVehicleNo = result.list[0].allocateVehicleNo;

                localStorage.setItem('carNumber', this.testCar);
                localStorage.setItem('carTraceNo', this.carTraceNo);
                localStorage.setItem('allocateVehicleNo', this.allocateVehicleNo);
                return;
            }

            this.carList = result.list;
            this.isShowCar = true;
        },

    },
};
</script>

<style lang="less" scoped>
.load-content {
    padding-bottom: 120px;
}

.activeColor {
    color: #007aff;
}

.search-content {
    padding: 15px;


    .search-history {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #DCDCDC;
    }

    .list-content {
        display: flex;
        flex-wrap: wrap;
        padding-top: 10px;

        .content-item {
            background-color: #DCDCDC;
            border-radius: 20px;
            padding: 5px 10px 5px 10px;
            margin-left: 5px;
            margin-top: 5px;
            font-size: 15px;
        }
    }
}

.load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
}

.load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
}

.load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
}
</style>