<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">选择车牌号</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div class="in-content" style="height: 45%;overflow-y: auto;padding-bottom: 0;">
            <van-cell is-link title="装卸点" size="large" v-model="targetHandPointId" @click="openHandPoint('1')" />


            <van-cell title="车牌号" size="large" v-model="carNumber" />
            <div style="text-align: right;margin-right: 12px;">
                <van-button round plain type="info" size="small" @click="getOtherCar()">作业中车辆</van-button>
            </div>

            <van-radio-group v-model="radioCarNumber" style="margin-top: 1em;height: 40%;overflow-y: auto;">
                <van-cell-group>
                    <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in carNumberList" :key="index"
                        @click="onSelect(item)">
                        <template #right-icon>
                            <van-radio :name="item.vehicleNo" />
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>

        </div>

        <div class="foot-sticky">
            <van-button type="info" size="large" class="foot-sticky-btn" @click="toUnload('1')">开始装货</van-button>
            <van-button type="info" size="large" class="foot-sticky-btn" @click="toUnload('2')">开始卸货</van-button>
            <van-button type="info" size="large" class="foot-sticky-btn" @click="openCall">强制叫号</van-button>
            <van-button type="info" size="large" class="foot-sticky-btn" @click="returnFactory">离厂回退</van-button>
            <van-button type="info" size="large" class="foot-sticky-btn" @click="uninstallFactory">未装离厂</van-button>
        </div>

        <van-dialog v-model="isShowPonit" title="选择装卸点" show-cancel-button :beforeClose="closeHandPonit">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="handPonitRadio">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in pointList" :key="index" :title="item.handPointName"
                            :label="item.handPointId" @click="handPonitRadio = item.handPointId">
                            <template #right-icon>
                                <van-radio :name="item.handPointId" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>


        <van-dialog v-model="isShowCall" title="强制叫号" show-cancel-button :beforeClose="mandatoryCall">
            <van-cell is-link title="装卸点" size="large" v-model="callHandPointName" @click="openHandPoint('2')" />
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="radio">
                    <van-cell-group>
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in callCarList" :key="index"
                            :label="item.targetHandPointName" @click="radio = item.vehicleNo">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>

        <van-dialog v-model="isShowReturn" title="离厂回退" show-cancel-button :beforeClose="returnCar">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="callRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in returnCarList" :key="index"
                            @click="callRadio = item.vehicleNo">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>

        <van-dialog v-model="isShowUninstall" title="未装离厂" show-cancel-button :beforeClose="confirmUninstall">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="uninstallRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in uninstallList" :key="index"
                            @click="uninstallRadio = item.vehicleNo">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>

        <van-dialog v-model="isShowTruck" title="装卸人员" show-cancel-button :beforeClose="confirmTruck">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="truckRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.perName" v-for="(item, index) in loadTruckOperatorList"
                            :key="index" @click="truckRadio = item.perName">
                            <template #right-icon>
                                <van-radio :name="item.perName" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog, Notify } from "vant";
export default {
    data() {
        return {
            carNumber: '',
            isShowCarNumber: false,
            carNumberActions: [],
            targetHandPointId: '', // 装卸点
            carNumberList: [], // 车牌号数组
            carTraceNo: '', // 车辆跟踪号
            handPointId: '',
            isShowCall: false,
            radio: '',
            callCarList: [],
            pointList: [],
            ponitActions: [],
            isShowPonit: false,
            handPonitRadio: '',
            radioCarNumber: '',
            carInPoint: [],
            status: '',
            isShowReturn: false,
            callHandPointId: '', // 强制叫号的装卸点
            callHandPointName: '',
            openPointType: '',
            callRadio: '',
            returnCarList: [],
            uninstallRadio: '',
            uninstallList: [],
            isShowUninstall: false,
            loadTruckOperatorList: [],
            isShowTruck: false,
            truckRadio: '',
        };
    },
    methods: {
        onClickLeft() {
            this.$router.replace('/index');
        },


        onSelect(currentData) {
            this.carNumber = currentData.vehicleNo;
            this.carTraceNo = currentData.carTraceNo;
            this.radioCarNumber = currentData.vehicleNo;
            this.status = currentData.status,
                localStorage.setItem('carNumber', currentData.vehicleNo); // 用缓存来使用车牌号
            localStorage.setItem('carTraceNo', currentData.carTraceNo);// 车辆跟踪号
        },

        async confirmTruck(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.truckRadio) {
                Dialog.alert({ message: '请选择装卸人员' });
                done(false);
                return;
            }

            const currentData = this.loadTruckOperatorList.find(p => p.perName == this.truckRadio);
            localStorage.setItem("perName", currentData.perName);
            localStorage.setItem("perNo", currentData.perNo);

            const query = {
                serviceId: 'S_LI_RL_0010',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                currentHandPointId: this.handPointId,
                status: this.status,
                perName: currentData.perName,
            };
            const resultData = await post(query);
            if (resultData.__sys__.status == -1) {
                return;
            }

            const type = localStorage.getItem("loadingType");
            localStorage.setItem('currentHandPointId', resultData.currentHandPointId);
            localStorage.setItem('loadId', resultData.loadId);
            this.$router.togo({
                name: (type && type.includes("卸")) ? "scan-store" : 'in-storage',
                params: {
                    carNumber: this.carNumber,
                    carTraceNo: this.carTraceNo,
                    handPointId: this.handPointId,
                    currentHandPointId: resultData.currentHandPointId,
                    loadId: resultData.loadId,
                },
            });

            done(true);
        },

        /**
         * 查询其他车辆
         */
        async getOtherCar() {
            if (!this.targetHandPointId) {
                Dialog.alert({
                    message: '请先选择装卸点!',
                });
                return;
            }
            const param = {
                serviceId: 'S_LI_RL_0055',
                handPointId: this.handPonitRadio,
            };
            const res = await post(param);
            if (!res) {
                return;
            }

            if (res.list.length == 0) {
                Dialog.alert({ message: '没有其他车辆' });
                return;
            }

            this.carNumberList = [
                ...this.carInPoint,
                ...res.list
            ];
        },

        /**
         * 确定选择装卸点
         * @param action
         * @param done
         */
        async closeHandPonit(action, done) {
            // 强制叫号页面
            if (this.openPointType.includes('2')) {
                if (action != 'confirm') {
                    this.callHandPointId = '';
                    this.callHandPointName = '';
                    done();
                    return;
                }

                const currentData = this.pointList.find(p => p.handPointId == this.handPonitRadio);
                this.callHandPointName = currentData.handPointName;
                this.callHandPointId = currentData.handPointId;
                done(true);
                return;
            }

            if (action != 'confirm') {
                this.targetHandPointId = '';
                this.handPointId = '';
                this.carNumber = '';
                this.radioCarNumber = '';
                done();
                return;
            }

            if (!this.handPonitRadio) {
                Dialog.alert({ message: '请选择装卸点' });
                done(false);
                return;
            }

            const currentData = this.pointList.find(p => p.handPointId == this.handPonitRadio);
            this.targetHandPointId = currentData.handPointName;
            this.handPointId = currentData.handPointId;
            localStorage.setItem('handPointId', currentData.handPointId); // 用缓存来使用车牌号
            localStorage.setItem('targetHandPointId', this.targetHandPointId); // 用缓存来使用车牌号

            // 查询车牌号
            await this.openCarAction();
            done(true);
        },

        /**
 * 查询车牌号
 */
        async openCarAction() {
            const params = {
                serviceId: 'S_LI_RL_0005',
                handPointId: this.handPointId,
            };
            let result = await post(params);
            if (!result.list || result.list.length == 0) {
                this.carInPoint = [];
                this.carNumberList = [];
                this.carNumber = '';
                Dialog.alert({
                    message: '未查到待作业的车辆，可点击【其他车辆】查看正在作业的车辆',
                });
                return;
            }
            this.carInPoint = result.list;
            this.carNumberList = result.list;

            if (result.list.length == 1) {
                this.onSelect(result.list[0]);
                return;
            }
            return;

        },

        /**
         * 查询装卸点
         */
        async openHandPoint(type) {
            const params = {
                serviceId: 'S_LI_RL_0051',
                factoryArea: localStorage.getItem('factoryAreaTrue'),
            };
            const result = await post(params);
            if (!result.list || result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到装卸点信息',
                });
                return;
            }

            this.openPointType = type;
            this.pointList = result.list;
            this.isShowPonit = true;
        },

        /**
         * 开始装卸货
         * @param type
         */
        async toUnload(type) {
            if (!this.carNumber) {
                this.$toast('请选择车牌号');
                return;
            }

            localStorage.setItem("loadingType", type == 2 ? "卸" : '装');
            // 查询装卸人员
            await this.getLoadTruckOperatorList();
        },

        /**
         * 打开叫号页面弹框
         */
        async openCall() {
            const params = {
                serviceId: 'S_LI_RL_0050',
            };
            const result = await post(params);

            if (!result || !result.list) {
                return;
            }
            if (result.list.length == 0) {
                this.$toast('无车牌号数据');
                return;
            }



            this.callCarList = result.list;
            if (this.callCarList.length == 1) {
                this.radio = 0;
            }
            this.isShowCall = true;
        },

        /**
         * 强制叫号
         */
        async mandatoryCall(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.radio && this.radio != 0) {
                Dialog.alert({ message: '请选择强制叫号车辆' });
                done(false);
                return;
            }

            const data = this.callCarList.find(c => c.vehicleNo == this.radio);
            const params = {
                serviceId: 'S_LI_RL_0007',
                vehicleNo: data.vehicleNo,
                carTraceNo: data.carTraceNo,
                handPointId: this.callHandPointId ? this.callHandPointId : data.handPointId,
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                done(false);
                return;
            }

            this.callHandPointId = '';
            this.callHandPointName = '';
            this.$toast(result.__sys__.msg);
            done(true);
        },

        /**
 * 结束装卸货
 */
        // async endLoad() {
        //     const params = {
        //         serviceId: "S_LI_RL_0010",
        //         recCreator: localStorage.getItem("userId"),
        //         recCreatorName: localStorage.getItem("userName"),
        //         carTraceNo: this.carTraceNo,
        //         vehicleNo: this.carNumber,
        //         currentHandPointId: this.handPointId,
        //         perName: localStorage.getItem("perName"),
        //     };

        //     const result = await post(params);
        //     if (!result || result.__sys__.status != 1) {
        //         return;
        //     }
        //     this.$router.togo({
        //         name: "end-load",
        //         params: {
        //             vehicleNo: this.carNumber,
        //             carTraceNo: this.carTraceNo,
        //             handPointId: this.handPointId,
        //         },
        //     });
        // },

        /**
         * 打开离厂回退选择框
         */
        async returnFactory() {
            const params = {
                serviceId: 'S_LI_RL_0103',
            };
            const result = await post(params);

            if (!result || !result.list) {
                return;
            }
            if (result.list.length == 0) {
                this.$toast('无车牌号数据');
                return;
            }



            this.returnCarList = result.list;
            if (this.returnCarList.length == 1) {
                this.callRadio = 0;
            }

            this.isShowReturn = true;
        },

        /**
         * 确认离厂回退
         */
        async returnCar(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.callRadio && this.callRadio != 0) {
                Dialog.alert({ message: '请选择回退车辆' });
                done(false);
                return;
            }

            const data = this.returnCarList.find(c => c.vehicleNo == this.callRadio);
            const params = {
                serviceId: 'S_LI_RL_0104',
                vehicleNo: data.vehicleNo,
                carTraceNo: data.carTraceNo,
                currentHandPointId: data.currentHandPointId,
                finishLoadId: data.finishLoadId,
                recCreator: localStorage.getItem("userId"),
                recCreatorName: localStorage.getItem("userName"),
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                done(false);
                return;
            }

            this.$toast('回退成功');
            done(true);
        },

        /**
         * 未装离厂查询车辆
         */
        async uninstallFactory() {
            const params = {
                serviceId: 'S_LI_RL_0105',
            };
            const result = await post(params);

            if (!result || !result.list) {
                return;
            }
            if (result.list.length == 0) {
                this.$toast('无车牌号数据');
                return;
            }


            this.uninstallList = result.list;
            if (this.uninstallList.length == 1) {
                this.uninstallRadio = 0;
            }

            this.isShowUninstall = true;
        },

        async confirmUninstall(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.uninstallRadio && this.uninstallRadio != 0) {
                Dialog.alert({ message: '请选择未装离厂车辆' });
                done(false);
                return;
            }
            const data = this.uninstallList.find(c => c.vehicleNo == this.uninstallRadio);
            const params = {
                serviceId: 'S_LI_RL_0106',
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                ...data,
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                done(false);
                return;
            }

            this.$toast('未装离厂成功');
            done(true);
        },

        async getLoadTruckOperatorList() {
            const params = {
                serviceId: 'S_LI_RL_0137',
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            console.log(result);

            this.loadTruckOperatorList = result.list;
            this.truckRadio = '';
            this.isShowTruck = true;
        },
    },
};
</script>
<style>
.van-cell__value {
    color: #323233;
}

/* .hand-radion {
    font-size: 16px;
    color:#323233;
} */
</style>
