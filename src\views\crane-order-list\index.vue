<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">行车作业清单</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
                <!-- <template #right>
                    <span class="iconfont icon-sousuo" @click="onClickRight"></span>
                </template> -->
            </van-nav-bar>
        </div>

        <div>
            <div>
                <van-field label="行车" is-link readonly placeholder="请选择行车" v-model="craneName" @click="showCrane" />
                <van-field label="清单来源" is-link readonly placeholder="请选择清单来源" v-model="listSourceStr" @click="showListSource" />
                <van-field label="起始创建时间" is-link readonly placeholder="请选择起始创建时间" v-model="recCreateTimeStart" @click="showRecCreateTimeStart" />
                <van-field label="截止创建时间" is-link readonly placeholder="请选择截止创建时间" v-model="recCreateTimeEnd" @click="showRecCreateTimeEnd" />
                <van-field label="机组" is-link readonly v-model="machineName" @click="showMachine" placeholder="请选择机组"/>
                <van-field label="捆包号" v-model="packId" placeholder="请输入捆包号"/>
                <van-field label="工单号" v-model="voucherNum" placeholder="请输入工单号"/>
            </div>

            <van-button type="info" size="large" class="foot-sticky-btn" @click="fetchData()">查询</van-button>

            <van-popup v-model="isShowRecCreateTimeStart" position="bottom">
                <van-datetime-picker v-model="startTime" type="datetime" title="选择起始时间"
                    @confirm="confirmRecCreateTimeStart" @cancel="closeTimePopup" />
            </van-popup>

            <van-popup v-model="isShowRecCreateTimeEnd" position="bottom">
                <van-datetime-picker v-model="endTime" type="datetime" title="选择截止时间" @confirm="confirmRecCreateTimeEnd"
                    @cancel="closeTimePopup" />
            </van-popup>

            <van-dialog v-model="isShowCrane" title="行车" show-cancel-button :beforeClose="confirmCrane">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="craneRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.craneName" v-for="(item, index) in craneList" :key="index"
                                @click="craneRadio = item.craneId">
                                <template #right-icon>
                                    <van-radio :name="item.craneId" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>

            <van-dialog v-model="isShowMachine" title="机组" show-cancel-button :beforeClose="confirmMachine">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="machineRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.machineName" v-for="(item, index) in machineList"
                                :key="index" @click="machineRadio = item.machineCode">
                                <template #right-icon>
                                    <van-radio :name="item.machineCode" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>

            <van-dialog v-model="isShowListSource" title="清单来源" show-cancel-button :beforeClose="confirmListSource">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="listSourceRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.label" v-for="(item, index) in listSourceList" :key="index"
                                @click="listSourceRadio = item.value">
                                <template #right-icon>
                                    <van-radio :name="item.value" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';

export default {
    data() {
        return {
            isShowRecCreateTimeStart: false,
            isShowRecCreateTimeEnd: false,
            recCreateTimeStart: '',
            recCreateTimeEnd: '',
            startTime: new Date(),
            endTime: new Date(),
            craneId: '',
            craneName: '',
            machineCode: '',
            machineName: '',
            packId: '',
            voucherNum: '',
            isShowCrane: false,
            craneRadio: '',
            craneList: [],
            listSource: '',
            listSourceStr: '',
            isShowListSource: false,
            listSourceList: [],
            listSourceRadio: '',
            isShowMachine: false,
            machineRadio: '',
            machineList: [],
        };
    },

    methods: {
        formatTime(timeStr) {
            if (!timeStr || timeStr.length !== 14) return '';
            return `${timeStr.substr(0, 4)}/${timeStr.substr(4, 2)}/${timeStr.substr(6, 2)} ` +
                `${timeStr.substr(8, 2)}:${timeStr.substr(10, 2)}:${timeStr.substr(12, 2)}`;
        },

        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        onClickLeft() {
            this.$router.replace({ name: 'index' });
        },

        // 获取行车作业清单
        async fetchData() {
            // if (!this.craneId || !this.listSource) {
            //     return;
            // }
            const params = {
                craneId: this.craneId,
                listSource: this.listSource,
                recCreateTimeStart: this.recCreateTimeStart.replaceAll(/[\s-:]/g, ''),
                recCreateTimeEnd: this.recCreateTimeEnd.replaceAll(/[\s-:]/g, ''),
                machineCode: this.machineCode,
                machineName: this.machineName,
                packId: this.packId,
                voucherNum: this.voucherNum
            };

            this.$router.push({
                name: "crane-order-list-detail",
                params: {
                    queryCondition: params,
                },
            });
        },

        // 确认行车
        async confirmCrane(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.craneRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.craneList.find(p => p.craneId == this.craneRadio);
            if (!currentData) {
                done();
                return;
            }

            this.craneName = currentData.craneName;
            this.craneId = currentData.craneId;
            this.offset = 0;
            done(true);
        },

        // 确认清单来源
        async confirmListSource(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.listSourceRadio) {
                Dialog.alert({ message: '请选择清单来源' });
                done(false);
                return;
            }
            const currentData = this.listSourceList.find(p => p.value == this.listSourceRadio);
            if (!currentData) {
                done();
                return;
            }
            this.offset = 0;
            this.listSourceStr = currentData.label;
            this.listSource = currentData.value;
            done(true);
        },
        // 确认起始创建时间
        async confirmRecCreateTimeStart(value) {
            // Date格式转换为yyyyMMddHHmmss
            this.recCreateTimeStart = this.formatDate(value);
            this.isShowRecCreateTimeStart = false;
        },
        // 确认截止创建时间
        async confirmRecCreateTimeEnd(value) {
            this.recCreateTimeEnd = this.formatDate(value);
            this.isShowRecCreateTimeEnd = false;
        },

        // 获取行车
        async getCraneList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }
            this.craneList = result.list;
        },
        // 获取清单来源
        async getListSourceList() {
            let queryMap = {
                serviceId: 'S_VG_DM_0012',
                codeset: 'P042'
            };
            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            this.listSourceList = result.list;
        },
        // 获取机组
        async getMachineList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0019',
                factoryArea: localStorage.getItem("factoryAreaTrue"),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            this.machineList = result.returnList;
        },
        // 确认机组
        async confirmMachine(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }
            if (!this.machineRadio) {
                Dialog.alert({ message: '请选择机组' });
                done(false);
                return;
            }
            const currentData = this.machineList.find(p => p.machineCode == this.machineRadio);
            if (!currentData) {
                done();
                return;
            }
            this.machineName = currentData.machineName;
            this.machineCode = currentData.machineCode;
            done(true);
        },

        // 显示行车
        showCrane() {
            this.isShowCrane = true;
        },

        // 显示清单来源
        showListSource() {
            this.isShowListSource = true;
        },

        // 显示机组
        showMachine() {
            this.isShowMachine = true;
        },

        // 显示起始创建时间
        showRecCreateTimeStart() {
            this.isShowRecCreateTimeStart = true;
        },

        // 显示截止创建时间
        showRecCreateTimeEnd() {
            this.isShowRecCreateTimeEnd = true;
        },

        // 关闭时间弹窗
        closeTimePopup() {
            this.isShowRecCreateTimeStart = false;
            this.isShowRecCreateTimeEnd = false;
        },
    },

    mounted() {
        this.craneId = '';
        this.craneName = '';
        this.listSource = '';
        this.listSourceStr = '';
        this.recCreateTimeStart = '';
        this.recCreateTimeEnd = '';
        this.getListSourceList();
        this.getCraneList();
        this.getMachineList();
    }
};
</script>

<style scoped>
.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>