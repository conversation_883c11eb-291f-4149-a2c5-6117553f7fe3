<template>
    <div>
        <van-sticky>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">车辆排队一览</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </van-sticky>

        <van-pull-refresh success-text="刷新成功" v-model="isLoading" @refresh="onRefresh">
            <van-cell-group inset class="child-cell-data van-group" v-for="(item, index) in list" :key="index">
                <van-cell class="vCell" title="装卸点" v-model:value="item.handPointName" />
                <van-cell class="vCell" title="配单类型" v-model:value="item.allocTypeName" />
                <van-cell class="vCell" title="车牌号" v-model:value="item.vehicleNo" />
                <van-cell class="vCell" title="进厂时间" v-model:value="item.intoFactoryDate" />
                <van-cell class="vCell" title="业务类型" v-model:value="item.businessTypeName" />
                <div class="delete-btn">
                    <van-button type="info" size="small" @click="callNumber(item)">叫号</van-button>
                    <van-button type="info" size="small" @click="transferToDriver(item)">转给行(叉)车工</van-button>
                </div>
            </van-cell-group>
            <VanEmpty v-show="list.length == 0" description="" />
        </van-pull-refresh>

    </div>
</template>
<script>
import { post } from '../../api/base-service';
export default {
    data() {
        return {
            list: [],
            isLoading: false,
        };
    },
    async created() {
        await this.getList();

    },
    methods: {
        onClickLeft() {
            this.$router.replace('/index');
        },

        /**
         * 叫号
         * @param data 
         */
        callNumber(data) {
            console.log(data);
            // return;
            this.$router.togo({
                name: 'unload-cq',
                params: {
                    carNumber: data.vehicleNo,
                    carTraceNo: data.carTraceNo,
                    driverName: data.driverName,
                    driverTel: data.driverTel,
                    allocateVehicleNo: data.allocateVehicleNo,
                    handPointName: data.handPointName.split(','),
                },
            });
        },

        async onRefresh() {
            await this.getList();
            this.isLoading = false;
        },

        async getList() {
            const params = {
                serviceId: 'S_LI_RL_0057',
                perNo: localStorage.getItem('userId'),
                factoryArea: localStorage.getItem('factoryAreaTrue'), // 厂区代码
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            const result = await post(params);
            if (!result || !result.list || result.list.length == 0) {
                return;
            }
            let resultList = result.list;
            this.list = resultList.map(r => ({
                ...r,
                allocTypeName: r.allocType == '10' ? '装货配单' : '卸货配单',
            }));
        },

        async transferToDriver(data) {
            const params = {
                serviceId: 'S_LI_RL_0063',
                perNo: localStorage.getItem('userId'),
                result: [data],
            };
            const result = await post(params);
            if (!result || !result.__sys__ || result.__sys__.status == -1) {
                return;
            }
            this.$toast(result.__sys__.msg);
            await this.getList();
        },
    },
};
</script>
<style>
.child-cell-data {
    margin-bottom: 0.5em;
}

.vCell {
    padding: 8px;
}

.van-group {
    margin-top: 1em;
}

.van-cell__value {
    color: #323233;
}

div.delete-btn {
    height: 3.5em;
    line-height: 3em;
    text-align: right;
    margin-right: 12px;
}
</style>
