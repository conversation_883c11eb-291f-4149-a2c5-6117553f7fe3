// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import Vant from 'vant';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import 'vue2-datepicker/locale/zh-cn';
import 'vant/lib/index.css';
import 'vant/lib/icon/local.css';
import '@/assets/css/admin.css';
import '@/assets/less/navbar.less';
import '../static/js/flexible.js';
import Back from '../static/js/back.js';
import Calendar from 'vue-mobile-calendar';
import { Toast } from 'vant';
// 防止多次点击
import preventReClick from '@/utils/click.js'
import 'babel-polyfill'
import es6promise from 'es6-promise';

// 确保Promise polyfill
if (typeof Promise === 'undefined') {
  es6promise.polyfill();
} else {
  es6promise.polyfill();
}

// 添加Array方法polyfill
if (!Array.prototype.find) {
  Array.prototype.find = function(predicate) {
    if (this == null) {
      throw new TypeError('Array.prototype.find called on null or undefined');
    }
    if (typeof predicate !== 'function') {
      throw new TypeError('predicate must be a function');
    }
    var list = Object(this);
    var length = parseInt(list.length) || 0;
    var thisArg = arguments[1];
    for (var i = 0; i < length; i++) {
      var element = list[i];
      if (predicate.call(thisArg, element, i, list)) {
        return element;
      }
    }
    return undefined;
  };
}

if (!Array.prototype.findIndex) {
  Array.prototype.findIndex = function(predicate) {
    if (this == null) {
      throw new TypeError('Array.prototype.findIndex called on null or undefined');
    }
    if (typeof predicate !== 'function') {
      throw new TypeError('predicate must be a function');
    }
    var list = Object(this);
    var length = parseInt(list.length) || 0;
    var thisArg = arguments[1];
    for (var i = 0; i < length; i++) {
      var element = list[i];
      if (predicate.call(thisArg, element, i, list)) {
        return i;
      }
    }
    return -1;
  };
}

// Object.assign polyfill
if (typeof Object.assign !== 'function') {
  Object.assign = function(target) {
    if (target == null) {
      throw new TypeError('Cannot convert undefined or null to object');
    }
    var to = Object(target);
    for (var index = 1; index < arguments.length; index++) {
      var nextSource = arguments[index];
      if (nextSource != null) {
        for (var nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey];
          }
        }
      }
    }
    return to;
  };
}
Vue.use(preventReClick);
Toast.setDefaultOptions({ duration: 2000 });  // duration延迟时间
Vue.use(Toast);
Vue.use(Calendar);
Vue.use(Vant);
Vue.use(DatePicker);
// import vConsole from '@/utils/vconsole.js';
Vue.prototype.$plusExtends = fn => {
  if (window.plus) {
    setTimeout(fn, 0)
  } else {
    document.addEventListener('plusready', fn, false)
  }
}
Vue.config.productionTip = false
// 解决点击事件延迟
document.addEventListener('DOMContentLoaded', function () {
  typeof FastClick === 'function' && FastClick.attach(document.body);
}, false);
let offline = null
window.addEventListener('online', () => {
  //offline.close()
  offline = null
  Toast('网络连接恢复');
})
window.addEventListener('offline', () => {
  offline = Toast('网络连接失败');
})
/* eslint-disable no-new */
window.Vue = new Vue({
  el: '#app',
  router,
  Back,
  // vConsole,
  components: { App },
  template: '<App/>'
})
