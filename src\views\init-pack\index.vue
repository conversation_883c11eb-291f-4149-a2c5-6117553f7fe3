<template>
    <div>
        <ErrorHandler ref="errorHandler" />
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">捆包定位</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
                <!-- <template #right>
                    <span class="iconfont icon-sousuo" @click="onClickRight"></span>
                </template> -->
            </van-nav-bar>
        </div>

        <div class="in-content" style="height: 70%;overflow-y: auto;padding-bottom: 0;">
            <div>
                <van-field required label="库位" @keyup.enter.native="toFocus(3)" size="large" placeholder="请输入库位"
                    ref="locationRef" v-model="loactionId" />
                <van-field name="radio" required label="层" @keyup.enter.native="toFocus(3)" size="large" ref="posDirCodeRef">
                    <template #input>
                        <van-radio-group v-model="posDirCode" direction="horizontal">
                            <van-radio name="1">1</van-radio>
                            <van-radio name="2">2</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field required size="large" label="捆包号" v-model="packId" placeholder="请输入捆包号"
                    @keyup.enter.native="toFocus(4)" ref="packRef">
                    <template #button>
                        <QRScanner @scan-success="onScanSuccess" />
                    </template>
                </van-field>
                <van-field required size="large" label="坐标起始" type="number" ref="startDistanceRef"
                    @keyup.enter.native="toFocus(5)" v-model="startDistance" placeholder="坐标起始" />
                <van-field required size="large" label="坐标结束" type="number" ref="endDistanceRef" v-model="endDistance"
                    placeholder="坐标结束" />
            </div>
        </div>

        <div class="foot-sticky-unload">
            <div class="button-container">
                <van-button type="info" size="large" class="foot-sticky-btn-unload" @click="confirmData">
                    提交
                </van-button>
                <van-button type="warning" size="large" class="foot-sticky-btn-unload" @click="clearData">
                    重置
                </van-button>
            </div>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog } from 'vant';
import QRScanner from '../../components/QRScanner.vue';
import ErrorHandler from '../../components/ErrorHandler.vue';

export default {
    components: {
        QRScanner,
        ErrorHandler
    },
    data() {
        return {
            machineCode: '',
            machineName: '',
            packId: '',
            processOrderId: '',
            loactionId: '',
            posDirCode: '1',
            startDistance: '',
            endDistance: '',
        };
    },
    created() {
        this.$nextTick((x) => {
            this.$refs.packRef.focus();
        });
    },

    methods: {
        onClickLeft() {
            this.$router.replace('/index');
        },

        onCancelItem() {
            this.locationList = [];
        },

        /**
         * 焦点跳转
         */
        toFocus(sort) {
            if (sort == 2) {
                this.$refs.posDirCodeRef.focus();
            } else if (sort == 3) {
                this.$refs.packRef.focus();
            } else if (sort == 4) {
                this.$refs.startDistanceRef.focus();
            } else if (sort == 5) {
                this.$refs.endDistanceRef.focus();
            }
        },

        // 扫码成功处理
        onScanSuccess(data) {
            this.packId = data.text;
            // 自动跳转
            this.toFocus(4);
        },
        // 提交
        confirmData: function () {
            var self = this;
            try {
                if (!this.packId || !this.loactionId || !this.posDirCode || !this.startDistance || !this.endDistance) {
                    Dialog.alert({ message: '参数不能为空' });
                    return;
                }
                var params = {
                    serviceId: 'S_LI_DS_0030', // 假设这是根据捆包号查询的服务ID，需要根据实际情况调整
                    packId: this.packId,
                    posDirCode: this.posDirCode,
                    loactionId: this.loactionId,
                    startDistance: this.startDistance,
                    endDistance: this.endDistance,
                    factoryArea: localStorage.getItem("factoryAreaTrue"),
                    factoryBuilding: localStorage.getItem('factoryBuilding'),
                };

                post(params).then(function (result) {
                    if (!result || !result.__sys__ || !result.__sys__.status || result.__sys__.status == -1) {
                        // self.$toast(result.__sys__.msg);
                        Dialog.alert({
                            message: result.__sys__.msg,
                        });
                        return false;
                    } else {
                        self.$toast('提交成功');

                        // 清空表单
                        self.packId = '';
                        self.startDistance='';
                        self.endDistance='';
                    }
                }).catch(function (error) {
                    console.error('提交失败:', error);
                    self.$toast('网络请求失败，请重试');
                    if (self.$refs.errorHandler) {
                        self.$refs.errorHandler.handleError(error);
                    }
                });
            } catch (error) {
                console.error('提交方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
        // 重置
        clearData: function () {
            this.loactionId = '';
            this.posDirCode = '1';
            this.packId = '';
            this.startDistance = '';
            this.endDistance = '';
        },

    },

    mounted: function () {
        var self = this;
        // 页面初始化时设置错误处理
        try {
            // 检查基础环境
            if (typeof Promise === 'undefined') {
                throw new Error('您的设备不支持Promise，请更新浏览器版本');
            }

            // 页面初始化完成
            console.log('捆包定位页面初始化完成');
        } catch (error) {
            console.error('页面初始化异常:', error);
            if (this.$refs.errorHandler) {
                this.$refs.errorHandler.handleError(error);
            }
        }
    }
};
</script>

<style scoped>
.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}

.foot-sticky-unload {
    margin-top: 1em;
    width: 100%;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload {
    width: calc(50% - 4px) !important;
    margin-bottom: 0.8em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
    font-size: 12px !important;
    font-weight: bold !important;
    flex: none !important;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload:nth-child(odd):last-child {
    margin-right: auto !important;
}

.foot-sticky-unload .button-container {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    gap: 8px !important;
    padding: 0 2% !important;
}

.title {
    font-size: 14px;
    padding: 10px 10px;
}

.title2 {
    color: #969799;
    font-size: 14px;
    padding: 10px 10px;
    text-align: center;
}
</style>