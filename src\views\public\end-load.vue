<template>
    <div>
        <van-nav-bar>
            <template #title>
                <div class="global-hfont">装卸结束</div>
            </template>
            <template #left>
                <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
            </template>
        </van-nav-bar>
        <div class="in-content">
            <div class="detail_row" style="background-color: #e7e7e7">
                <div class="fourtext2">当前车辆</div>
                <div class="iconfont icon-shuxian" style="color: #d0d0d0"></div>
                <div class="fourtext1">
                    <input class="detail_input" type="text" v-model="vehicleNo" readonly />
                </div>
            </div>
            <van-dropdown-menu class="select-menu" active-color="#0000ff">
                <van-dropdown-item :title="nextTarget" ref="item">
                    <div v-if="nextTargetList && nextTargetList.length > 0">
                        <van-radio-group v-model="nextTargetNo">
                            <van-cell-group>
                                <van-cell v-for="(item, index) in nextTargetList" :key="index" clickable
                                    @click="isChecked(item)">
                                    <template #title>
                                        <div class="ware-title">{{ item.nextTargetName }}</div>
                                    </template>
                                    <template #label>
                                        <div class="ware-name">{{ item.nextTargetNo }}</div>
                                    </template>
                                    <template #right-icon>
                                        <van-radio :name="item.nextTargetNo"><template #icon="props">
                                                <span class="iconfont"
                                                    :class="props.checked ? activeIcon : inactiveIcon"></span>
                                            </template>
                                        </van-radio>
                                    </template>
                                </van-cell>
                            </van-cell-group>
                        </van-radio-group>
                    </div>
                    <div v-else>
                    </div>
                </van-dropdown-item>
            </van-dropdown-menu>

            <!-- 第二个下拉菜单 - 只有非重庆宝钢才显示 -->
            <div style="height: 12px" v-if="!isChongqingBaogang"></div>
            <van-dropdown-menu class="select-menu" active-color="#0000ff" v-if="!isChongqingBaogang">
                <van-dropdown-item :title="nextLoadingPoint" ref="item2">
                    <div v-if="loadList && loadList.length > 0">
                        <van-radio-group v-model="nextLoadingPointNo">
                            <van-cell-group>
                                <van-cell v-for="(item, index) in loadList" :key="index" clickable
                                    @click="isChecked2(item)">
                                    <template #title>
                                        <div class="ware-title">{{ item.handPointName }}</div>
                                    </template>
                                    <template #label>
                                        <div class="ware-name">{{ item.handPointId }}</div>
                                    </template>
                                    <template #right-icon>
                                        <van-radio :name="item.handPointId"><template #icon="props">
                                                <span class="iconfont"
                                                    :class="props.checked ? activeIcon : inactiveIcon"></span>
                                            </template>
                                        </van-radio>
                                    </template>
                                </van-cell>
                            </van-cell-group>
                        </van-radio-group>
                    </div>
                    <div v-else>
                    </div>
                </van-dropdown-item>
            </van-dropdown-menu>

            <!-- 重庆宝钢装卸点显示 -->
            <div style="height: 12px" v-if="isChongqingBaogang"></div>
            <div class="detail_row" style="background-color: #e7e7e7" v-if="isChongqingBaogang">
                <div class="fourtext2">装卸点</div>
                <div class="iconfont icon-shuxian" style="color: #d0d0d0"></div>
                <div class="fourtext1">
                    <input class="detail_input" type="text" v-model="selectedHandPointDisplay" readonly
                        :placeholder="handPointPlaceholder" />
                </div>
                <div class="fourtext2" v-if="nextTargetNo === '下一个装卸点'" @click="selectHandPoint"
                    style="color: #007aff; cursor: pointer;">
                    选择
                </div>
            </div>

            <!-- 附件上传按钮 - 只有重庆宝钢可以使用 -->
            <div style="height: 12px" v-if="isChongqingBaogang"></div>
            <div class="upload-button-section" v-if="isChongqingBaogang">
                <van-button type="default" size="large" @click="showUploadDialog = true" class="upload-button">
                    <span class="iconfont icon-fujian"></span>
                    <span>附件上传</span>
                    <span v-if="uploadedFileUrls.length > 0" class="upload-badge">{{ uploadedFileUrls.length }}</span>
                </van-button>
            </div>
        </div>
        <div class="mui-input-row3" v-show="isShowBottom">
            <button type="button" class="mui-btn3" @click="onConfirm" v-preventReClick="3000">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
            <button type="button" class="mui-btn3" @click="continuLoad('装');" v-preventReClick="3000">
                继续装货
            </button>
            <button type="button" class="mui-btn3" @click="continuLoad('卸');" v-preventReClick="3000">
                继续卸货
            </button>
            <button v-if="isChongqingBaogang" type="button" class="mui-btn3" @click="getOtherHand();"
                v-preventReClick="3000">
                其他装卸点
            </button>
        </div>

        <van-dialog v-model="isShowPonit" title="选择装卸点" show-cancel-button :beforeClose="closeHandPonit">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="handPonitRadio">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in loadList" :key="index" :title="item.handPointName"
                            :label="item.handPointId" @click="handPonitRadio = item.handPointId">
                            <template #right-icon>
                                <van-radio :name="item.handPointId" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>


        <!-- 签名板 -->
        <van-popup v-model="showSignature" position="bottom" :style="{ height: '100%' }">
            <div style="display: flex; flex-direction: column; height: 100%; width: 100%;">
                <!-- 标题 -->
                <div style="text-align: center; padding: 10px; flex-shrink: 0;">
                    <h3>{{ signatureType }}签名</h3>
                </div>
                <!-- 签名板 -->
                <div style="flex: 1; display: flex; justify-content: center; align-items: center;height: 100%;touch-action: none;"
                    @touchstart="handleTouchStart" @touchmove="handleTouchMove">
                    <vue-esign ref="signaturePad" :height="screenHeight - 200" :width="screenWidth - 10"
                        :line-width="10" :stroke-color="'black'" :background-color="'white'"
                        style="border: 1px solid;width: 100%;" />
                </div>
                <!-- 按钮 -->
                <div style="padding: 10px; flex-shrink: 0; display: flex; justify-content: space-around;">
                    <van-button type="primary" @click="saveSignature" color="green">确认签名</van-button>
                    <van-button type="danger" @click="clearSignature" color="red">清空签名</van-button>
                    <van-button @click="cancelSignature">取消</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 上传进度弹窗 -->
        <van-dialog v-model="showUploadProgress" title="上传进度" :show-cancel-button="false"
            :close-on-click-overlay="false">
            <div class="upload-progress">
                <p>正在上传图片... ({{ uploadProgressText }})</p>
                <van-progress :percentage="uploadProgress" stroke-width="8" color="#07c160" />
            </div>
        </van-dialog>

        <!-- 附件上传弹出框 -->
        <van-popup v-model="showUploadDialog" position="bottom" :style="{ height: '70%' }"
            :close-on-click-overlay="true" :safe-area-inset-bottom="true" @close="onUploadDialogClose">
            <div class="upload-popup">
                <!-- 弹窗标题 -->
                <div class="upload-popup-header">
                    <h3>附件上传</h3>
                    <span class="upload-count-text">{{ uploadSummary }}</span>
                    <van-icon name="cross" @click="closeUploadDialog" class="close-icon" />
                </div>

                <!-- 上传区域 -->
                <div class="upload-content">
                    <van-uploader v-model="uploadedImages" multiple :max-count="9" :max-size="10 * 1024 * 1024"
                        :before-read="beforeImageRead" :after-read="afterImageRead" :before-delete="beforeImageDelete"
                        accept="image/*" upload-text="选择图片" :preview-size="80">
                        <template #preview-cover="{ file }">
                            <div class="preview-cover">
                                <span class="iconfont icon-shanchu" @click.stop="deleteImage(file)"></span>
                            </div>
                        </template>
                    </van-uploader>

                    <!-- 上传提示 -->
                    <div class="upload-tips">
                        <p>• 最多可上传9张图片</p>
                        <p>• 单张图片大小不超过10MB</p>
                        <p>• 支持JPG、PNG、JPEG格式</p>
                    </div>

                    <!-- 已上传文件列表 -->
                    <div v-if="uploadedFileUrls.length > 0" class="uploaded-files">
                        <h4>已上传文件</h4>
                        <div class="file-list">
                            <div v-for="(file, index) in uploadedFileUrls" :key="index" class="file-item">
                                <span class="iconfont icon-tupian"></span>
                                <span class="file-name">{{ file.name }}</span>
                                <span class="file-time">{{ file.uploadTime }}</span>
                                <van-icon name="delete" @click="removeUploadedFile(index)" class="delete-icon" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="upload-popup-footer">
                    <van-button type="default" @click="finishUploadAndContinue" size="large">
                        完成
                    </van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script>
import { Dialog } from 'vant';
import { post } from '../../api/base-service';
import VueEsign from 'vue-esign';
export default {
    components: {
        VueEsign,
    },
    data() {
        return {
            vehicleNo: "",
            nextLoadingPointNo: "",
            nextLoadingPoint: "请选择下个装卸点",
            nextLoadingPointName: "",
            loadList: [],
            w: document.documentElement.clientWidth, //实时屏幕宽度
            h: document.documentElement.clientHeight, //实时屏幕高度
            isShowBottom: true, //显示或者隐藏footer
            documentHeight: document.documentElement.clientHeight,
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            handPointId: '',
            carTraceNo: '',
            nextTarget: '请选择下一个目标点',
            nextTargetList: [], // 将在created中根据用户类型动态设置
            nextTargetNo: '',
            showSignature: false, // 控制签名板显示
            signatureData: "", // 存储签名数据
            signatureType: '发货人',
            screenHeight: document.documentElement.clientHeight, // 动态屏幕高度
            screenWidth: document.documentElement.clientWidth,
            unloadUrl: localStorage.getItem('segNo') == 'KF000000' ? '/unload' : '/unload-cq',
            isShowPonit: false,
            handPonitRadio: '',
            loadId: '',
            uploadedImages: [], // 已上传的图片列表
            uploadProgress: 0, // 上传进度
            uploadProgressText: '', // 上传进度文本
            showUploadProgress: false, // 显示上传进度弹窗
            uploadedFileUrls: [], // 存储上传成功的文件URL
            showUploadDialog: false, // 控制附件上传对话框的显示
            isUploadTriggeredByConfirm: false, // 标识是否由确认按钮触发的附件上传
            isChongqingBaogang: false, // 是否为重庆宝钢，控制附件上传功能显示
            selectedHandPointDisplay: '', // 重庆宝钢选中的装卸点显示文本
            handPointPlaceholder: '请先选择目标点', // 装卸点输入框占位符
        };
    },
    computed: {
        // 附件上传统计
        uploadSummary() {
            const count = this.uploadedFileUrls.length;
            if (count === 0) {
                return '未上传附件';
            }
            return `已上传${count}个附件`;
        }
    },
    created() {
        this.vehicleNo = localStorage.getItem('carNumber');
        this.carTraceNo = localStorage.getItem('carTraceNo');
        this.handPointId = localStorage.getItem('handPointId');
        this.loadId = localStorage.getItem('loadId');

        // 判断是否为重庆宝钢，只有重庆宝钢才显示附件上传功能
        const segNo = localStorage.getItem('segNo');
        this.isChongqingBaogang = segNo && segNo !== 'KF000000';

        // 根据是否为重庆宝钢设置不同的目标选项
        if (this.isChongqingBaogang) {
            // 重庆宝钢用户显示"完成"
            this.nextTargetList = [{
                nextTargetNo: '下一个装卸点',
                nextTargetName: '下一个装卸点'
            }, {
                nextTargetNo: '完成',
                nextTargetName: '完成'
            }];
        } else {
            // 非重庆宝钢用户显示"离厂"
            this.nextTargetList = [{
                nextTargetNo: '下一个装卸点',
                nextTargetName: '下一个装卸点'
            }, {
                nextTargetNo: '离厂',
                nextTargetName: '离厂'
            }];
        }
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                if (this.documentHeight > document.documentElement.clientHeight) {
                    this.isShowBottom = false;
                } else {
                    this.isShowBottom = true;
                }
            })();
        };
        this.updateScreenSize(); // 初始化调用
        window.addEventListener('resize', this.updateScreenSize);

    },
    beforeDestroy() {
        // 移除监听器
        window.removeEventListener("resize", this.updateScreenSize);
    },
    methods: {
        handleTouchStart(e) {
            e.preventDefault(); // 阻止默认行为
        },
        handleTouchMove(e) {
            e.preventDefault(); // 阻止默认滚动
        },

        updateScreenSize() {
            this.screenHeight = document.documentElement.clientHeight;
            this.screenWidth = document.documentElement.clientWidth;

        },
        onClickLeft() {
            this.$router.replace({
                name: 'index',
            });
        },
        isChecked2(item) {
            this.nextLoadingPoint = item.handPointName;
            this.nextLoadingPointName = item.handPointName;
            this.nextLoadingPointNo = item.handPointId;
            console.log(item);

            // 重庆宝钢用户更新装卸点显示
            if (this.isChongqingBaogang) {
                this.selectedHandPointDisplay = `${item.handPointName} (${item.handPointId})`;
            }

            // 只有非重庆宝钢才调用 toggle 方法，因为重庆宝钢隐藏了第二个下拉菜单
            if (!this.isChongqingBaogang && this.$refs.item2) {
                this.$refs.item2.toggle();
            }
        },

        // 清空签名
        clearSignature() {
            this.$refs.signaturePad.reset();
        },

        // 取消签名
        cancelSignature() {
            this.showSignature = false; // 关闭签名板
        },

        // 保存签名
        saveSignature() {
            const signaturePad = this.$refs.signaturePad;

            if (signaturePad) {
                // 获取签名数据（Base64 图片格式）
                signaturePad.generate()
                    .then((res) => {
                        this.signatureData = res; // 得到了签字生成的base64图片
                        this.submitSignature();
                    })
                    .catch(() => {
                        // 没有签名，点击生成图片时调用
                        this.$toast("未签名!");
                    });

                // 将签名数据提交到后端或其他业务逻辑
                // this.submitSignature();
            } else {
                this.$toast("请先完成签名！");
            }
        },

        /**
         * 确定选择装卸点
         * @param action 
         * @param done 
         */
        closeHandPonit(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.handPonitRadio) {
                Dialog.alert({ message: '请选择装卸点' });
                done(false);
                return;
            }

            const currentData = this.loadList.find(p => p.handPointId == this.handPonitRadio);
            this.nextTarget = '下一个装卸点';
            this.nextTargetNo = '下一个装卸点';
            this.isChecked2(currentData);
            done();
        },

        // 提交签名数据
        async submitSignature() {
            const isSignatureType = this.signatureType.includes('发货人');
            const params = {
                serviceId: "S_LI_RL_0049",
                file: this.signatureData,
                signatureMark: '1',
                id: this.carTraceNo,
                id2: [this.carTraceNo],
                type: isSignatureType ? '1' : '2',
            };

            const result = await post(params);
            if (result && result.__sys__.status == 1) {
                // 司机签名
                if (isSignatureType) {
                    Dialog.alert({ message: '发货人签名成功, 请司机签名' });
                    this.signatureType = '司机';
                    this.clearSignature();
                    return;
                }
                this.showSignature = false; // 关闭签名板
                const result = await this.endLoad();
                if (!result) {
                    return;
                }

                this.$router.togo(this.unloadUrl);
            }
        },

        /**
         * 其他装卸点
         */
        async getOtherHand() {
            await this.getOtherLoadList(false);
            if (this.loadList.length == 0) {
                return;
            }
            this.isShowPonit = true;
        },

        /**
         * 继续装卸货
         */
        async continuLoad(type) {
            // 检查是否有附件正在上传
            if (this.showUploadProgress) {
                this.$toast("请等待附件上传完成!");
                return;
            }

            // 更新loadingType缓存
            localStorage.setItem('loadingType', type);

            const scanUrl = localStorage.getItem('segNo').includes('JC') ? "scan-store-cq" : "scan-store";

            this.$router.togo({
                name: type.includes('卸') ? scanUrl : 'in-storage',
                params: {
                    carNumber: this.vehicleNo,
                    carTraceNo: this.carTraceNo,
                    handPointId: this.nextLoadingPointNo,
                },
            });
        },

        async isChecked(item) {
            this.nextTarget = item.nextTargetNo;
            this.nextTargetNo = item.nextTargetNo;
            this.$refs.item.toggle();
            if (item.nextTargetNo.includes('完成') || item.nextTargetNo.includes('离厂')) {
                const leavFactory = {
                    handPointName: item.nextTargetNo,
                    handPointId: item.nextTargetNo,
                };
                this.loadList = [leavFactory];
                this.isChecked2(leavFactory);
                return;
            }

            this.nextLoadingPoint = '请选择下个装卸点';
            this.nextLoadingPointName = '';
            this.nextLoadingPointNo = '';

            // 重庆宝钢用户更新装卸点显示状态
            if (this.isChongqingBaogang) {
                this.selectedHandPointDisplay = '';
                this.handPointPlaceholder = '请选择装卸点';
            }

            // 选择的是下一个装卸点
            await this.getLoadList();

            // 如果是重庆宝钢，直接弹出装卸点选择对话框
            if (this.isChongqingBaogang && this.loadList.length > 0) {
                this.isShowPonit = true;
            }
        },

        //查询装卸点  先选择仓库再选择装卸点
        async getLoadList(isJudg = true) {
            if (this.nextTargetNo.includes('完成') || this.nextTargetNo.includes('离厂')) {
                return;
            }

            if (this.nextTarget.includes('请选择') && isJudg) {
                this.$toast("请先选择目标点!");
                return;
            }
            let params = {
                carTraceNo: this.carTraceNo,
                vehicleNo: this.vehicleNo,
                serviceId: 'S_LI_RL_0169',
                allocateVehicleNo: localStorage.getItem('allocateVehicleNo'),

            };
            if (localStorage.getItem('segNo').includes('KF')) {
                params = {
                    serviceId: 'S_LI_RL_0051',
                    factoryArea: localStorage.getItem('factoryAreaTrue'),
                    factoryBuilding: localStorage.getItem('factoryBuilding'),
                }
            }

            const dataObj = await post(params);

            this.loadList = dataObj.list || [];

            // 如果是重庆宝钢且没有返回装卸点，显示提示
            if (this.isChongqingBaogang && this.loadList.length === 0) {
                this.$toast("未查询到可用的装卸点，请联系管理员");
                this.handPointPlaceholder = '暂无可用装卸点';
            }
        },

        async getOtherLoadList(isJudg = true) {
            if (this.nextTarget.includes('请选择') && isJudg) {
                this.$toast("请先选择目标点!");
                return;
            }
            let params = {
                serviceId: 'S_LI_RL_0051',
            };

            const dataObj = await post(params);

            this.loadList = dataObj.list;
        },

        /**
         * 确认按钮
         */
        async onConfirm() {
            if (!this.nextLoadingPointNo) {
                const errorMsg = this.isChongqingBaogang ? "请选择装卸点!" : "请选择下个装卸点!";
                this.$toast(errorMsg);
                return;
            }

            // 自动上传，只有卸货才能调用
            const loadingType = localStorage.getItem('loadingType');
            if (loadingType && loadingType.includes('卸')) {
                const needUploadResult = await this.checkAttachmentRequirement();
                if (needUploadResult == false) {
                    return; // 检查失败，停止执行
                }

                if (needUploadResult == 'pending') {
                    this.showUploadDialog = true;
                    return; // 用户选择上传附件，等待上传完成
                }
            }

            // 继续原有逻辑
            await this.proceedWithConfirm();
        },

        /**
         * 自动打印
         */
        async checkAttachmentRequirement() {
            // 只有重庆宝钢才需要检查附件上传需求
            if (!this.isChongqingBaogang) {
                return 'continue';
            }

            try {
                const params = {
                    serviceId: 'S_LI_RL_0183',
                    carTraceNo: this.carTraceNo,
                    allocateVehicleNo: localStorage.getItem('allocateVehicleNo'),
                };

                const result = await post(params);

                if (result && result.__sys__.status == 1) {
                    return 'pending';
                }

                return 'continue'; // 没有消息或接口调用失败，继续后续逻辑
            } catch (error) {
                console.error('检查附件需求失败:', error);
                return 'continue'; // 出错时继续后续逻辑
            }
        },

        /**
         * 执行确认的后续逻辑
         */
        async proceedWithConfirm() {
            // 检查是否有附件正在上传
            if (this.showUploadProgress) {
                this.$toast("请等待附件上传完成!");
                return;
            }

            const loadingType = localStorage.getItem('loadingType');
            // 如果是离厂, 需要签名
            if ((this.nextLoadingPointName.includes('完成') || this.nextLoadingPointName.includes('离厂')) && loadingType && loadingType.includes('装') && localStorage.getItem('segNo').includes('KF')) {
                this.showSignature = true; // 打开签名板
                return;
            }

            // 调用接口
            const result = await this.endLoad();
            if (!result) {
                return;
            }

            this.$router.togo(this.unloadUrl);
        },

        /**
         * 结束装卸货
         */
        async endLoad() {
            const segNo = localStorage.getItem('segNo');
            // 调用接口
            const params = {
                serviceId: segNo.includes('KF') ? 'S_LI_RL_0011' : 'S_LI_RL_0081',
                carTraceNo: this.carTraceNo,
                targetHandPointId: this.nextLoadingPointNo,
                nextTarget: (this.nextLoadingPointName.includes('完成') || this.nextLoadingPointName.includes('离厂')) ? '20' : '10',
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                // 重庆多传一个配单号
                allocateVehicleNo: localStorage.getItem('allocateVehicleNo'),
                loadId: this.loadId,

            };
            const result = await post(params);
            if (!result || result.__sys__.status != 1) {
                return false;
            }
            return true;
        },

        beforeImageRead(file) {
            // 验证文件类型
            const isImage = file.type.startsWith('image/');
            if (!isImage) {
                this.$toast('请选择图片文件');
                return false;
            }

            // 验证文件大小 - 限制为10MB
            const isLt10M = file.size < 10 * 1024 * 1024;
            if (!isLt10M) {
                this.$toast('图片大小不能超过10MB');
                return false;
            }

            return true;
        },

        async afterImageRead(file) {
            // 文件读取成功后直接上传
            if (Array.isArray(file)) {
                // 多文件上传
                for (let i = 0; i < file.length; i++) {
                    await this.uploadSingleFile(file[i]);
                }
            } else {
                // 单文件上传
                await this.uploadSingleFile(file);
            }
        },

        async uploadSingleFile(file) {
            this.showUploadProgress = true;
            this.uploadProgress = 0;

            const currentIndex = this.uploadedImages.findIndex(item => item.file === file.file) + 1;
            const totalCount = this.uploadedImages.length;
            this.uploadProgressText = `正在上传... (${currentIndex}/${totalCount})`;

            try {
                // 将文件转换为base64
                const base64 = await this.fileToBase64(file.file);

                // 准备上传参数
                const params = {
                    serviceId: 'S_LI_RL_0197',
                    file: base64,
                    id: this.carTraceNo, // 车辆跟踪号
                    fileName: file.file.name,
                    fileType: file.file.type,
                    signatureMark: '1',
                };

                // 模拟上传进度
                const progressInterval = setInterval(() => {
                    if (this.uploadProgress < 90) {
                        this.uploadProgress += 10;
                    }
                }, 100);

                // 调用上传接口
                const result = await post(params);

                clearInterval(progressInterval);
                this.uploadProgress = 100;

                if (result && result.__sys__.status == 1) {
                    // 上传成功，保存文件URL
                    this.uploadedFileUrls.push({
                        url: result.fileUrl || '',
                        name: file.file.name,
                        id: result.fileId || '',
                        uploadTime: new Date().toLocaleString(),
                    });

                    this.$toast('图片上传成功');
                } else {
                    const errorMsg = (result && result.__sys__ && result.__sys__.msg) || '图片上传失败，请重试';
                    this.$toast(errorMsg);
                    // 上传失败，从列表中移除
                    const index = this.uploadedImages.findIndex(item => item.file === file.file);
                    if (index > -1) {
                        this.uploadedImages.splice(index, 1);
                    }
                }
            } catch (error) {
                console.error('上传失败:', error);
                this.$toast('图片上传失败，请检查网络连接后重试');
                // 上传失败，从列表中移除
                const index = this.uploadedImages.findIndex(item => item.file === file.file);
                if (index > -1) {
                    this.uploadedImages.splice(index, 1);
                }
            } finally {
                setTimeout(() => {
                    this.showUploadProgress = false;
                }, 1000);
            }
        },

        fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        },

        /**
         * 压缩图片
         * @param {File} file 原始图片文件
         * @param {number} quality 压缩质量 0-1
         * @param {number} maxWidth 最大宽度
         * @param {number} maxHeight 最大高度
         * @returns {Promise<File>} 压缩后的文件
         */
        compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    // 计算压缩后的尺寸
                    let { width, height } = img;

                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }

                    // 设置画布尺寸
                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片到画布
                    ctx.drawImage(img, 0, 0, width, height);

                    // 将画布内容转换为Blob
                    canvas.toBlob((blob) => {
                        // 创建新的File对象
                        const compressedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now()
                        });

                        console.log(`图片处理完成: ${(file.size / 1024 / 1024).toFixed(2)}MB -> ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
                        resolve(compressedFile);
                    }, file.type, quality);
                };

                img.onerror = () => {
                    console.error('图片加载失败，使用原始文件');
                    resolve(file);
                };

                // 加载图片
                img.src = URL.createObjectURL(file);
            });
        },

        beforeImageDelete(file, detail) {
            return new Promise((resolve) => {
                Dialog.confirm({
                    title: '确认删除',
                    message: '确定要删除这张图片吗？',
                })
                    .then(() => {
                        // 从已上传文件列表中移除
                        const fileName = (file.file && file.file.name) || file.name;
                        const index = this.uploadedFileUrls.findIndex(item => item.name === fileName);
                        if (index > -1) {
                            this.uploadedFileUrls.splice(index, 1);
                        }
                        resolve(true);
                    })
                    .catch(() => {
                        resolve(false);
                    });
            });
        },

        deleteImage(file) {
            // 这个方法在模板中的删除按钮中使用
            const fileName = (file.file && file.file.name) || file.name;
            const index = this.uploadedImages.findIndex(item =>
                ((item.file && item.file.name) || item.name) === fileName
            );
            if (index > -1) {
                this.uploadedImages.splice(index, 1);

                // 同时从上传文件列表中移除
                const urlIndex = this.uploadedFileUrls.findIndex(item => item.name === fileName);
                if (urlIndex > -1) {
                    this.uploadedFileUrls.splice(urlIndex, 1);
                }
            }
        },

        // 移除已上传的文件
        removeUploadedFile(index) {
            Dialog.confirm({
                title: '确认删除',
                message: '确定要删除这个附件吗？',
            })
                .then(() => {
                    const fileName = this.uploadedFileUrls[index].name;

                    // 从已上传列表中移除
                    this.uploadedFileUrls.splice(index, 1);

                    // 从上传组件中移除
                    const imageIndex = this.uploadedImages.findIndex(item =>
                        ((item.file && item.file.name) || item.name) === fileName
                    );
                    if (imageIndex > -1) {
                        this.uploadedImages.splice(imageIndex, 1);
                    }

                    this.$toast('附件删除成功');
                })
                .catch(() => {
                    // 用户取消删除
                });
        },

        /**
         * 完成附件上传并继续确认流程
         */
        finishUploadAndContinue() {
            this.showUploadDialog = false;
            this.isUploadTriggeredByConfirm = false;
            // 延迟执行，确保弹窗完全关闭
            this.$nextTick(() => {
                this.proceedWithConfirm();
            });
        },

        /**
         * 上传对话框关闭事件
         */
        onUploadDialogClose() {
            if (this.isUploadTriggeredByConfirm) {
                this.isUploadTriggeredByConfirm = false;
                // 延迟执行，确保弹窗完全关闭
                this.$nextTick(() => {
                    this.proceedWithConfirm();
                });
            }
        },

        /**
         * 关闭上传对话框
         */
        closeUploadDialog() {
            this.showUploadDialog = false;
        },

        /**
         * 选择装卸点 - 重庆宝钢专用
         */
        selectHandPoint() {
            if (this.nextTargetNo !== '下一个装卸点') {
                this.$toast("请先选择下一个装卸点");
                return;
            }

            if (this.loadList.length === 0) {
                this.$toast("暂无可用的装卸点");
                return;
            }

            this.isShowPonit = true;
        },
    },
};
</script>

<style lang="less" scoped>
/deep/ .van-dropdown-menu__item {
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
}

.factory-body {
    height: 100vh;
    background-color: #f3f3f3;
}

.ware-title {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #007aff;
    line-height: 20px;
}

.ware-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #333333;
    line-height: 22px;
}

.activeColor {
    color: #007aff;
}

.canvans-tip {
    width: 100px;
    font-size: 15px;
    z-index: 10;
    left: 200px;
    position: fixed;
    bottom: 100px;
    transform: rotate(90deg);
}

.page-content {
    padding: 10px;
    background-color: #f1f1f1;

    .content {
        border: 1px solid #f1f1f1;
    }

    .sign-btn {
        margin: 10px;
        display: flex;
        justify-content: space-around;
        align-content: center;
    }
}

.van-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.vue-esign {
    // width: 100%;
    // height: 100%;
    touch-action: none;
    /* 禁用触摸屏默认行为 */
    border: 1px solid #000;
}

.vue-esign canvas {
    aspect-ratio: auto;
    height: 100%;
    /* 强制占满父容器 */
    width: 100%;
}

/* 图片上传相关样式 */
.upload-section {
    background: #fff;
    margin: 0 10px;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.section-title .iconfont {
    margin-right: 8px;
    font-size: 18px;
    color: #007aff;
}

.upload-count {
    margin-left: auto;
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.upload-tips {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007aff;
}

.upload-tips p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.5;
}

.preview-cover {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0 0 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-cover .iconfont {
    color: white;
    font-size: 12px;
}

.upload-progress {
    padding: 20px;
    text-align: center;
}

.upload-progress p {
    margin-bottom: 15px;
    color: #333;
    font-size: 14px;
}

/* 附件上传弹出框样式 */
.upload-popup {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    border-radius: 20px 20px 0 0;
}

.upload-popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;
}

.upload-popup-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.upload-count-text {
    font-size: 12px;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
}

.close-icon {
    font-size: 20px;
    color: #999;
    cursor: pointer;
}

.upload-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.uploaded-files {
    margin-top: 20px;
}

.uploaded-files h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.file-list {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #fff;
    margin-bottom: 8px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-item .iconfont {
    color: #007aff;
    font-size: 16px;
    margin-right: 10px;
}

.file-name {
    flex: 1;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-time {
    font-size: 12px;
    color: #999;
    margin-right: 10px;
}

.delete-icon {
    color: #ff4444;
    font-size: 16px;
    cursor: pointer;
}

.upload-popup-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    flex-shrink: 0;
}

.upload-button-section {
    text-align: center;
    padding: 10px;
}

.upload-button {
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    transition: all 0.3s;
    position: relative;
}

.upload-button:hover {
    background-color: #007aff;
    color: #fff;
}

.upload-button .iconfont {
    margin-right: 8px;
    font-size: 18px;
}

.upload-badge {
    background-color: #ff4444;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    margin-left: 8px;
    min-width: 16px;
    text-align: center;
}

/deep/ .van-uploader__upload {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    color: #666;
    transition: all 0.3s;
}

/deep/ .van-uploader__upload:hover {
    border-color: #007aff;
    color: #007aff;
}

/deep/ .van-uploader__upload-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

/deep/ .van-uploader__upload-text {
    font-size: 14px;
}
</style>