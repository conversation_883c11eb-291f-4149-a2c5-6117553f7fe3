<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">实绩补录</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <van-cell-group>
                <van-cell title="行车实绩清单号" :value="data.craneResultId" />
                <!-- <van-cell title="类型" :value="data.craneResultId" /> -->
                <van-field v-model="packId" label="捆包" placeholder="请输入捆包号" input-align="right" ref="packRef"
                    @keyup.enter.native="getPack" />
            </van-cell-group>

            <div style="height: 65vh;">
                <van-divider content-position="left">捆包信息</van-divider>

                <!-- 修改这里：用 swipe-cell 包裹 -->
                <div style="height: 58vh;overflow-y: auto;">
                    <van-swipe-cell v-for="(item, index) in list" :key="index" >
                        <van-cell-group inset>
                            <van-cell title="捆包号" :value="item" />
                        </van-cell-group>
                        <template #right>
                            <van-button square type="danger" class="delete-button" text="删除"
                                @click="handleDelete(index)" />
                        </template>
                    </van-swipe-cell>
                </div>
            </div>

            <div class="foot-sticky">
                <van-button type="info" class="foot-sticky-btn" @click="onConfrimCheck">补录</van-button>
            </div>
        </div>

    </div>
</template>

<script>
import { post } from '../../api/base-service';
import {
    Dialog
} from "vant";
export default {
    data() {
        return {
            data: '',
            packId: '',
            list: [],
            abnormalFlag: '1',
        };
    },
    created() {
        this.data = this.$route.params.data;
        this.abnormalFlag = this.$route.params.abnormalFlag;
        this.$nextTick((x) => {
            this.$refs.packRef.focus();
        });
    },

    beforeRouteLeave(to, from, next) {
        to.meta.keepAlive = true;
        next(0);
    },
    methods: {
        onClickLeft() {
            this.$router.goBack();
        },

        getPack() {
            if (!this.packId) {
                Dialog.alert({
                    title: '提示',
                    message: '请扫描捆包',
                });
                return;
            }
            this.list.unshift(this.packId);
            this.packId = '';
        },

        // 添加删除方法
        handleDelete(index) {
            this.list.splice(index, 1);
        },

        async onConfrimCheck() {
            if (this.list.length == 0) {
                Dialog.alert({
                    title: '提示',
                    message: '请扫描捆包',
                });
                return;
            }

            const params = {
                serviceId: 'S_LI_DS_0009',
                craneResultId: this.data.craneResultId,
                craneId: this.data.craneId,
                packIdList: this.list,
                abnormalFlag: this.abnormalFlag,
            };
            const res = await post(params);
            if (!res || res.__sys__.status == -1) {
                return;
            }
            this.$toast("录入成功");
            this.list = [];

            console.log(res);
            
        },

    }
};
</script>

<style>
/* 添加删除按钮样式 */
.delete-button {
    height: 100%;
}
</style>