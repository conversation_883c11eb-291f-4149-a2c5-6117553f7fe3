.all-font-size {
  font-size: 18px;
  font-size-Adiust: 1.5;
  /* 设置适应屏幕大小的字体大小*/
}

.mui-content {
  margin: 0;
  padding: 0;
}

.new-field {
  display: block;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  margin: 0;
  padding: 0;
  color: #323233;
  line-height: inherit;
  text-align: left;
  background-color: transparent;
  border: 0;
  resize: none;
}

.detail_row {
  height: 48px;
  background: #FFFFFF;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.detail_row2 {
  height: 48px;
  background: #FFFFFF;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.detail_row3 {
  height: 95px;
  background: #FFFFFF;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.detail_row4 {
  height: 120px;
  background: #FFFFFF;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  padding-right: 10px;
}

.detail_textarea {
  margin-top: 8px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
}

.detail_textarea2 {
  margin-top: 8px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
}

.detail_text {
  display: flex;
  flex-direction: row;
}

.detail_text1 {
  height: 9px;
}

.detail_text2 {
  margin-right: auto;
  display: flex;
  flex-direction: column;
}

.fourtext {
  height: 23px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  width: 20%;
  /* width: 64px; */
  margin-left: 16px;
  margin-right: auto;
}

.fourtext2 {
  width: 30%;
  height: 23px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  margin-left: 16px;
  margin-right: auto;
}

.baletext {
  width: 100%;
}

.baletext3 {
  width: 80%;
}

.baletext2 {
  width: 70%;
  height: 23px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  margin-left: 16px;
  margin-right: auto;
}

.specdes {
  height: 30px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  font-size: 16px;
  width: 64px;
  margin-left: 16px;
  margin-right: auto;
  margin-bottom: 6px;
}

.spec-text {
  width: 200px;
  height: 30px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000FF;
  line-height: 22px;
  font-size: 16px;
  padding-left: 5px;
  margin-bottom: 6px;
  background: #F3F3F3;
  border-radius: 4px 4px 4px 4px;
}

.fourtext3 {
  height: 23px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  font-size: 16px;
  margin-right: auto;
  margin-left: 5%;
  margin-top: 7px;
  margin-bottom: 6px;
  color: #0000FF;
  font-size: 19px;
  line-height: 27px;
  height: 28px;
}

.icon-line {
  color: #E4E4E4;
}

.fourline-gray {
  width: 3px;
  height: 16px;
  opacity: 1;
  width: 1px;
  border: 1px solid #E4E4E4;
}

.fourline-blue {
  margin-left: 16px;
  margin-right: 6px;
  margin-top: 19px;
  width: 3px;
  height: 14px;
  background: #007AFF;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.fourtext1 {
  width: 70%;
}



.right-part {
  width: 70%;

}

.right-total {
  width: 300px;
  margin-left: 20px;
}

.right-text {
  font-size: 20px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #D33017;
  line-height: 28px;
}

.detail_input {
  border: #FFFFFF;
  margin-left: 28px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
  width: 88%;
}

.weight_input {
  width: 30%;
  height: 34px;
  /* /margin-left: 10px; */
  margin-left: 10px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
  background: #F3F3F3;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.total-flex {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.span-total {
  width: 116px;
  height: 34px;
  font-size: 24px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #007AFF;
  line-height: 34px;
  padding-left: 5px;
  background: #F3F3F3;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.span-des {
  /* width: 16px; */
  height: 23px;
  margin-left: 11px;
  font-size: 16px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}

.search_input {
  background-color: #F3F3F3;
  border: #FFFFFF;
  margin: 0 4%;
  font-size: 16px;
  padding-left: 3%;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 36px;
  width: 89%;
  border-radius: 4px 4px 4px 4px;
}

.in-content {
  padding-bottom: 100px;
  text-align: left;
}

.company-btn {
  position: fixed;
  bottom: 0px;
  margin: 25px 16px 25px 16px;
  width: 328px;
  height: 48px;
  background: #007aff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
  letter-spacing: 6px;
}

/* input输入框的样式 */
input {
  background: none;
  outline: none;
  border: none;
}

input:focus {
  border: none;
}

input:focus {
  border: none;
}

input::-webkit-input-placeholder {
  /* WebKit browsers*/
  height: 22px;
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #999999;
  line-height: 21px;

}

textarea {
  border: none;
  outline: none;
  resize: none;
}

textarea::-webkit-input-placeholder {
  /* WebKit browsers*/
  height: 22px;
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #999999;
  line-height: 21px;

}

.quality_desc {
  font-weight: 400;
  height: 98px;
  background: #F3F3F3;
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  padding: 7px 0px 7px 9px;
  margin: 0;
}

.quality_desc_unuse {
  font-weight: 400;
  height: 98px;
  background: #D0D0D0;
  width: 100%;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  padding: 7px 0px 7px 9px;
  margin: 0;
}

.quality_desc_textarea {
  margin: 9px 25px 20px 16px;
}

button {
  border: none;
  outline: none;
  resize: none;
}

/* 底部按钮 */
.mui-btn {
  width: 100%;
  height: 48px;
  background: #007AFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 28px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

/* 底部按钮 */
.mui-delete {
  width: 100%;
  height: 48px;
  background: #d33017;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 28px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

/* 底部按钮 */
.confirm-btn {
  position: fixed;
  bottom: 0px;
  width: 100%;
  height: 48px;
  background: #007aff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
  letter-spacing: 6px;
  z-index: 3;
}

.load-btn {
  width: 100%;
  height: 48px;
  background: #007AFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 10px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

.mui-input-row {
  background-color: #F3F3F3;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  width: 90%;
  margin: 0;
  padding: 8px 5% 0 5%;
  margin-bottom: 1em;

}

/* 底部左右两个按钮 */
.mui-input-row1 {
  overflow: hidden;
  position: fixed;
  top: 60%;
  width: 100%;
  margin: 0 5%;

}

.mui-btn1 {
  width: 45%;
  height: 48px;
  background: #007AFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 28px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

.mui-btn2 {
  width: 90%;
  height: 48px;
  background: #007AFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 28px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

.mui-input-row3 {
  background-color: #F3F3F3;
  overflow: hidden;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 8px 5% 0 5%;

}

.mui-btn3 {
  width: 90%;
  height: 48px;
  background: #007AFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-bottom: 28px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #FFFFFF;
  font-size: 16px;
  line-height: 22px;
}

/* 选中标识 */
.check-style {
  width: 7px;
  height: 14px;
  border-color: #007AFF;
  border-style: solid;
  border-width: 0 2px 3px 0;
  transform: rotate(45deg);
  margin-right: 10px;
}

/* 选中标识 */
.check-style2 {
  width: 7px;
  height: 14px;
  border-color: #fff;
  border-style: solid;
  border-width: 0 2px 3px 0;
  transform: rotate(45deg);
  margin-right: 10px;
}

/* 查询标识 */
.search.icon {
  color: #000;
  position: absolute;
  left: 30%;
  margin-top: 3px;
  width: 12px;
  height: 12px;
  border: solid 2px #FFFFFF;
  border-radius: 100%;
  -webkit-transform: rotate(-45 deg);
  transform: rotate(-45 deg);
}

.search.icon:before {
  content: '';
  position: absolute;
  top: 9px;
  left: 14px;
  height: 7px;
  width: 2px;
  background-color: #FFFFFF;
  transform: rotate(-45deg);
}

/* 右箭头样式 */

.mui_right_arrow1 {
  position: absolute;
  left: 86%;
  ;
  width: 8px;
  height: 8px;
  border-top: 1px solid #999999;
  border-right: 1px solid #999999;
  transform: rotate(45deg);
}

.mui-scroll-wrapper {
  display: none;
  border-right: 1px solid #F3F3F3;
  border-left: 1px solid #F3F3F3;
  border-bottom: 1px solid #F3F3F3;
  z-index: 9999;
  background-color: #FFFFFF;
  height: 135px;
  width: 50%;
  position: absolute;
  top: 145px;
  margin-left: 15px;
  border-radius: 4px 4px 4px 4px;
  padding-right: 5%;
  overflow: hidden;
  max-height: 200px;
  overflow-y: scroll;
}

.mui-scroll-wrapper1 {
  display: none;
  border-right: 1px solid #F3F3F3;
  border-left: 1px solid #F3F3F3;
  border-bottom: 1px solid #F3F3F3;
  z-index: 9999;
  background-color: #FFFFFF;
  height: 135px;
  width: 85%;
  position: absolute;
  top: 145px;
  margin-left: 5%;
  padding-right: 5%;
  border-radius: 4px 4px 4px 4px;
  overflow: hidden;
  max-height: 200px;
  overflow-y: scroll;
}

.mui-scroll-wrapper2 {
  border-right: 1px solid #F3F3F3;
  border-left: 1px solid #F3F3F3;
  border-bottom: 1px solid #F3F3F3;
  background-color: #FFFFFF;
  height: 70%;
  width: 95%;
  position: absolute;
  border-radius: 4px 4px 4px 4px;
  overflow: hidden;
  max-height: 70%;
  overflow-y: scroll;
}

.mui-table-view-cell {
  display: flex;
  justify-content: center;
  list-style: none;
  line-height: 30px;
  border-bottom: 1px solid #F3F3F3;
  margin-left: -30px;
}

.mui-table-view {
  margin-top: 5px;
  height: auto;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
}

/* 取消符号 */
.cha {
  width: 15px;
  height: 15px;
  position: relative;
  left: 85%;
  border: 1px solid #A7A7A7;
  border-radius: 10px 10px 10px 10px;
}

.cha::before,
.cha::after {
  content: "";
  position: absolute;
  margin-left: 7px;
  /*方便进行定位*/
  height: 10px;
  width: 1px;
  top: 2px;
  /*设置top和right使图像在20*20框中居中*/
  background: #A7A7A7;

}

.cha::before {
  transform: rotate(45deg);
  /*进行旋转*/
}

.cha::after {
  transform: rotate(-45deg);
}

/* 卡片样式 */

.area-header {
  margin-left: 16px;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 1;
  height: 2em;
}

.nav-baling {
  width: 3px;
  height: 14px;
  background: #007AFF;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

/* 删除按钮 */
.btn_delete {
  width: 68px;
  height: 32px;
  background: #FFF2F0;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #D33017;
  font-size: 14px;
  font-weight: 400;
  color: #D33017;
  line-height: 20px;
  margin-top: 5px;
}

/* 修改按钮 */
.btn_update {
  width: 72px;
  height: 32px;
  background: #F8FBFF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #007AFF;
  font-size: 14px;
  font-weight: 400;
  color: #007AFF;
  line-height: 20px;
  margin-top: 5px;
}

/* 卡片顶部的线 */
.border_top {
  width: 90%;
  margin-left: 5%;
}

.content_div {
  display: flex;
  flex-direction: row;
  margin-bottom: 1px;
  font-size: 16px;
  font-family: Noto Sans SC;
}

.content_div2 {
  display: flex;
  flex-direction: row;
  margin-top: 4px;
  margin-bottom: 1px;
  font-size: 16px;
  font-family: Noto Sans SC;
  justify-content: space-between;
}

.content_div4 {
  display: flex;
  flex-direction: row;
  margin-top: 4px;
  margin-bottom: 1px;
  font-size: 16px;
  font-family: Noto Sans SC;
  justify-content: space-between;
}

.content_div3 {
  display: flex;
  flex-direction: row;
  margin-top: 4px;
  margin-bottom: 1px;
  height: 56px;
  justify-content: space-between;
}

/* 对号的样式 */
.check-style-unequal-width {
  font-weight: 500;
  margin: 20px 14px;
  width: 8px;
  height: 13px;
  transform: rotate(45deg);
  border-style: solid;
  border-color: #007AFF;
  border-width: 0 2px 3px 0;
  display: none;

}


/* 滑动删除 */

.list::-webkit-scrollbar {
  display: none;
}

.list {
  display: flex;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
}

.content {
  flex: 0 0 100vw;
  background-color: #fff;
  position: relative;
  scroll-snap-align: start;
}

.space {
  flex: 0 0 4rem;
  scroll-snap-align: end;
}

.button {
  height: 111px;
  margin: 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #D33017;
  color: #fff;
  width: 4rem;
  position: absolute;
  right: 0;
}

/* 班组 */
.group-content {
  /* padding: 6px 16px 0 16px; */
  background-color: #fff;
  /* margin-bottom: 12px; */
  margin: 6px 16px 0 16px;
}

.group-line {
  width: 3px;
  height: 14px;
  background: #007aff;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.group-flex {
  display: flex;
  align-items: center;
}

.group-title {
  font-size: 16px;
  margin-left: 20px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}

.group-choose2 {
  margin-top: 10px;
  margin-left: 16px;
  display: flex;
}

.span-div {
  margin-left: 10px;
  width: 40%;
}

.choose-text1 {
  width: 20%;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  font-family: Noto Sans SC;
}

.choose-text2 {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #666666;
  line-height: 22px;
}

.check-text {
  font-size: 18px;
  font-family: Noto Sans SC;
  font-weight: 500;
  line-height: 22px;
  color: #007aff;
  margin-left: 5px;
}
