<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">车辆装卸示意图</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="box">
      <div class="prompt">
        图示：<span>红色--自带货</span>&nbsp;&nbsp;&nbsp;
        <span>黑色：未装卸</span>&nbsp;&nbsp;&nbsp;
        <span>蓝色：已装卸</span>
      </div>
    </div>

    <div style="margin-top: 12px;height: 80%;
    overflow-y: auto;">
      <van-cell-group inset v-for="(item, index) in storeList" :key="index" @click="goto(item)"
        style="margin-bottom: 8px;">
        <van-cell :value="item.vehicleNo">
          <template #title>
            <span class="custom-title">车牌号</span>
            <van-tag type="primary">{{ item.allocTypeName }}</van-tag>
          </template>
        </van-cell>
        <div>
          <van-cell :value="`${item.juanList.length}/${item.banPiceNum}`" title="卷/板个数">
          </van-cell>
          <div>
            <van-steps direction="vertical">
              <van-step>
                <span style="font-size: 16px;color: #07c160;">车头</span>
              </van-step>
              <van-step v-for="(li, i) in item.list" :key="i + 10010" :style="{ color: getColor(li.allocType) }">
                <span>{{ li.packId }}</span>
              </van-step>
              <van-step>
                <span style="font-size: 16px;color: #07c160;">车尾</span>
              </van-step>
            </van-steps>
          </div>
        </div>
      </van-cell-group>
    </div>
  </div>



</template>
<script>
import { post } from "../../api/base-service";
export default {
  data() {
    return {
      listLenght: 0,
      storeList: [],
      numLenght: 0,
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    getColor(status) {
      switch (status) {
        case "1":
          return "red";
        case "20":
          return "#38f";
        default:
          return "#323233";
      }
    },
    async query() {
      const params = {
        serviceId: "S_LI_RL_0095",
        segNo: localStorage.getItem("segNo"),
      };
      let result = await post(params);
      this.storeList = result.list.reduce((acc, item) => {
        // 查找是否已存在具有相同 vehicleNo 和 allocTypeName 的对象
        const existingVehicle = acc.find(
          (group) =>
            group.vehicleNo === item.vehicleNo &&
            group.allocTypeName === item.allocTypeName
        );

        if (existingVehicle) {
          // 如果找到，将当前项目的 packId 和 status 添加到 list 中
          existingVehicle.list.push({
            packId: item.packId,
            allocType: item.allocType,
            piceNum: item.piceNum
          });
        } else {
          // 如果未找到，创建一个新的对象并添加到累加器数组中
          acc.push({
            vehicleNo: item.vehicleNo,
            allocTypeName: item.allocTypeName,
            piceNum: item.piceNum,
            list: [{ packId: item.packId, allocType: item.allocType, piceNum: item.piceNum }],
          });
        }
        return acc;
      }, []);
      this.storeList.forEach((item) => {
        this.numLenght += item.list.length;
        if (this.listLenght < item.list.length) {
          this.listLenght = item.list.length;
        }
      });

      this.storeList = this.storeList.map(s => {
        const banList = s.list.filter(s => s.packId == 'XXX');
        console.log(banList);

        return {
          ...s,
          banPiceNum: banList.length > 0 && banList[0].piceNum ? banList[0].piceNum : 0,
          juanList: s.list.filter(s => s.packId != 'XXX'),
        }
      })
    },
    goto(item) {
      this.$router.togo({
        name: "detail",
        params: {
          list: item,
        },
      });
    },
  },
  mounted() {
    this.query();
  },
  watch: {},
};
</script>
<style scoped>
.box {
  width: 100%;
  background: white;
}

.prompt {
  font-size: 16px;
  padding: 10px 20px;
}

.van-cell__value {
  color: #323233;
}
</style>
