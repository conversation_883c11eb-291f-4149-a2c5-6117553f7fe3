<template>
    <div>
        <van-nav-bar>
            <template #title>
                <div class="global-hfont">登记驳回</div>
            </template>
            <template #left>
                <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
            </template>
        </van-nav-bar>
        <div class="search-list" v-if="searchList && searchList.length > 0" style="height: 80%;overflow-y: auto;">
            <van-checkbox-group v-model="checkBoxResult">
                <van-cell-group>
                    <van-cell v-for="item in searchList" :key="item.vehicleNo" @click="toggle(item)">
                        <template #title>
                            <div class="load-number">{{ item.vehicleNo }}</div>
                            <div>姓名: {{ item.driverName }}</div>
                            <div>电话: {{ item.telNum }}</div>
                        </template>
                        <template #right-icon>
                            <van-checkbox :name="item.vehicleNo" ref="checkboxes">
                                <template #icon="props">
                                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                                </template>
                            </van-checkbox>
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-checkbox-group>
        </div>

        <div class="mui-input-row3">
            <button class="mui-btn-del" @click="onReject" v-preventReClick="3000">
                驳&nbsp; &nbsp; &nbsp;&nbsp; 回
            </button>

        </div>
    </div>
</template>

<script>
import { Dialog } from 'vant';
import { post } from '../../api/base-service';
export default {
    data() {
        return {
            searchList: [],
            checkBoxResult: [],
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
        };
    },
    async created() {
        await this.getList();
    },
    methods: {
        onClickLeft() {
            this.$router.replace({
                name: 'index',
            });
        },

        /**
         * 选择数据
         * @param item 
         */
        async toggle(item) {
            let id = item.vehicleNo;
            if (this.checkBoxResult.includes(id)) {
                let index = this.checkBoxResult.findIndex(item => item == id);
                this.checkBoxResult.splice(index, 1);
            } else {
                this.checkBoxResult.push(id);
            }
        },

        /**
         * 驳回
         */
        async onReject() {
            const checkList = this.searchList.filter(s => this.checkBoxResult.includes(s.vehicleNo)).map(s => {
                return {
                    ...s,
                    recRevisor: localStorage.getItem('userId'),
                    recRevisorName: localStorage.getItem('userName'),
                }
            });
            const pamars = {
                serviceId: 'S_LI_RL_0118',
                result: checkList,
            };
            const res = await post(pamars);
            if (res && res.__sys__ && res.__sys__.status == 1) {
                this.$toast('驳回成功');
                await this.getList();
            }
        },

        /**
         * 查询数据
         */
        async getList() {
            const pamars = {
                serviceId: 'S_LI_RL_0117',
                status: '10',
            };
            const res = await post(pamars);
            if (!res || !res.result || res.__sys__.status != 1) {
                return;
            }
            this.searchList = res.result;
        },

    },
};
</script>

<style lang="less" scoped>
.mui-btn-del {
    width: 90%;
    height: 48px;
    background: #ee0a24;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    margin-bottom: 28px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #FFFFFF;
    font-size: 16px;
    line-height: 22px;
}

.activeColor {
  color: #007aff;
}
</style>