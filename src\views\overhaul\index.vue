<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">检修实绩</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>
        <div class="in-content" style="height: 80vh;overflow-y: auto;">
            <van-field label="设备名称" required readonly v-model="overhaulPlan.equipmentName" size="large"
                @click="serachEquipment" />
            <van-field label="检修计划号" required readonly v-model="overhaulPlan.overhaulPlanId" size="large"
                @click="serachPlan" />
            <van-field label="分部设备名称" readonly v-model="overhaulPlan.deviceName" size="large" />
            <van-field label="计划检修项目" type="textarea" readonly v-model="overhaulPlan.overhaulProject" autosize
                size="large" />
            <van-field label="实际检修项目" required type="textarea" v-model="overhaulPlan.actualLegacyProject" autosize
                size="large" placeholder="请输入实际检修项目" />
            <van-field label="检修实施日期" required readonly v-model="overhaulPlan.overhaulImplementDate" size="large"
                placeholder="请选择检修实施日期" @click="showDate = true" />
            <van-calendar v-model="showDate" @confirm="confirmDate" :show-confirm="false" />
            <van-field label="实际检修时间(min)" required v-model="overhaulPlan.actualOverhaulTime" size="large"
                placeholder="请输入检修时间" type="number" />
            <van-field label="实际检修人数" required v-model="overhaulPlan.actualOverhaulNumber" size="large"
                placeholder="请输入检修人数" type="number" />
            <van-field required name="radio1" label="是否完成" size="large">
                <template #input>
                    <van-radio-group v-model="overhaulPlan.isComplete" direction="horizontal">
                        <van-radio name="1">是</van-radio>
                        <van-radio name="0">否</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-if="overhaulPlan.isComplete == '0'" required size="large"
                v-model="overhaulPlan.overhaulLegacyProject" autosize label="待处理及遗留问题" placeholder="请输入待处理及遗留问题" />
            <van-field required name="radio2" label="是否动火" size="large">
                <template #input>
                    <van-radio-group v-model="overhaulPlan.isHot" direction="horizontal">
                        <van-radio name="1">是</van-radio>
                        <van-radio name="0">否</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-if="overhaulPlan.isHot == '1'" required size="large" name="uploader1" label="动火手续">
                <template #input>
                    <van-uploader v-model="hotFileList" :upload-icon="uploadIcon" @oversize="onOversize" :max-count="9"
                        accept="image/*" :max-size="10 * 1024 * 1024" />
                </template>
            </van-field>
            <van-field required name="radio3" label="是否符合标准" size="large">
                <template #input>
                    <van-radio-group v-model="overhaulPlan.isConformStandard" direction="horizontal">
                        <van-radio name="1">是</van-radio>
                        <van-radio name="0">否</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field v-if="overhaulPlan.isConformStandard == '0'" required size="large"
                v-model="overhaulPlan.relevantMeasures" autosize label="相关措施" placeholder="请输入相关措施" />
            <van-field size="large" v-model="overhaulPlan.overhaulSuggestions" autosize label="主要问题及建议"
                placeholder="请输入主要问题及建议" />
            <van-field required size="large" v-model="overhaulPlan.overhaulSummarize" autosize label="综合评价及总结"
                placeholder="请输入综合评价及总结" />
            <van-field size="large" name="uploader" label="图片上传">
                <template #input>
                    <van-uploader v-model="uploaderList" :upload-icon="uploadIcon" @oversize="onOversize"
                        :before-read="beforeImageRead" :max-count="9" accept="image/*" :max-size="10 * 1024 * 1024" />
                </template>
            </van-field>
        </div>
        <div class="mui-input-row" style="margin: 0" @click="updateActual">
            <button type="button" class="mui-btn">
                确&nbsp; &nbsp; &nbsp;&nbsp;定
            </button>
        </div>
        <van-dialog v-model="isShowList" title="选择检修计划" show-cancel-button :beforeClose="closeList">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in planList" :key="index"
                            :title="item.overhaulPlanId + item.deviceName" :label="item.overhaulProject"
                            @click="selectedId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
        <!-- 选择设备 -->
        <van-dialog v-model="isShowEquipment" title="选择设备" show-cancel-button :beforeClose="closeEquipment">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedEquipmentId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in equipmentList" :key="index"
                            :title="item.eArchivesNo" :label="item.equipmentName"
                            @click="selectedEquipmentId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>
<script>
import { post } from '../../api/base-service';
import uploadIcon from '@/assets/imgs/upload-icon.png';
import { Dialog } from 'vant';
export default {
    data() {
        return {
            uploadIcon: uploadIcon,
            uploaderList: [],
            hotFileList: [],
            overhaulPlan: {},
            isShowList: false,
            selectedId: '',
            planList: [],
            isShowEquipment: false,
            equipmentList: [],
            selectedEquipmentId: '',
            showDate: false
        }
    },
    methods: {
        //校验上传图片大小
        onOversize(file) {
            this.$toast.fail("文件大小不能超过10MB，请重新选择！");
        },
        onClickLeft() {
            this.$router.replace('/index');
        },
        // 查找计划
        serachPlan() {
            this.getPlanList();
        },
        serachEquipment() {
            this.getEquipmentList();
        },
        // 获取检修计划列表
        async getPlanList() {
            if (!this.overhaulPlan.eArchivesNo) {
                Dialog.alert({
                    message: '请选择设备',
                });
                return;
            }
            const params = {
                serviceId: 'S_VG_DM_0014',
                eArchivesNo: this.overhaulPlan.eArchivesNo
            };

            const result = await post(params);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到检修计划',
                });
                return;
            }
            this.planList = result.list;
            this.isShowList = true;
        },
        // 获取设备列表
        async getEquipmentList() {
            const params = {
                serviceId: 'S_VG_DM_0013'
            };
            // console.log(params);
            const result = await post(params);
            // console.log(result);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到需检修的设备信息',
                });
                return;
            }
            this.equipmentList = result.list;
            this.isShowEquipment = true;
        },
        // 关闭选择检修计划弹窗
        closeList(action, done) {
            // console.log(this.selectedId);
            if (action === 'confirm') {
                if (!this.selectedId) {
                    Dialog.alert({
                        message: '请选择检修计划',
                    });
                    done(false);
                    return;
                }
                const currentData = this.planList.find(p => p.uuid == this.selectedId);
                // console.log(currentData);
                this.clearData();
                this.overhaulPlan.eArchivesNo = currentData.eArchivesNo;
                this.overhaulPlan.equipmentName = currentData.equipmentName;
                this.overhaulPlan.deviceName = currentData.deviceName;
                this.overhaulPlan.uuid = currentData.uuid;
                this.overhaulPlan.overhaulPlanId = currentData.overhaulPlanId;
                this.overhaulPlan.overhaulProject = currentData.overhaulProject;
                this.overhaulPlan.actualLegacyProject = currentData.actualLegacyProject.trim();
                this.overhaulPlan.actualOverhaulNumber = currentData.actualOverhaulNumber || "";
                this.overhaulPlan.overhaulImplementDate = currentData.overhaulImplementDate.trim();
                this.overhaulPlan.actualOverhaulTime = currentData.actualOverhaulTime || "";
                this.overhaulPlan.isComplete = currentData.isComplete.trim() || '1';
                this.overhaulPlan.overhaulLegacyProject = currentData.overhaulLegacyProject.trim();
                this.overhaulPlan.overhaulSuggestions = currentData.overhaulSuggestions;
                this.overhaulPlan.overhaulSummarize = currentData.overhaulSummarize.trim();
                this.overhaulPlan.isHot = currentData.isHot.trim() || '0';
                this.overhaulPlan.hotCardId = currentData.hotCardId.trim();
                this.overhaulPlan.isConformStandard = currentData.isConformStandard.trim() || '1';
                this.overhaulPlan.relevantMeasures = currentData.relevantMeasures.trim();
            }
            this.isShowList = false;
            done();
        },
        // 关闭选择设备弹窗
        closeEquipment(action, done) {
            if (action === 'confirm') {
                if (!this.selectedEquipmentId) {
                    Dialog.alert({
                        message: '请选择设备',
                    });
                    done(false);
                    return;
                }
                const currentData = this.equipmentList.find(p => p.uuid == this.selectedEquipmentId);
                this.overhaulPlan.eArchivesNo = currentData.eArchivesNo;
                this.overhaulPlan.equipmentName = currentData.equipmentName;
                this.serachPlan();
            }
            this.isShowEquipment = false;
            done();
        },
        // 确认日期
        confirmDate(date) {
            // console.log(date);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            this.overhaulPlan.overhaulImplementDate = `${year}-${month}-${day}`;
            this.showDate = false;
        },
        // 校验数据
        checkData() {
            // console.log(this.overhaulPlan);
            if (!this.overhaulPlan.uuid) {
                Dialog.alert({
                    message: '请选择检修计划',
                });
                return false;
            }
            if (!this.overhaulPlan.actualLegacyProject) {
                Dialog.alert({
                    message: '请输入实际检修项目',
                });
                return false;
            }
            if (!this.overhaulPlan.overhaulImplementDate) {
                Dialog.alert({
                    message: '请选择检修实施日期',
                });
                return false;
            }
            if (!this.overhaulPlan.actualOverhaulTime) {
                Dialog.alert({
                    message: '请输入检修时间',
                });
                return false;
            }
            if (!this.overhaulPlan.actualOverhaulNumber) {
                Dialog.alert({
                    message: '请输入实际检修人数',
                });
                return false;
            }
            if (!this.overhaulPlan.isComplete) {
                Dialog.alert({
                    message: '请选择是否完成',
                });
                return false;
            }
            if (this.overhaulPlan.isComplete == '0') {
                // console.log(this.overhaulPlan.overhaulLegacyProject);
                if (!this.overhaulPlan.overhaulLegacyProject) {
                    Dialog.alert({
                        message: '是否完成为“否”，请输入遗留项目',
                    });
                    return false;
                }
            }
            if (this.overhaulPlan.isHot == '1') {
                // console.log(this.overhaulPlan.hotCardId);
                if (this.hotFileList.length < 1) {
                    Dialog.alert({
                        message: '是否动火为“是”，请上传动火证',
                    });
                    return false;
                }
            }
            if (this.overhaulPlan.isConformStandard == '0') {
                // console.log(this.overhaulPlan.relevantMeasures);
                if (!this.overhaulPlan.relevantMeasures) {
                    Dialog.alert({
                        message: '是否符合标准为“否”，请输入相关措施',
                    });
                    return false;
                }
            }
            if (!this.overhaulPlan.overhaulSummarize) {
                Dialog.alert({
                    message: '请输入综合评价及总结',
                });
                return false;
            }
            // if (!this.uploaderList.length) {
            //     Dialog.alert({
            //         message: '请上传图片',
            //     });
            //     return false;
            // }
            return true;
        },
        // 更新检修实绩
        async updateActual() {
            // console.log(this.overhaulPlan);
            if (!this.checkData()) {
                return;
            }
            const params = {
                serviceId: 'S_VG_DM_0015',
                userName: localStorage.getItem("userName"),
                fileList: this.uploaderList.map(item => item.content),
                hotFileList: this.hotFileList.map(item => item.content),
                ...this.overhaulPlan
            };
            // console.log(params);
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                this.$toast(result.__sys__.msg);
                return;
            }
            if (this.planList.length > 1) {
                Dialog.confirm({
                    message: '上传成功，是否继续检修此设备?',
                }).then(() => {
                    this.clearData();
                    this.selectedId = '';
                    this.getPlanList();
                }).catch(() => {
                    this.clearData();
                });
            } else {
                this.clearData();
                this.$toast('检修完成');
            }
        },
        // 清除数据
        clearData() {
            this.overhaulPlan.uuid = '';
            this.overhaulPlan.deviceName = '';
            this.overhaulPlan.overhaulPlanId = '';
            this.overhaulPlan.overhaulProject = '';
            this.overhaulPlan.actualLegacyProject = '';
            this.overhaulPlan.actualOverhaulNumber = '';
            this.overhaulPlan.overhaulImplementDate = '';
            this.overhaulPlan.actualOverhaulTime = '';
            this.overhaulPlan.isComplete = '';
            this.overhaulPlan.overhaulLegacyProject = '';
            this.overhaulPlan.overhaulSuggestions = '';
            this.overhaulPlan.overhaulSummarize = '';
            this.overhaulPlan.isHot = '';
            this.overhaulPlan.hotCardId = '';
            this.overhaulPlan.isConformStandard = '';
            this.overhaulPlan.relevantMeasures = '';
            this.uploaderList = [];
            this.hotFileList = [];
        },
        // 检查并请求摄像头权限
        async checkAndRequestPermission() {
            try {
                // 检查是否支持permissions API
                if (navigator.permissions && navigator.permissions.query) {
                    const result = await navigator.permissions.query({ name: 'camera' });
                    if (result.state === 'denied' || result.state === 'prompt') {
                        await this.requestCameraAccess();
                    }
                    // 监听权限变化
                    result.addEventListener('change', () => {
                        if (result.state === 'denied') {
                            Dialog.alert({
                                message: '摄像头权限已被禁用，部分功能可能无法使用。',
                            });
                        }
                    });
                } else {
                    // 如果不支持permissions API,直接尝试请求摄像头
                    await this.requestCameraAccess();
                }
            } catch (err) {
                Dialog.alert({
                    message: '权限检查失败:' + err,
                });
                await this.requestCameraAccess();
            }
        },
        // 新增方法处理摄像头访问
        async requestCameraAccess() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                // 获得权限后立即停止摄像头
                stream.getTracks().forEach(track => track.stop());
            } catch (err) {
                Dialog.alert({
                    message: '请授权摄像头权限以使用此功能。' + err,
                });
            }
        },
        // 压缩图片
        async beforeImageRead(file) {
            return this.compressImage(file);
        },

        /**
         * 压缩图片
         * @param {File} file 原始图片文件
         * @param {number} quality 压缩质量 0-1
         * @param {number} maxWidth 最大宽度
         * @param {number} maxHeight 最大高度
         * @returns {Promise<File>} 压缩后的文件
         */
        compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    // 计算压缩后的尺寸
                    let { width, height } = img;

                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }

                    // 设置画布尺寸
                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片到画布
                    ctx.drawImage(img, 0, 0, width, height);

                    // 将画布内容转换为Blob
                    canvas.toBlob((blob) => {
                        // 创建新的File对象
                        const compressedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now()
                        });

                        // console.log(`图片压缩完成: ${(file.size / 1024 / 1024).toFixed(2)}MB -> ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
                        resolve(compressedFile);
                    }, file.type, quality);
                };

                img.onerror = () => {
                    console.error('图片加载失败，使用原始文件');
                    resolve(file);
                };

                // 加载图片
                img.src = URL.createObjectURL(file);
            });
        },
    },
    mounted() {
        this.overhaulPlan = {
            eArchivesNo: "",
            equipmentName: "",
            overhaulPlanId: "",
            deviceName: "",
            overhaulProject: "",
            actualLegacyProject: "",
            actualOverhaulNumber: "",
            overhaulImplementDate: "",
            actualOverhaulTime: "",
            isComplete: "",
            overhaulLegacyProject: "",
            overhaulSuggestions: "",
            overhaulSummarize: "",
            isHot: "",
            hotCardId: "",
            isConformStandard: "",
            relevantMeasures: "",
            uuid: "",
            actualLegacyProject: ""
        };
        // 检查并请求权限
        // this.checkAndRequestPermission();
    }
}
</script>
<style></style>
