document.addEventListener('plusready', function () {
  const webview = plus.webview.currentWebview();

  // 监听返回键
  plus.key.addEventListener('backbutton', function () {
    const currentRoute = window.Vue.$router.currentRoute;
    console.log(currentRoute.name);

    if (currentRoute.name == 'unload' || currentRoute.name == 'unload-cq') {
      // 跳转到 end-load 页面并传递数据
      window.Vue.$router.replace({
        name: 'index',
      });
    } else if (['scan-store', 'in-storage', 'scan-store-cq'].includes(currentRoute.name)) {
      // 跳转到 end-load 页面并传递数据
      window.Vue.$router.togo({
        name: 'end-load',
      });
    } else if (currentRoute.name == 'index' || currentRoute.name == 'login') {
      webview.close(); // 关闭应用
    } else if (currentRoute.name == 'end-load') {
      // 跳转到 end-load 页面并传递数据
      window.Vue.$router.replace({
        name: 'index',
      });
    } else if (currentRoute.name == 'select-car') {
      window.Vue.$router.replace({
        name: 'unload-cq',
      });
    } else {
      window.Vue.$router.goBack();
    }
  });
});
