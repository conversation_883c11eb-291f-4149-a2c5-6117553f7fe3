/**
 * 引入fetch
 * @param params
 * @returns {*}
 */
import { 
  fetch, 
  fetchIMC, 
  fetchIMCProduction, 
  fetchIMCModule, 
  fetchOriginal, 
  fetchWithDomain 
} from '../apiconfig/service'

// 默认服务（使用原有域名）
export function baseService(params) {
  return fetch(params);
}

// 调用IMC物流模块的服务
export function imcLogisticsService(params) {
  return fetchIMC(params);
}

// 调用IMC生产模块的服务  
export function imcProductionService(params) {
  return fetchIMCProduction(params);
}

// 调用IMC指定模块的服务
export function imcModuleService(params, module = 'LOGISTICS') {
  return fetchIMCModule(params, module);
}

// 调用原有域名的服务
export function originalService(params) {
  return fetchOriginal(params);
}

// 通用服务，可指定域名
export function serviceWithDomain(params, domain = 'ORIGINAL') {
  return fetchWithDomain(params, domain);
}

// 为了向后兼容，保留原有的imcService，指向物流模块
export function imcService(params) {
  return fetchIMC(params);
}