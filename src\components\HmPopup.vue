<template>
  <van-dialog v-model="myshow" title="捆包信息" @confirm = "onConfirmItem">
    <div class="dialog-content">
      <van-radio-group v-model="radio2">
        <van-cell-group>
          <van-cell
            v-for="(item, index) in arr"
            :key="index"
            clickable
            @click="isChecked(index, item)"
          >
            <template #title>
              <div class="ware-title">{{item.packId}}</div>
            </template>
            <template #label>
              <div v-if="item.labelId">
               标签号：{{ item.labelId }}</div>
              <div>
                材料号：{{ item.matInnerId }}</div>

               <div>
                规格：{{ item.specDesc }}</div>
                <div>
                 净重：{{ item.netWeight }}</div>
               <div v-if=" item.putinPlanId ">
                入库计划号：{{ item.putinPlanId }}</div>
                <div  v-if="item.tradeCodeName ">
                贸易方式：{{ item.tradeCodeName }}</div>
            </template>
            <template #right-icon>
               <van-radio :name="index"
                      ><template #icon="props">
                        <span
                          class="iconfont"
                          :class="props.checked ? activeIcon : inactiveIcon"
                        ></span>
                      </template>
                    </van-radio>
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
  </van-dialog>
</template>
<script>
export default {
  name: "HmPopup",
  props: ["arr"], //父组件传来的值
  data() {
    return {
      myshow: true, //popup的显示，在组件中，默认是显示，不用父组件传值
      dataarr: this.arr,
      list: [],
      loading: false,
      finished: false,
      radio2: -1,
       activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  methods: {
    close() {
      this.$emit("closeTip", false); //把关闭信息传递给父组件
    },
    isChecked(index, item) {
      this.radio2 = index;
      this.list = [item];
    },
    onConfirmItem(){
        this.$emit("checkInfo", this.list);
    }
  },
};
</script>
<style lang="less" scoped>
.dialog-content {
  width: 100%;
  height: 200px;
  overflow-y: auto;
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 2px;
  }
  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}
.activeColor {
  color: #007aff;
}
.ware-title{
  font-size: 14px;
  color: #000;
}
.ware-name{
  font-size: 13px;
  color: #333333;
}
</style>
