<template>
  <div>
    <div>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">模具入库</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </div>

    <div class="in-content">
      <!-- <van-cell title="车牌号" size="large" v-model="carNumber" /> -->
      <van-field v-model="moldId" ref="ladRef" label="模具编号" placeholder="请扫描模具" input-align="right"
        @keyup.enter.native="moldScan" style="font-size: 16px;" />
      <van-cell title="模具名称" v-model="moldName" style="font-size: 16px;" />
      <van-cell title="模具重量" v-model="moldWeight" style="font-size: 16px;" />
      <van-field v-model="moldFlag" label="层数标记" type="digit" placeholder="请输入层数标记" input-align="right"
        style="font-size: 16px;" />
      <van-cell title="行车名称" is-link v-model="trainName" @click="getLoadTruckOperatorList" style="font-size: 16px;" />
      <van-field v-if="craneId == 'other'" required v-bind:error-message="errMsg" v-model="areaName" label="区域名称"
        placeholder="请输入区域名称" input-align="right" error-message-align="right" style="font-size: 16px;" @input="() => errMsg = ''" />



      <van-dialog v-model="isShowTruck" title="行车" show-cancel-button :beforeClose="confirmTruck">
        <div style="max-height: 320px; overflow-y: auto;">
          <van-radio-group v-model="truckRadio">
            <van-cell-group>
              <van-cell clickable :title="item.craneName" v-for="(item, index) in loadTruckOperatorList" :key="index"
                @click="truckRadio = item.craneId">
                <template #right-icon>
                  <van-radio :name="item.craneId" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>
      </van-dialog>

    </div>


    <div class="foot-sticky">
      <van-button type="info" size="large" class="foot-sticky-btn" @click="confirmToStore">入库</van-button>
    </div>

  </div>
</template>

<script>
import { Dialog } from "vant";
import { post } from '../../api/base-service';

export default {
  name: "InStorage",
  data() {
    return {
      moldId: '',
      moldWeight: '',
      moldName: '',
      moldFlag: 1,
      isShowTruck: false,
      truckRadio: '',
      loadTruckOperatorList: [],
      trainName: '请选择行车',
      craneId: '', // 行车id
      areaName: '',
      errMsg: '',
    };
  },
  created() {
    this.changfouce();
  },
  methods: {
    // 聚焦
    changfouce() {
      this.$nextTick((x) => {
        this.$refs.ladRef.focus();
      });
    },

    onClickLeft() {
      this.$router.replace({
        name: 'index',
      });
    },

    // 扫描到的模具信息
    moldScan() {
      const moldList = this.moldId.split(' ');
      this.moldId = moldList[0];
      this.moldName = moldList[1];
      this.moldWeight = moldList[2];

    },

    /**
     * 确认选择行车
     * @param action 
     * @param done 
     */
    confirmTruck(action, done) {
      if (action != 'confirm') {
        done();
        return;
      }

      if (!this.truckRadio) {
        Dialog.alert({ message: '请选择行车' });
        done(false);
        return;
      }

      const currentData = this.loadTruckOperatorList.find(p => p.craneId == this.truckRadio);
      if (!currentData) {
        done();
        return;
      }

      this.trainName = currentData.craneName;
      this.craneId = currentData.craneId;
      done(true);
    },

    /**
     * 查询行车
     */
    async getLoadTruckOperatorList() {
      let queryMap = {
        serviceId: 'S_LI_DS_0006',
      };

      const result = await post(queryMap);
      if (!result || result.__sys__.status == -1) {
        return;
      }

      if (result.list.length == 0) {
        return;
      }
      let resultList = result.list;
      resultList.push({
        craneName: '其他',
        craneId: 'other',
      });
      this.loadTruckOperatorList = resultList;
      this.isShowTruck = true;
    },

    /**
     * 入库
     */
    async confirmToStore() {
      if (!this.craneId) {
        Dialog.alert({
          message: "请选择行车!",
        });
        return;
      }

      if (!this.moldId || !this.moldName || !this.moldWeight) {
        Dialog.alert({
          message: "请扫描模具!",
        });
        return;
      }

      if (this.craneId == 'other' && !this.areaName) {
        this.errMsg = '请输入区域名称';
        return;
      }

      const params = {
        serviceId: 'S_LI_DS_0007',
        mouldId: this.moldId,
        craneId: this.craneId,
        craneName: this.trainName,
        posDirCode: this.moldFlag,
        mouldName: this.moldName,
        mouldSpecWeightSum: this.moldWeight,
        areaName: this.areaName,
        factoryBuilding: localStorage.getItem('factoryBuilding'), // F1
        factoryArea: localStorage.getItem('factoryAreaTrue'), // CQBG
        warehouseCode: localStorage.getItem('warehouseCode'),
        warehouseName: localStorage.getItem('warehouseName'),
      };

      const result = await post(params);
      if (!result || result.__sys__.status == -1) {
        return;
      }
      this.$toast('入库成功');
    },

  },
};
</script>
<style>
.van-cell__value,
.van-field__label {
  color: #323233;
}

.empty-des {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 32px 0;
}

.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}

.activeColor {
  color: #007aff;
}
</style>