/* eslint-disable */
import axios from 'axios';
import router from '@/router';
import {
  Toast,
  Dialog
} from 'vant';
import userPreferencesManager from '../utils/userPreferences';
/**
 * 定义请求常量
 * TIME_OUT、ERR_OK
 */
export const TIME_OUT = 60000; // 请求超时时间
export const ERR_OK = true; // 请求成功返回状态，字段和后台统一
export let baseUrl = '';
//export const baseUrl = process.env.BASE_URL   // 引入全局url，定义在全局变量process.env中，开发环境为了方便转发，值为空字符串

// 请求超时时间
axios.defaults.timeout = TIME_OUT

// 多域名配置
export const API_DOMAINS = {
  ORIGINAL: {
    production: 'http://10.70.38.45:8080/imom',
    test: 'http://10.82.6.17:8080',
    development: '/api'
  },
  IMC: {
    production: 'http://imc.baogang.info',
    test: 'http://imctest.baogang.info',
    development: '/imc-api'
  }
};

// IMC模块配置
export const IMC_MODULES = {
  LOGISTICS: 'elim-bc',    // 物流模块
  PRODUCTION: 'imc-ma'     // 生产模块
};

// 获取当前环境
const currentEnv = process.env.NODE_ENV === "production" ? "production" : 
                   process.env.NODE_ENV === "test" ? "test" : "development";

// 默认使用原有域名
if (process.env.NODE_ENV == "production") {
  // 正式环境地址
  baseUrl = API_DOMAINS.ORIGINAL.production;
  // baseUrl = 'http://10.70.88.10:8080';// 测试(旧)
  // baseUrl = 'http://ims-em-test.baointl.info'; // 测试
} else if (process.env.NODE_ENV == "test") {
  // baseUrl = 'http://10.70.88.10:8080'; // 测试(旧)
  baseUrl = API_DOMAINS.ORIGINAL.test;// 测试
  // baseUrl = 'http://IMOM-xb.baointl.info/imom';
} else {
  baseUrl = API_DOMAINS.ORIGINAL.development;
}

let loading

function startLoading() {
  loading = Toast.loading({
    duration: 0,
    forbidClick: true,
  })
}

function endLoading() {
  loading.clear()
}

// 封装请求拦截
axios.interceptors.request.use(
  config => {
    startLoading();
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

const vm = this;
// 封装响应拦截，判断token是否过期
axios.interceptors.response.use(
  response => {
    endLoading();

    if (!response || !response.data) {
      Toast({
        message: '网络异常, 请重试',
        duration: 3000,
      });
      return Promise.reject(new Error('网络异常'));
    } else if (response.data.__sys__.status == -1) {
      Toast({
        message: response.data.__sys__.msg,
        duration: 3000,
      });
      return Promise.reject(new Error(response.data.__sys__.msg));
    } else if (response.data.__sys__.status == 0) {
      Dialog.alert({
        title: '提示',
        message: '登录信息失效，请重新登录',
      }).then(() => {
        // 保存当前用户的偏好设置
        const currentUserId = localStorage.getItem("userId");
        if (currentUserId) {
          userPreferencesManager.saveCurrentPreferencesToUser(currentUserId);
        }
        
        // 清除登录相关的localStorage数据，但保留所有用户的偏好设置
        const keysToRemove = [
          "userId", "userName", "accessToken", "thisVerion",
          "segNo", "segName", "factoryName", "factoryArea", "factoryAreaTrue",
          "factoryBuilding", "factoryBuildingName", "warehouseCode", "warehouseName"
        ];
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key);
        });
        
        sessionStorage.clear();

        router.push('/');
      });
      return Promise.reject(new Error('登录信息失效'));
    } else if (response.data.__sys__.status == 2) {
      localStorage.setItem("accessToken", response.data.accessToken);
      return Promise.resolve(response);
    } else {
      return Promise.resolve(response);
    }
  },
  err => {
    endLoading();
    Toast('请求异常,请重试')
    return Promise.reject(err)
  }
)

// 封装post请求（原有的，使用默认域名）
export function fetch(params) {
  return new Promise((resolve, reject) => {
    axios.post(`${baseUrl}/service`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  });
}

// 封装支持指定域名的请求
export function fetchWithDomain(params, domain = 'ORIGINAL') {
  const targetUrl = API_DOMAINS[domain][currentEnv];
  let apiPath = '/service';
  
  return new Promise((resolve, reject) => {
    axios.post(`${targetUrl}${apiPath}`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  });
}

// 封装IMC模块请求
export function fetchIMCModule(params, module = 'LOGISTICS') {
  const targetUrl = API_DOMAINS.IMC[currentEnv];
  const modulePath = IMC_MODULES[module];
  const apiPath = `/${modulePath}/service`;
  
  return new Promise((resolve, reject) => {
    axios.post(`${targetUrl}${apiPath}`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  });
}

// 专门用于调用IMC物流模块的请求
export function fetchIMC(params) {
  return fetchIMCModule(params, 'LOGISTICS');
}

// 专门用于调用IMC生产模块的请求
export function fetchIMCProduction(params) {
  return fetchIMCModule(params, 'PRODUCTION');
}

// 专门用于调用原有域名的请求
export function fetchOriginal(params) {
  return fetchWithDomain(params, 'ORIGINAL');
}

