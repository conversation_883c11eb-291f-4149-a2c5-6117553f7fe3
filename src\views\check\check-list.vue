<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">盘库单列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search v-model="inventoryNumber" shape="round" background="#007aff" placeholder="请输入或扫描盘库单号"
        @search="getCheckList" left-icon="" :clearable="true">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getCheckList" style="color: #999999"></div>
        </template>
      </van-search>
    </van-sticky>
    <div v-if="checkList !== undefined && checkList != null && checkList.length > 0" style="overflow-y: auto; height: 75vh;">
     <van-radio-group v-model="check">
       <van-cell-group>
         <van-cell
           v-for="(item, index) in checkList"
           :key="index"
           clickable
           @click="isChecked2(index, item)"
         >
           <template #title>
             <div class="all-font-size" style="color:#007aff ;">{{item.stockCheckNum}}</div>
              <div class="" style="margin-top: 6px;  font-size: 16px;"><span style="font-weight: 400;">账套：</span>{{item.segCname}}</div>
           </template>
           <template #label>
              <div class="" style="color:#000 ; margin-top: 5px; font-size: 16px;"><span  style="font-weight: 400;">仓库：</span>{{item.warehouseName}}</div>
           </template>
           <template #right-icon>
              <van-radio :name="index"
                     ><template #icon="props">
                       <span
                         class="iconfont"
                         :class="props.checked ? activeIcon : inactiveIcon"
                       ></span>
                     </template>
                   </van-radio>
           </template>
         </van-cell>
       </van-cell-group>
     </van-radio-group>
        <div class="mui-input-row3" v-if="showFlag">
          <button id="block_button" type="button" class="mui-btn3" v-preventReClick="3000" @click="finishCheckList()" >
           盘库完成
          </button>
          <button id="block_button" type="button" class="mui-btn3" v-preventReClick="3000" @click="goCheck()">
           盘库
          </button>
        </div>
      <div class="mui-input-row" style="margin: 0" v-else>
        <button type="button" class="mui-btn" @click="goCheck()" v-preventReClick="3000">
          盘库
        </button>
      </div>
    </div>
    <div v-else>
    </div>
    <van-dialog v-model="myshow" title="捆包信息" @confirm="onConfirmItem">
      <div class="dialog-content">
        <van-radio-group v-model="radio2">
          <van-cell-group>
            <van-cell v-for="(item, index) in 3" :key="index" clickable @click="isChecked(index, item)">
              <template #title>
                <div class="ware-title">11111{{item.packId}}</div>
              </template>
              <template #label>
                <div>
                  描述</div>
              </template>
              <template #right-icon>
                <van-radio :name="index"><template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  import {
    isEmpty
  } from "@/utils/tools";
  export default {
    data() {
      return {
        check: -1,
        checkNum: "",
        myshow: false,
        showFlag: false,
        checkList: [],
        inventoryNumber: "",
        radio2: -1,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    created() {
      this.getCheckList();
      this.isShowbtn();
    },
    methods: {
      onClickLeft() {
        this.$router.replace('/checkStorage');
      },
      isChecked(index, item) {
        this.radio2 = index;
       // this.checkNum = item.stockCheckNum;
         this.list = [item];
      },
      isChecked2(index, item) {
        this.check = index;
        this.checkNum = item.stockCheckNum;
      },
      onConfirmItem() {
        this.myshow = false;
      },
      goCheckCell(item) {
        this.$router.togo("/scanCheck?stockCheckNum=" + item.stockCheckNum);
        //this.$router.togo("/checkListDetail?stockCheckNum=" + item.stockCheckNum);
      },
      goCheck() {
        if(!isEmpty(this.checkNum)){
          this.$router.togo("/scanCheck?stockCheckNum=" + this.checkNum);
        }else{
             this.$toast("请选择盘库单！");
        }

        //this.$router.togo("/checkListDetail?stockCheckNum=" + item.stockCheckNum);
      },
      //获取盘库列表
      async getCheckList() {
        const params = {
          serviceId: "S_LI_RL_0126",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.inventoryNumber
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.checkList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //是否显示盘库完成按钮
      async isShowbtn() {
        const params = {
          serviceId: "S_LI_RL_0128",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                if (res.data.result == "1") {
                  this.showFlag = true;
                } else {
                  this.showFlag = false;
                }
                //this.checkList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      finishCheckList(){
       let that = this;
       if(!isEmpty(this.checkNum)){
         Dialog.confirm({
             title: '提示',
             message: `是否确认盘库单号${this.checkNum}盘库完成?`,
           })
           .then(() => {
             that.continueFinishCheckList();
           })
           .catch(() => {
             // on cancel
           });
       }else{
           this.$toast("请选择盘库单！");
       }

      },
      //盘库完成
      async continueFinishCheckList() {

        const params = {
          serviceId: "S_LI_RL_0129",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.checkNum
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                // this.checkList = dataObj.result;
                this.$toast(res.data.__sys__.msg);
                  setTimeout(() => {
                         this.getCheckList();
                      }, 3000);

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },

  };
</script>

<style lang="less" scoped>
  .dialog-content {
    width: 100%;
    height: 250px;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .ware-title {
    font-size: 14px;
    color: #000;
  }

  .ware-name {
    font-size: 13px;
    color: #333333;
  }
</style>
