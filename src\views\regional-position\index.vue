<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">区域定位</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <van-search v-model="searchQuery" placeholder="请输入搜索关键词" @search="onSearch" />
            <div class="search-page">
                <!-- 紧凑型分类选择器 -->
                <div class="compact-selector">
                    <!-- 固定宽度的触发器 -->
                    <div class="selector-trigger" @click="toggleSelector">
                        <span class="truncate-text">{{ currentCategory.label }}</span>
                        <van-icon :name="showSelector ? 'arrow-up' : 'arrow-down'" size="12" color="#666" />
                    </div>

                    <!-- 动态宽度的下拉菜单 -->
                    <transition name="fade-slide">
                        <div v-show="showSelector" class="selector-dropdown" :style="{ left: dropdownLeft + 'px' }">
                            <div v-for="(item, index) in categories" :key="index" class="dropdown-item"
                                :style="{ width: calculateItemWidth(item.label) + 'px' }"
                                @click="selectCategory(index)">
                                <span class="item-text">{{ item.label }}</span>
                                <van-icon v-if="activeIndex === index" name="success" size="14" color="#007aff" />
                            </div>
                        </div>
                    </transition>
                </div>
            </div>

            <van-divider content-position="left">定位内容</van-divider>

            <div style="height: 58vh;overflow-y: auto;margin-bottom: 16px;">
                <van-radio-group v-model="positionRadio">
                    <van-cell-group>
                        <van-cell v-for="(item, index) in list" :key="index" clickable
                            @click="positionRadio = item.uuid">
                            <template #title>
                                <div class="ware-title">{{ item.areaCode }}</div>
                            </template>
                            <template #label>
                                <div>
                                    {{ item.areaName }} <br />
                                    X起始: {{ item.xInitialPoint }}, Y起始: {{ item.yInitialPoint }}<br />
                                    X终点: {{ item.xDestination }}, Y终点: {{ item.yDestination }} <br />
                                    库位名称: {{ item.locationName }}
                                </div>
                            </template>
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>

            <div class="foot-btn">
                <van-button type="info" size="normal" @click="positionStartOrEnd('1')">定位起始坐标</van-button>
                <van-button plain type="info" size="normal" @click="positionStartOrEnd('0')">定位终点坐标</van-button>
            </div>
        </div>


        <van-dialog v-model="isShowTruck" title="行车" show-cancel-button :beforeClose="confirmTruck">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="truckRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.craneName" v-for="(item, index) in loadTruckOperatorList"
                            :key="index" @click="truckRadio = item.craneId">
                            <template #right-icon>
                                <van-radio :name="item.craneId" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>

<script>
import { Dialog } from 'vant';
import { post } from '../../api/base-service';

export default {
    data() {
        return {
            positionRadio: '',
            list: [],
            searchQuery: '',
            show: false,
            showSelector: false,
            activeIndex: 0,
            dropdownLeft: 0,
            textWidths: {}, // 缓存文字宽度计算结果
            categories: [
                { label: '区域' },
                { label: '库位' },
            ],
            isShowTruck: false,
            loadTruckOperatorList: [],
            truckRadio: '',
            curPosition: '',
            startFlag: '0',
        };
    },
    computed: {
        currentCategory() {
            return this.categories[this.activeIndex]
        }
    },
    methods: {
        onClickLeft() {
            this.$router.replace({ name: 'index' });
        },

        toggleSelector() {
            this.showSelector = !this.showSelector;
            if (this.showSelector) {
                const trigger = this.$el.querySelector('.selector-trigger');
                this.dropdownLeft = trigger.getBoundingClientRect().left;
            }
        },

        selectCategory(index) {
            this.activeIndex = index;
            this.showSelector = false;
            this.onSearch();
        },

        calculateItemWidth(text) {
            // 使用缓存避免重复计算
            if (!this.textWidths[text]) {
                const span = document.createElement('span');
                span.className = 'width-calculator';
                span.innerText = text;
                document.body.appendChild(span);
                this.textWidths[text] = Math.ceil(span.offsetWidth) + 40; // 文字宽度 + 左右padding和图标空间
                document.body.removeChild(span);
            }
            return this.textWidths[text];
        },

        /**
         * 搜索
         */
        async onSearch() {
            // 0为区域, 1是库位
            const params = {
                serviceId: 'S_LI_DS_0013',
                type: this.activeIndex,
                searchQuery: this.searchQuery,
            };
            const res = await post(params);
            if (!res || !res.__sys__ || res.__sys__.status == -1) {
                return;
            }
            this.list = res.returnList;

        },

        /**
         * 定位坐标
         */
        async positionStartOrEnd(type) {
            if (!this.positionRadio) {
                Dialog.alert({
                    message: '请选择数据',
                });
                return;
            }
            const data = this.list.find(l => l.uuid == this.positionRadio);
            this.curPosition = data;
            this.startFlag = type; // 开始定位

            // 查询行车
            this.getLoadTruckOperatorList();
        },

        async confirmTruck(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.truckRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.loadTruckOperatorList.find(p => p.craneId == this.truckRadio);
            if (!currentData) {
                done();
                return;
            }

            const params = {
                serviceId: this.startFlag == '1' ? 'S_LI_DS_0014' : 'S_LI_DS_0015',
                type: this.activeIndex,
                craneId: currentData.craneId,
                craneName: currentData.craneName,
                paramList: [this.curPosition],
            };
            const res = await post(params);

            console.log(res);
            if (!res || !res.__sys__ || res.__sys__.status == -1) {
                done(false);
                return;
            }

            this.$toast('定位成功');
            await this.onSearch();
            done(true);
            
        },

        /**
 * 查询行车
 */
        async getLoadTruckOperatorList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }
            this.loadTruckOperatorList = result.list;
            this.isShowTruck = true;
        },

    }
};
</script>

<style scoped>
.van-cell {
    padding: 12px 16px;
}

.foot-btn {
    justify-content: space-around;
    display: flex;
}

.search-page {
    padding: 16px;
    position: relative;
    padding-bottom: 0;

    .compact-selector {
        display: inline-block;
        position: relative;

        /* 固定宽度的触发器 */
        .selector-trigger {
            width: 60px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            background: #fff;
            border-radius: 15px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            overflow: hidden;

            &:active {
                background: #e6e8eb;
            }

            .truncate-text {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
                min-width: 0;
            }
        }

        /* 下拉菜单容器 */
        .selector-dropdown {
            position: absolute;
            top: calc(100% + 6px);
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 100;
            padding: 6px 0;
            max-height: 200px;
            overflow-y: auto;

            /* 动态宽度的选项 */
            .dropdown-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 16px;
                font-size: 14px;
                color: #333;
                transition: all 0.2s;
                margin: 0 auto;
                /* 水平居中 */

                .item-text {
                    white-space: nowrap;
                }

                &:hover {
                    background: #f7f8fa;
                }

                &:active {
                    background: #f0f2f5;
                }
            }
        }
    }

    /* 隐藏的宽度计算元素 */
    .width-calculator {
        position: absolute;
        visibility: hidden;
        height: auto;
        width: auto;
        white-space: nowrap;
        font-size: 14px;
        padding: 0 8px;
    }

    /* 动画效果 */
    .fade-slide-enter-active {
        transition: all 0.2s ease-out;
    }

    .fade-slide-leave-active {
        transition: all 0.15s ease-in;
    }

    .fade-slide-enter-from,
    .fade-slide-leave-to {
        opacity: 0;
        transform: translateY(-8px);
    }
}
</style>