<template>
    <div>
        <van-nav-bar>
            <template #title>
                <div class="global-hfont">上料吊运信息查询</div>
            </template>
            <template #left>
                <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
            </template>
        </van-nav-bar>

        <div style="height: 85%;">
            <van-cell is-link title="机组" size="large" placeholder="请选择机组" v-model="selectedUnit" @click="getUnitList" />
            <van-cell title="机组名称" size="large" v-model="selectedUnitName" />

            <van-dialog v-model="showUnitPicker" title="选择机组" show-cancel-button :beforeClose="onUnitConfirm">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="selectedUnit">
                        <van-cell-group>
                            <van-cell clickable v-for="(item, index) in unitList" :key="index" :title="item.machineName"
                                @click="selectUnit(item)">
                                <template #right-icon>
                                    <van-radio :name="item.machineCode" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>

            <div class="table-container">
                <div class="table-header">
                    <div class="header-title">{{ selectedUnitName || '请先选择机组' }}</div>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>规格</th>
                            <th>捆包号</th>
                            <th v-if="isLuoLiaoUnit">模具ID</th>
                            <th>净重</th>
                            <th>库位</th>
                            <th>选择</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in hoistingList" :key="index" @click="toggle(item)" 
                            :class="{ 'selected-row': selectedItems.includes(item.packId) }">
                            <td>{{ item.specsDesc }}</td>
                            <td>{{ item.packId }}</td>
                            <td v-if="isLuoLiaoUnit">{{ item.moldId || '-' }}</td>
                            <td>{{ item.netWeight }}</td>
                            <td>{{ item.locationId }}</td>
                            <td>
                                <van-checkbox :model-value="selectedItems.includes(item.packId)" @click.stop>
                                    <template #icon="props">
                                        <span class="iconfont"
                                            :class="selectedItems.includes(item.packId) ? activeIcon : inactiveIcon"></span>
                                    </template>
                                </van-checkbox>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="foot-sticky">
                <van-button type="info" class="foot-sticky-btn" @click="onConfirm">确定</van-button>
            </div>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog, Notify } from "vant";

export default {
    data() {
        return {
            showUnitPicker: false,
            unitList: [],
            selectedUnit: '请选择机组',
            hoistingList: [],
            selectedItems: [],
            selectedUnitName: '',
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            isLuoLiaoUnit: false,
        };
    },

    methods: {
        onClickLeft() {
            this.$router.goBack();
        },

        onUnitConfirm(action, done) {
            if (action === 'confirm') {
                if (!this.selectedUnit) {
                    Dialog.alert({ message: '请选择机组' });
                    done(false);
                    return;
                }
                this.showUnitPicker = false;
                this.getHoistingInfo();
                done(true);
            } else {
                done(true);
            }
        },

        selectUnit(item) {
            this.selectedUnit = item.machineCode;
            this.selectedUnitName = item.machineName;
            this.isLuoLiaoUnit = item.machineName.includes('落料') || item.machineCode.includes('落料');
        },

        toggle(item) {
            const id = item.packId;
            if (this.selectedItems.includes(id)) {
                let index = this.selectedItems.findIndex(item => item == id);
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(id);
            }
        },

        async getHoistingInfo() {
            const params = {
                serviceId: 'S_LI_DS_0023',
                machineCode: this.selectedUnit
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            this.$toast(result.__sys__.msg);
            this.hoistingList = result.demandMaterailList || [];
        },

        async onConfirm() {
            if (this.selectedItems.length === 0) {
                Dialog.alert({ message: '请选择吊运捆包信息' });
                return;
            }

            const dataList = this.hoistingList.filter(item => this.selectedItems.includes(item.packId));
            const params = {
                serviceId: 'S_LI_DS_0024',
                demandMaterailList: dataList,
                machineCode: this.selectedUnit,
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            Notify({ type: 'success', message: '操作成功' });
            await this.getHoistingInfo();
        },

        // 获取机组
        async getUnitList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0019',
                factoryArea: localStorage.getItem("factoryAreaTrue"),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            this.unitList = result.returnList;
            this.showUnitPicker = true;
        },
    }
};
</script>

<style scoped>
.content {
    padding: 16px;
}

.table-container {
    margin-top: 16px;
    padding-bottom: 80px;
    height: 80%;
    overflow-y: auto;
    background-color: #fff;
}

.table-header {
    background-color: #f5f5f5;
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.header-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background-color: #f8f9fa;
    padding: 12px 8px;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
    color: #333;
}

.data-table td {
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.data-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s;
}

.data-table tbody tr:hover {
    background-color: #f0f9ff;
}

.data-table tbody tr.selected-row {
    background-color: #e6f3ff;
}

.data-table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.data-table tbody tr:nth-child(even).selected-row {
    background-color: #e6f3ff;
}

.foot-sticky {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 16px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.foot-sticky-btn {
    width: 100%;
    margin: 0;
}

.activeColor {
    color: #007aff;
}
</style>