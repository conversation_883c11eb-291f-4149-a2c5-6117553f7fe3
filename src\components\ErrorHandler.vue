<template>
  <div class="error-container" v-if="hasError">
    <div class="error-content">
      <div class="error-icon">⚠️</div>
      <h3>页面加载异常</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <van-button type="primary" @click="reload">重新加载</van-button>
        <van-button type="default" @click="goBack" style="margin-left: 10px;">返回首页</van-button>
      </div>
      <div class="device-info" v-if="showDeviceInfo">
        <p><strong>设备信息：</strong></p>
        <p>User Agent: {{ userAgent }}</p>
        <p>屏幕分辨率: {{ screenResolution }}</p>
        <p>是否支持Promise: {{ supportsPromise ? '是' : '否' }}</p>
        <p>是否支持ES6: {{ supportsES6 ? '是' : '否' }}</p>
      </div>
      <van-button 
        type="info" 
        size="small" 
        @click="showDeviceInfo = !showDeviceInfo"
        style="margin-top: 10px;"
      >
        {{ showDeviceInfo ? '隐藏' : '显示' }}设备信息
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorHandler',
  props: {
    error: {
      type: Error,
      default: null
    }
  },
  data: function() {
    return {
      hasError: false,
      errorMessage: '',
      showDeviceInfo: false,
      userAgent: '',
      screenResolution: '',
      supportsPromise: false,
      supportsES6: false
    };
  },
  mounted: function() {
    this.checkDeviceInfo();
    this.setupErrorHandling();
  },
  watch: {
    error: function(newError) {
      if (newError) {
        this.handleError(newError);
      }
    }
  },
  methods: {
    setupErrorHandling: function() {
      var self = this;
      
      // 全局错误处理
      window.addEventListener('error', function(event) {
        self.handleError(event.error || new Error(event.message));
      });
      
      // Promise错误处理
      window.addEventListener('unhandledrejection', function(event) {
        self.handleError(new Error('Promise rejected: ' + event.reason));
      });
      
      // Vue错误处理
      if (this.$root && this.$root.$options) {
        var originalErrorHandler = this.$root.$options.errorHandler;
        this.$root.$options.errorHandler = function(err, vm, info) {
          self.handleError(err);
          if (originalErrorHandler) {
            originalErrorHandler.call(this, err, vm, info);
          }
        };
      }
    },
    
    handleError: function(error) {
      console.error('捕获到错误:', error);
      this.hasError = true;
      
      if (error && error.message) {
        this.errorMessage = error.message;
      } else {
        this.errorMessage = '未知错误，请尝试重新加载页面';
      }
      
      // 常见错误的特殊处理
      if (this.errorMessage.includes('import') || this.errorMessage.includes('module')) {
        this.errorMessage = '页面资源加载失败，可能是网络问题或设备不兼容';
      } else if (this.errorMessage.includes('Promise') || this.errorMessage.includes('async')) {
        this.errorMessage = '您的设备不支持某些新特性，请更新浏览器或联系技术支持';
      } else if (this.errorMessage.includes('getUserMedia') || this.errorMessage.includes('camera')) {
        this.errorMessage = '摄像头功能不可用，请检查权限设置';
      }
    },
    
    checkDeviceInfo: function() {
      try {
        this.userAgent = navigator.userAgent || '未知';
        this.screenResolution = screen.width + 'x' + screen.height;
        this.supportsPromise = typeof Promise !== 'undefined';
        this.supportsES6 = this.checkES6Support();
      } catch (error) {
        console.error('获取设备信息失败:', error);
      }
    },
    
    checkES6Support: function() {
      try {
        // 检查一些ES6特性
        eval('const test = () => {}; let x = {a: 1, ...{b: 2}}; [1,2].find(x => x > 1);');
        return true;
      } catch (error) {
        return false;
      }
    },
    
    reload: function() {
      window.location.reload();
    },
    
    goBack: function() {
      if (this.$router) {
        this.$router.replace('/index');
      } else {
        window.location.href = '/index';
      }
    }
  }
};
</script>

<style scoped>
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f7f7f7;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-content {
  background: white;
  border-radius: 8px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-content h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 18px;
}

.error-message {
  color: #666;
  margin: 0 0 25px 0;
  line-height: 1.5;
  font-size: 14px;
}

.error-actions {
  margin-bottom: 20px;
}

.device-info {
  text-align: left;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
  font-size: 12px;
  line-height: 1.4;
}

.device-info p {
  margin: 5px 0;
  word-break: break-all;
}

.device-info strong {
  color: #333;
}
</style> 