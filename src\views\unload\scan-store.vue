<template>
    <div>
        <van-sticky>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">入库捆包清单</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </van-sticky>
        <div>
            <van-form>
                <van-field name="radio" label="上传货损" class="all-font-size">
                    <template #input>
                        <van-radio-group v-model="radio" direction="horizontal">
                            <van-radio name="00"><template #icon="props">
                                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                                </template>
                                否
                            </van-radio>
                            <van-radio name="10"><template #icon="props">
                                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                                </template>
                                是
                            </van-radio>
                        </van-radio-group>
                    </template>
                </van-field>
                <van-field label="库位编码" placeholder="请输入或扫描库位编码" ref="labelsecond" v-model="locationId"
                    class="all-font-size" @keyup.enter.native="searchLocation" enterkeyhint="enter">
                    <template #right-icon>
                        <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE"
                            @click="searchLocation"></span>
                    </template>
                </van-field>
                <van-field label="库位" placeholder="库位" v-model="locationName" class="all-font-size" readonly />
                <van-field label="捆包" @keyup.enter.native="getPack" class="all-font-size" enterkeyhint="enter">
                    <template #input>
                        <input type="search" class="new-field" placeholder="请输入或扫描捆包号" ref="baleref" v-model="packId" />
                    </template>
                </van-field>

                <van-field label="规格" placeholder="规格" class="all-font-size" v-model="specsDesc" readonly />
                <van-field label="仓库" placeholder="仓库" class="all-font-size" v-model="storeName" readonly />
            </van-form>




            <van-dialog v-model="showUploadImg" title="上传图片" :beforeClose="beforeClose" show-cancel-button>
                <van-field readonly clickable name="picker" :value="cargoDamageValue" label="货损类型"
                    placeholder="点击选择货损类型" @click="showPicker = true" />
                <van-field name="uploader" label="图片上传">
                    <template #input>
                        <van-uploader v-model="uploaderList" :upload-icon="uploadIcon" @oversize="onOversize" multiple
                            :max-count="5" accept="image/*" :max-size="10 * 1024 * 1024" />
                    </template>
                </van-field>
            </van-dialog>


            <van-popup v-model="showPicker" position="bottom">
                <van-checkbox-group v-model="checkboxGroup">
                    <van-cell-group>
                        <van-cell v-for="(item, index) in cargoDamageList" clickable :key="item" :title="` ${item}`"
                            @click="toggle(index)">
                            <template #right-icon>
                                <van-checkbox :name="item" ref="checkboxes">
                                    <template #icon="props">
                                        <span class="iconfont"
                                            :class="props.checked ? activeIcon : inactiveIcon"></span>
                                    </template>
                                </van-checkbox>
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-checkbox-group>
                <div>
                    <van-button type="info" size="large" @click="onConfrimCheck">确认</van-button>
                </div>
            </van-popup>
            <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
                show-cancel-button>
                <div class="dialog-content" style="max-height: 200px;overflow-y: auto;">
                    <div v-if="locationList && locationList.length > 0">
                        <van-list @load="searchLocation">
                            <van-radio-group v-model="radio2">
                                <van-cell-group>
                                    <van-cell v-for="(item, index) in locationList" :key="index" clickable
                                        @click="isChecked(index, item)">
                                        <template #title>
                                            <div class="ware-title">{{ item.locationId }}</div>
                                        </template>
                                        <template #label>
                                            <div>
                                                库位名称：{{ item.locationName }}</div>
                                        </template>
                                        <template #right-icon>
                                            <van-radio :name="index"><template #icon="props">
                                                    <span class="iconfont"
                                                        :class="props.checked ? activeIcon : inactiveIcon"></span>
                                                </template>
                                            </van-radio>
                                        </template>
                                    </van-cell>
                                </van-cell-group>
                            </van-radio-group>
                        </van-list>
                    </div>
                    <div v-else>
                    </div>
                </div>
            </van-dialog>
        </div>

        <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
            title-active-color="#007aff" sticky>
            <van-tab title="匹配入库计划" :title-style="fontSize">
                <div v-if="packList && packList.length > 0">
                    <div class="inlist-content">
                        <div class="detail_textarea">
                            <div class="detail_text" style="padding-bottom: 10px">
                                <div class="fourline-blue"></div>
                                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                                    已扫捆包合计 :
                                    <span class="span-count">{{ packList.length }}</span>
                                </div>
                            </div>
                            <div class="detail_text" style="padding-bottom: 10px">
                                <div class="fourline-blue"></div>
                                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                                    总件数 :
                                    <span class="span-count">{{ totalPieceNum }}</span>
                                    总吨数 :
                                    <span class="span-count">{{
                                        formatNumber(totalNetWeight)
                                    }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="content-cell">
                            <van-swipe-cell v-for="(item, index) in packList" :key="index">
                                <div style="text-align: left">
                                    <div class="detail_textarea">
                                        <div class="border_top">
                                            <div>
                                                <span class="check-spec">捆包：</span>
                                                <span class="check-val div-pack">{{
                                                    item.packId
                                                }}</span>
                                            </div>
                                            <div>
                                                <span class="check-spec">入库计划：</span>
                                                <span class="check-val">{{ item.putinPlanId }}</span>
                                            </div>
                                            <div>
                                                <span class="check-spec">钢厂订单号：</span>
                                                <span class="check-val">{{ item.factoryOrderNum }}</span>
                                            </div>
                                            <div>
                                                <span class="check-spec div-tips">牌号：</span>
                                                <span class="check-val div-tips">{{ item.shopsign }}</span>
                                            </div>
                                            <div v-show="item.cargoDamageType.length > 0">
                                                <span class="check-spec div-tips">货损类型：</span>
                                                <span class="check-val div-tips">{{
                                                    item.cargoDamageType.toString()
                                                }}</span>
                                            </div>
                                            <div class="content-spec">
                                                <div>
                                                    <span class="check-spec">库位：</span>
                                                    <span class="check-val">{{ item.location }}</span>
                                                </div>
                                                <div v-show="item.pictureDetail.length > 0">
                                                    <van-button type="info" size="mini"
                                                        @click="showImg(item, index)">图片</van-button>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="check-spec">规格：</span>
                                                <span class="check-val">{{ item.specsDesc }}</span>
                                            </div>
                                            <div class="content-spec">
                                                <div>
                                                    <span class="check-spec">净/毛重：</span>
                                                    <span class="check-val">{{ item.netWeight }}/{{ item.grossWeight
                                                        }}</span>
                                                </div>
                                                <div>
                                                    <span class="check-spec">贸易：</span>
                                                    <span class="check-val">{{
                                                        item.tradeCodeName
                                                    }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <template #right>

                                    <button class="swiper-btn-delete" @click="deleteListItem(index)">
                                        <span class="swiper-text">删除</span>
                                    </button>
                                </template>
                            </van-swipe-cell>
                        </div>
                    </div>
                    <div v-if="packList && packList.length > 0">
                        <div class="mui-input-row3" v-show="isShowBottom">
                            <button type="button" class="mui-btn3" v-preventReClick="3000" @click="onInStorage">
                                入&nbsp; &nbsp; &nbsp;&nbsp;库
                            </button>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="mui-input-row3" v-show="isShowBottom">
                        <button type="button" class="mui-btn3" v-preventReClick="3000" @click="onInStorage">
                            入&nbsp; &nbsp; &nbsp;&nbsp;库
                        </button>
                    </div>
                </div>
            </van-tab>
            <van-tab title="未匹配" :title-style="fontSize">
                <div v-if="noInPackList">
                    <div class="inlist-content">
                        <div class="content-cell">
                            <div class="detail_text" style="padding-bottom: 10px">
                                <div class="fourline-blue"></div>
                                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                                    已扫捆包合计 :
                                    <span class="span-count">{{ noInPackList.length }}</span>
                                </div>
                            </div>


                            <van-swipe-cell v-for="(item, index) in noInPackList" :key="index">
                                <div style="text-align: left">
                                    <div class="detail_textarea">
                                        <div class="detail_text">
                                            <div class="fourtext3">{{ item.packId }}</div>
                                        </div>

                                        <div class="border_top">
                                            <div>
                                                <span class="check-spec" style="color: #ff0000">未匹配入库计划</span>
                                            </div>
                                            <div v-if="item.factoryOrderNum">
                                                <span class="check-spec">钢厂订单号：</span>
                                                <span class="check-val">{{ item.factoryOrderNum }}</span>
                                            </div>

                                            <div v-show="item.cargoDamageType.length > 0">
                                                <span class="check-spec div-tips">货损类型：</span>
                                                <span class="check-val div-tips">{{
                                                    item.cargoDamageType.toString()
                                                }}</span>
                                            </div>
                                            <div class="content-spec">
                                                <div>
                                                    <span class="check-spec">库位</span>
                                                    <span class="check-val">{{ item.location }}</span>
                                                </div>
                                                <div v-show="item.pictureDetail.length > 0">
                                                    <van-button type="info" size="mini"
                                                        @click="showImg(item, index)">图片</van-button>
                                                </div>
                                            </div>
                                            <div class="content-spec">
                                                <div v-if="item.specsDesc">
                                                    <span class="check-spec">规格：</span>
                                                    <span class="check-val">{{ item.specsDesc }}</span>
                                                </div>
                                                <div v-if="item.netWeight">
                                                    <span class="check-spec">净重：</span>
                                                    <span class="check-val">{{ item.netWeight }}</span>
                                                </div>
                                            </div>

                                            <div class="content-spec" v-if="segNo.includes('JC')">
                                                <div>
                                                    <span class="check-spec">委托单位</span>
                                                    <span class="check-val">{{ item.clientName }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <template #right>
                                    <button v-if="segNo.includes('JC')" class="swiper-btn-edit2"
                                        @click="editListItem2(item, index)">
                                        <span class="swiper-text">修改</span>
                                    </button>
                                    <button class="swiper-btn-delete2" @click="deleteListItem2(index)">
                                        <span class="swiper-text">删除</span>
                                    </button>
                                </template>
                            </van-swipe-cell>
                        </div>
                        <div class="mui-input-row3" style="margin: 0" v-show="isShowBottom">
                            <button type="button" class="mui-btn3" v-preventReClick="3000" @click="unplannedWarehous">
                                无计划入库
                            </button>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
                    </div>
                </div>
            </van-tab>
        </van-tabs>

        <van-popup v-model="showImgList" :style="{ height: '50%' }" position="bottom">
            <div class="img-list">
                <div v-for="(item, index) in imgList" :key="index" class="div-item">
                    <van-badge color="#007aff">
                        <van-image v-if="item.loaded" :src="item.docUrl" alt="图片" style="width: 100px; height: 100px;"
                            @click="seeBigImg(item, index)" />
                        <div class="placeholder" v-else>
                            <span>加载中...</span>
                        </div>

                        <van-image :src="item.docUrl" style="display: none;" @load="onImageLoad(index)"
                            @error="onImageError(index)" />


                        <template #content>
                            <div class="iconfont  icon-qingchu " style="font-size: 15px;" @click="deleteImg(index)">
                            </div>
                        </template>
                    </van-badge>
                </div>
            </div>
        </van-popup>
        <!-- </div> -->


        <van-image-preview v-model="showBigImage" :images="images" :start-position="imgIndex" @change="onChange">
            <template v-slot:index>第{{ imgIndex + 1 }}页</template>
        </van-image-preview>

        <van-dialog v-model="isShowEdit" title="修改" show-cancel-button :beforeClose="colseEditData">
            <van-field label="捆包" class="all-font-size" v-model="editData.packId" readonly />

            <van-field label="库位编码" class="all-font-size" v-model="editData.location" readonly />
            <van-field label="库位" class="all-font-size" v-model="editData.locationName" readonly />

            <van-field label="规格" class="all-font-size" v-model="editData.specsDesc" placeholder="请填写规格" />
            <van-field label="重量（吨）" class="all-font-size" type="number" v-model="editData.netWeight"
                placeholder="请填写重量" />
            <van-field label="委托单位" class="all-font-size" v-model="editData.clientName" placeholder="请填写委托单位" />
        </van-dialog>

        <van-dialog v-model="isShowTruck" title="行车" show-cancel-button :beforeClose="confirmTruck">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="truckRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.craneName" v-for="(item, index) in loadTruckOperatorList"
                            :key="index" @click="truckRadio = item.craneId">
                            <template #right-icon>
                                <van-radio :name="item.craneId" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>

<script>
import {
    Dialog
} from "vant";
import { post } from '../../api/base-service';
import uploadIcon from '@/assets/imgs/upload-icon.png'
export default {
    data() {
        return {
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            radio: '00', // 上传货损默认否
            packId: '',
            locationId: '',
            locationName: '',
            specsDesc: '',
            storeName: '', // 仓库
            uploaderList: [],
            cargoDamageValue: '',
            showUploadImg: false,
            showPicker: false,
            uploadIcon: uploadIcon,
            checkboxGroup: [],
            cargoDamageList: [],
            localtionIdShow: false,
            checkLocaltion: {},
            locationList: [],
            active: 2,
            fontSize: " font-size: 16px;",
            packList: [],
            isShowBottom: true,
            noInPackList: [],
            radio2: -1,
            isMatch: false,
            matchPack: '',
            vehicleNo: '', //车牌号
            carTraceNo: '',
            handPointId: '',
            docList: [], // 图片url
            loadingPack: {}, // 待加入到list的捆包
            showImgList: false,
            imgList: [],
            checkIndex: -1,
            imgIndex: -1,
            images: [],
            showBigImage: false,
            isShowEdit: false,
            editData: '',
            editIndex: -1,
            isShowTruck: false,
            truckRadio: '',
            loadTruckOperatorList: [],
            craneId: '',
            segNo: localStorage.getItem('segNo'),
            factoryBuilding: localStorage.getItem('factoryBuilding'),
            currentHandPointId: '',
            loadId: '',
        };
    },
    async created() {
        this.vehicleNo = localStorage.getItem('carNumber');
        this.carTraceNo = localStorage.getItem('carTraceNo');
        this.handPointId = localStorage.getItem('handPointId');
        this.currentHandPointId = localStorage.getItem('currentHandPointId');
        this.loadId = localStorage.getItem('loadId');
        if (this.segNo.includes('JC') && this.factoryBuilding.includes('F1')) {
            await this.getLoadTruckOperatorList();
        }
    },
    computed: {
        totalPieceNum() {
            let allPiece = this.packList.reduce(
                (sum, e) => sum + Number(e.pieceNum || 0),
                0
            );
            return allPiece;
        },
        totalNetWeight() {
            let allWeight = this.packList.reduce(
                (sum, e) => sum + Number(e.netWeight || 0),
                0
            );
            return allWeight;
        }
    },
    methods: {
        formatNumber: (val) => Math.round(val * 1000) / 1000, // 保留3位小数
        clearData() {
            this.packId = '';
            this.locationId = '';
            this.noInPackList = [];
            this.locationName = '';
            this.packList = [];
            this.storeName = '';
            this.specsDesc = '';
        },

        onClickLeft() {
            if (this.packList.length > 0 || this.noInPackList.length > 0) {
                Dialog.confirm({
                    title: "提示",
                    message: "清单里有数据未提交确认退出？"
                })
                    .then(() => {
                        this.toEnd();
                    })
                    .catch(() => { });
                return;
            }
            this.toEnd();
        },

        //校验上传图片大小
        onOversize(file) {
            this.$toast.fail("文件大小不能超过10MB，请重新选择！");
        },

        /** 确定关闭选择货损类型弹出框事件 */
        onConfrimCheck() {
            this.showPicker = false;
            this.cargoDamageValue = this.checkboxGroup.toString();
        },

        /**
         * 库位确认事件
         */
        onConfirmItem() {
            this.location = this.checkLocaltion.locationId;
            this.locationId = this.checkLocaltion.locationId;
            this.locationName = this.checkLocaltion.locationName;
            this.radio2 = -1;
            this.checkLocaltion = [];
            this.locationList = [];

            if (!this.packId) {
                return;
            }
            this.saveBale();
        },

        onCancelItem() {
            this.locationList = [];
        },

        // 匹配入库计划删除
        deleteListItem(index) {
            this.$delete(this.packList, index);
        },

        /**
         * 修改
         * @param index 
         */
        editListItem2(item, index) {
            this.isShowEdit = true;
            this.editData = {
                ...item,
            };
            this.editIndex = index;
        },

        colseEditData(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.editData.specsDesc) {
                Dialog.alert({
                    message: "请填写规格"
                })
                done(false);
                return;
            }

            if (!this.editData.netWeight || this.editData.netWeight == 0) {
                Dialog.alert({
                    message: "请填写重量"
                })
                done(false);
                return;
            }

            this.noInPackList[this.editIndex] = this.editData;
            this.editIndex = -1;
            this.editData = '';
            done();
        },

        deleteListItem2(index) {
            //删除数组中值
            this.$delete(this.noInPackList, index);
        },

        isChecked(index, item) {
            this.radio2 = index;
            this.checkLocaltion = item;
        },

        /**
         * 打开图片
         * @param item 
         * @param index 
         */
        showImg(item, index) {
            this.showImgList = true;
            this.imgList = item.pictureDetail;
            this.checkIndex = index;
        },

        seeBigImg(item, index) {
            this.imgIndex = index;
            this.showBigImage = true;
            this.images = this.imgList.map(i => i.docUrl);
        },

        // 图片加载成功
        onImageLoad(index) {
            this.docList[index].loaded = true;
        },

        // 图片加载失败
        onImageError(index) {
            this.$toast.fail("图片加载失败");
        },

        /**
         * 删除图片
         * @param val 
         */
        deleteImg(val) {
            if (this.active == 0) {
                this.$delete(this.packList[this.checkIndex].pictureDetail, val);
            } else {
                this.$delete(this.noInPackList[this.checkIndex].pictureDetail, val);
            }

        },

        onChange(index) {
            this.imgIndex = index;
        },

        // 无计划入库扫描捆包之后, 自动聚焦到库位
        changeFocus() {
            this.$nextTick(() => {
                this.$refs.labelsecond.focus();
            });
        },

        toEnd() {
            this.$router.togo({
                name: "end-load",
                params: {
                    vehicleNo: this.vehicleNo,
                    carTraceNo: this.carTraceNo,
                    handPointId: this.handPointId,
                },
            });
        },

        saveBale() {
            //获取的值添加到list里面
            let flag = this.noInPackList.some(item => item.packId == this.packId);
            if (flag) {
                this.$toast("此捆包号已添加，请重新扫描其他捆包号");
                return;
            }

            // 有计划无库位
            if (this.isMatch && this.matchPack) {
                this.packList.push({
                    ...this.matchPack,
                    location: this.locationId,
                    locationName: this.locationName,
                    cargoDamageType: this.checkboxGroup || [],
                    pictureDetail: this.docList || [],
                });
                this.active = 0;
                this.isMatch = false;
                this.matchPack = '';
                this.packId = '';
                return;
            }

            let data = {
                packId: this.packId,
                segNo: localStorage.getItem('segNo'),
                segName: localStorage.getItem('segName'),
                factoryArea: localStorage.getItem('factoryArea'),
                factoryName: localStorage.getItem('factoryName'),
                userId: localStorage.getItem('userId'),
                userName: localStorage.getItem('userName'),
                warehouseCode: localStorage.getItem('warehouseCode'),
                warehouseName: localStorage.getItem('warehouseName'),
                location: this.locationId,
                locationName: this.locationName,
                cargoDamageType: this.checkboxGroup || [],
                pictureDetail: this.docList || [],
            };
            this.packId = '';
            this.noInPackList.unshift(data);
            this.active = 1;
            this.editListItem2(data, 0);


        },

        /**
         * 选中货损
         * @param index 
         */
        toggle(index) {
            this.$refs.checkboxes[index].toggle();
        },

        /** 计划入库 */
        async onInStorage() {
            const perName = localStorage.getItem("perName");
            const perNo = localStorage.getItem("perNo");
            const userId = localStorage.getItem('userId');
            const userName = localStorage.getItem('userName');
            const segNo = this.segNo;
            let params = {
                serviceId: 'S_LI_RL_0020',
                rowList: this.packList.map(p => {
                    return {
                        ...p,
                        vehicleNo: this.vehicleNo,
                        carTraceNo: this.carTraceNo,
                        userId,
                        userName,
                        perNo,
                        perName,
                        currentHandPointId: this.currentHandPointId,
                        loadId: this.loadId,
                    }
                }),
                warehouseCode: localStorage.getItem("warehouseCode"),
                warehouseName: localStorage.getItem('warehouseName'),
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                carTraceNo: this.carTraceNo,
                allocateVehicleNo: localStorage.getItem('allocateVehicleNo'), // 配单号
            };

            if (segNo.includes('JC') && this.factoryBuilding.includes('F1')) {
                params = {
                    ...params,
                    craneId: this.craneId,
                }
            }

            const result = await post(params);
            if (!result || result.__sys__.status == -1 || !result.putinIdList) {
                return;
            }

            this.clearData();
            // 根据条件判断是否需要执行跳转
            const needRedirect = !(segNo.includes('JC') && this.factoryBuilding.includes('F1'))

            // 需要跳转时执行跳转方法
            if (needRedirect) {
                this.toEnd()
            }

            // 统一处理提示（无论是否跳转都显示）
            this.$toast(result.__sys__.msg)

        },

        /** 无计划入库 */
        async unplannedWarehous() {
            const perName = localStorage.getItem("perName");
            const perNo = localStorage.getItem("perNo");
            const segNo = localStorage.getItem("segNo");

            const errorList = this.noInPackList.filter(n => !n.specsDesc || !n.netWeight).map(n => n.packId);
            if (errorList.length > 0 && this.segNo.includes('JC')) {
                Dialog.alert({
                    message: `捆包号${errorList.join(',')}没有填写规格或者重量, 请检查`,
                });
                return;
            }
            let params = {
                serviceId: 'S_LI_RL_0021',
                rowList: this.noInPackList.map(n => {
                    return {
                        ...n,
                        carTraceNo: this.carTraceNo,
                        vehicleNo: this.vehicleNo,
                        perName,
                        perNo,
                        currentHandPointId: this.currentHandPointId,
                        loadId: this.loadId,
                    }
                }),
                warehouseCode: localStorage.getItem("warehouseCode"),
                warehouseName: localStorage.getItem('warehouseName'),
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                location: this.locationId,
                locationName: this.locationName,
            };

            if (segNo.includes('JC') && localStorage.getItem('factoryArea').includes('F1')) {
                params = {
                    ...params,
                    craneId: this.craneId,
                }
            }

            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            this.clearData();
            this.$toast(result.__sys__.msg);

            // 根据条件判断是否需要执行跳转
            const needRedirect = !(segNo.includes('JC') && this.factoryBuilding.includes('F1'))
            // 需要跳转时执行跳转方法
            if (needRedirect) {
                this.toEnd();
            }
        },

        // 查询捆包
        async getPack() {
            const segNo = this.segNo;
            const factoryBuilding = localStorage.getItem('factoryBuilding');
            if (segNo.includes('JC') && 'F1'.includes(factoryBuilding) && (this.noInPackList.length == 1 || this.packList.length == 1)) {
                this.$toast("只能入库一个捆包");
                return;
            }

            //获取的值添加到list里面
            let flag = this.packList.some(item => item.packId == this.packId);
            if (flag) {
                this.$toast("此捆包号已添加，请重新扫描其他捆包号");
                return;
            }

            let query = {
                packId: this.packId,
                locationId: this.locationId,
                serviceId: 'S_LI_RL_0018',
                warehouseCode: localStorage.getItem('warehouseCode'),
                factoryArea: localStorage.getItem("factoryArea"),
            };

            const result = await post(query);
            if (!result || !result.__blocks__) {
                return;
            }

            // 查询行车 重庆宝钢且为一厂
            if (segNo.includes('JC') && localStorage.getItem('factoryArea').includes('F1') && this.craneId) {
                query = {
                    serviceId: 'S_LI_DS_0012',
                    craneId: this.craneId,
                };
                const resData = await post(query);
                if (!resData || !resData.__sys__.status == -1) {
                    return;
                }

                if (resData.locationId) {
                    this.locationId = resData.locationId;
                    this.locationName = resData.locationName;
                }
            }

            const res = result.result;
            // 如果为0,就是无计划入库
            if (!res || res.length == 0) {
                if (this.radio == '10') {
                    // 查询货损
                    this.showUploadImg = true;
                    this.cargoDamageValue = '';
                    this.uploaderList = [];
                    this.$refs.checkboxes = [];
                    // 查询货损类型
                    await this.getCargoDamageList();
                    return;
                }


                if (!this.locationId) {
                    this.changeFocus();
                    return;
                }

                let noInPack = {
                    packId: this.packId,
                    segNo: localStorage.getItem('segNo'),
                    segName: localStorage.getItem('segName'),
                    factoryArea: localStorage.getItem('factoryArea'),
                    factoryName: localStorage.getItem('factoryName'),
                    userId: localStorage.getItem('userId'),
                    userName: localStorage.getItem('userName'),
                    warehouseCode: localStorage.getItem('warehouseCode'),
                    warehouseName: localStorage.getItem('warehouseName'),
                    location: this.locationId,
                    locationName: this.locationName,
                    cargoDamageType: this.checkboxGroup || [],
                    pictureDetail: this.docList || [],
                };
                this.packId = '';
                this.noInPackList.unshift(noInPack);
                this.active = 1;
                this.editListItem2(noInPack, 0);
                return;
            }

            const data = res[0];
            this.specsDesc = data.specsDesc;
            this.storeName = data.warehouseCode;
            if (!data.locationId && !this.locationId) {
                this.isMatch = true;
                this.matchPack = data;

                if (this.radio == '10') {
                    // 查询货损
                    this.showUploadImg = true;
                    this.cargoDamageValue = '';
                    this.uploaderList = [];
                    this.$refs.checkboxes = [];
                    // 查询货损类型
                    await this.getCargoDamageList();
                    return;
                }
                this.changeFocus();
                return;
            }

            if (this.radio == '10') {
                this.isMatch = true;
                this.matchPack = data;
                // 查询货损
                this.showUploadImg = true;
                this.cargoDamageValue = '';
                this.uploaderList = [];
                this.$refs.checkboxes = [];
                // 查询货损类型
                await this.getCargoDamageList();
                return;
            }

            let packObj = {
                ...data,
                cargoDamageType: [],
                pictureDetail: [],
                location: data.locationId && data.locationId != ' ' ? data.locationId : this.locationId,
                locationName: data.locationId && data.locationId != ' ' ? data.locationName : this.locationName,
            };
            this.packId = '';
            // 添加到计划入库列表
            this.packList.push(packObj);
            this.active = 0;
        },

        // 查询库位
        async searchLocation() {
            this.localtionIdShow = true;
            const params = {
                serviceId: this.segNo.includes('KF') ? "S_LI_RL_0019" : "S_LI_RL_0146",
                warehouseCode: localStorage.getItem("warehouseCode"),
                locationId: this.locationId,
                location: this.locationId,
                startSize: this.offset,
                length: 10
            };
            const result = await post(params);
            this.locationList = [];
            if (!result || !result.result) {
                return;
            }

            if (result.result.length == 1) {
                this.localtionIdShow = false;
                this.isChecked(0, result.result[0]);
                this.onConfirmItem();
                this.$nextTick(() => {
                    this.$refs.baleref.focus();
                });
                return;
            }
            this.locationList = result.result;
        },

        /** 图片弹出框确认关闭事件 */
        async beforeClose(action, done) {
            if (action === "confirm") {
                if (this.uploaderList.length > 0) {
                    let base64ImgList = this.uploaderList.map(f => f.content);
                    let isRes = await this.uploadImage(base64ImgList);
                    done(isRes);
                    if (this.locationId) {
                        this.saveBale();
                        return;
                    }
                    this.changeFocus();
                } else {
                    done();
                }
            } else {
                done();
            }
        },

        // 上传图片
        async uploadImage(val) {
            const params = {
                serviceId: "S_LI_RL_0053",
                pictureList: val,
                packId: this.packId,
            };
            try {
                let res = await post(params);
                console.log(res);
                if (!res || !res.list || res.list.length == 0) {
                    return false;
                }
                this.docList = res.list.map(r => {
                    // 找到最后一个斜杠的位置和最后一个点的位置
                    const lastSlashIndex = r.lastIndexOf('/');
                    const lastDotIndex = r.lastIndexOf('.');

                    // 提取文件名（去掉后缀）
                    const imageName = r.substring(lastSlashIndex + 1, lastDotIndex);

                    return {
                        packId: this.packId,
                        docUrl: r,
                        thumbnailDocId: '',
                        thumbnailDocUrl: '',
                        docId: imageName,
                        loaded: false,
                    }
                });
                return true;
            } catch (error) {
                console.error("上传图片失败：", error); // 打印错误日志
                return false; // 返回 false 表示失败
            }
        },

        /**
         * 查询货损类型
         */
        async getCargoDamageList() {
            const params = {
                serviceId: 'S_LI_RL_0054',
            };
            const res = await post(params);
            if (!res || res.result.length == 0) {
                return;
            }
            this.cargoDamageList = res.result;


        },

        /**
         * 行车确认事件
         * @param action 
         * @param done 
         */
        async confirmTruck(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.truckRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.loadTruckOperatorList.find(p => p.craneId == this.truckRadio);
            if (!currentData) {
                done();
                return;
            }

            this.craneId = currentData.craneId;
            done(true);
        },

        async getLoadTruckOperatorList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0017',
                handPointId: this.handPointId,
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.craneIdList.length == 0) {
                return;
            }

            if (result.craneIdList.length == 1) {
                this.loadTruckOperatorList = result.craneIdList;
                this.craneId = result.craneIdList[0].craneId;
                return;
            }

            this.loadTruckOperatorList = result.craneIdList;
            this.isShowTruck = true;
        },


    },
}
</script>

<style lang="less" scoped>
.activeColor {
    color: #007aff;
}

.inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
        width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
        background-color: #d9d9d9;
    }
}

/deep/ .van-tabs__wrap {
    height: 40px;
}

/deep/ .van-empty__description {
    margin-top: 16px;
    padding: 0 60px;
    color: #969799;
    font-size: 16px;
    line-height: 20px;
}

/deep/ .van-tabs__nav--card {
    box-sizing: border-box;
    height: 40px;
    margin: 0;
    //  border: 1px solid #ee0a24;
    border-radius: 2px;
}

.title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
}

.img-list {
    width: 98%;
    display: flex;
    flex-wrap: wrap;
}

.div-item {
    padding: 8px 5px 5px;
}

.content-cell {
    margin-bottom: 100px;
}

.check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
}

.content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
}

.div-flex {
    display: flex;
    justify-content: space-between;
}

.swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
}

.swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
}

.swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
}

.swiper-btn-update2 {
    width: 56px;
    height: 100%;
    background: #0000ff;
    opacity: 1;
}

.swiper-btn-delete2 {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
}

.swiper-btn-edit2 {
    width: 56px;
    height: 100%;
    background: #007AFF;
    opacity: 1;
}

.div-pack {
    color: #0000ff;
}

.div-tips {
    color: #d33017;
}

.placeholder {
    width: 100px;
    height: 100px;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #999;
}
</style>