<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">选择车牌号</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div class="in-content" style="height: 52%;overflow-y: auto;padding-bottom: 0;">
            <van-cell is-link title="装卸点" size="large" v-model="targetHandPointId" @click="openHandPoint" />


            <van-cell title="车牌号" size="large" v-model="carNumber" />

            <!-- 手机号显示区域 -->
            <van-cell v-if="driverTel" title="手机号" size="large" v-model="driverTel" />
            
            <div style="text-align: right;margin-right: 12px;">
                <van-button round plain type="info" size="small" @click="getPendCall()">待叫号车辆</van-button>
                <van-button round plain type="info" size="small" @click="getCall()">已叫号车辆</van-button>
                <van-button round plain type="info" size="small" @click="getOtherCar()">作业中车辆</van-button>
            </div>

            <van-radio-group v-model="radioCarNumber" style="margin-top: 1em;">
                <van-cell-group>
                    <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in carNumberList" :key="index"
                        @click="onSelect(item, index)">
                        <template #label>
                            <div class="wrap-text">
                                {{
                                    item.handPointName
                                        || (
                                            item.handPointList && item.handPointList.length > 0
                                                ? (Array.isArray(item.handPointList)
                                                    ? item.handPointList.join(',')
                                                    : item.handPointList)
                                                : ''
                                        )
                                }}
                            </div>
                        </template>
                        <template #right-icon>
                            <van-radio :name="item.vehicleNo" />
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>

        </div>

        <div class="foot-sticky-unload">
            <div class="button-container">
                <van-button type="info" size="large" class="foot-sticky-btn-unload" v-for="(item, index) in btnList"
                    :key="index" @click="item.click">
                    {{ item.name }}
                </van-button>
            </div>
        </div>

        <van-dialog v-model="isShowPonit" title="选择装卸点" show-cancel-button :beforeClose="closeHandPonit">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="handPonitRadio">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in pointList" :key="index" :title="item.handPointName"
                            :label="item.handPointId" @click="handPonitRadio = item.handPointId">
                            <template #right-icon>
                                <van-radio :name="item.handPointId" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>


        <van-dialog v-model="isShowCall" title="强制叫号" show-cancel-button :beforeClose="mandatoryCall">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="radio">
                    <van-cell-group>
                        <!-- radio = item.vehicleNo -->
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in callCarList" :key="index"
                            :label="item.targetHandPointName" @click="selectMandatory(item)">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>

        <van-dialog v-model="isShowUninstall" title="未装离厂" show-cancel-button :beforeClose="confirmUninstall">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="uninstallRadio">
                    <van-cell-group>
                        <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in uninstallList" :key="index"
                            @click="uninstallRadio = item.vehicleNo">
                            <template #right-icon>
                                <van-radio :name="item.vehicleNo" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog, Notify } from "vant";
export default {
    data() {
        return {
            carNumber: '',
            isShowCarNumber: false,
            carNumberActions: [],
            targetHandPointId: '', // 装卸点
            carNumberList: [], // 车牌号数组
            carTraceNo: '', // 车辆跟踪号
            allocateVehicleNo: '', // 配单号
            driverTel: '',
            driverName: '',
            handPointId: '',
            isShowCall: false,
            radio: '',
            callCarList: [],
            pointList: [],
            ponitActions: [],
            isShowPonit: false,
            handPonitRadio: '',
            radioCarNumber: '',
            carInPoint: [],
            status: '',
            statusType: 1,
            oldHandPointId: '',
            // 按钮配置
            btnList: [{
                name: '启动叫号',
                click: async () => await this.startCallNumber(),
            }, {
                name: '更改叫号装卸点',
                click: async () => await this.changeCallNumber(),
            }, {
                name: '开始卸货',
                click: async () => await this.toUnload("2"),
            }, {
                name: '开始装货',
                click: async () => await this.toUnload("1"),
            }, {
                name: 'IMC出库',
                click: async () => await this.outboundConfirmation(),
            }, {
                name: '取消叫号',
                click: async () => await this.cancelCalling(),
            }, {
                name: '未装离厂',
                click: async () => await this.uninstallFactory(),
            }],
            isShowTruck: false,
            truckRadio: '',
            loadTruckOperatorList: [],
            isPendCall: false,
            isStartCall: false,
            // 未装离厂相关
            isShowUninstall: false,
            uninstallRadio: '',
            uninstallList: [],
        };
    },
    async created() {
        // 数据一致性检查
        this.validateCacheConsistency();

        const routPar = this.$route.params;
        if (routPar && routPar.carNumber) {
            // 使用统一的数据更新方法
            const success = this.updateCarInfo({
                vehicleNo: routPar.carNumber,
                carTraceNo: routPar.carTraceNo,
                allocateVehicleNo: routPar.allocateVehicleNo,
                handPointName: routPar.handPointName,
            });

            if (!success) {
                this.$toast('数据初始化失败');
                return;
            }

            this.driverName = routPar.driverName;
            this.driverTel = routPar.driverTel;
            this.statusType = 5;
            this.carNumberList = [{
                vehicleNo: routPar.carNumber,
                carTraceNo: routPar.carTraceNo,
                allocateVehicleNo: routPar.allocateVehicleNo,
                handPointList: routPar.handPointName,
            }];
            this.radioCarNumber = routPar.carNumber;

            await this.getPendCallCarPoint();
            this.isStartCall = true;
            return;
        }
        this.isStartCall = false;
    },
    methods: {
        /**
         * 统一的缓存管理方法
         */
        setCacheData(data) {
            try {
                Object.keys(data).forEach(key => {
                    localStorage.setItem(key, data[key]);
                });
                return true;
            } catch (error) {
                console.error('缓存设置失败:', error);
                this.$toast('缓存设置失败');
                return false;
            }
        },

        /**
         * 清理缓存数据
         */
        clearCacheData(keys) {
            try {
                keys.forEach(key => {
                    localStorage.removeItem(key);
                });
                return true;
            } catch (error) {
                console.error('缓存清理失败:', error);
                return false;
            }
        },

        /**
         * 清除所有捆包相关缓存
         */
        clearAllBundleCache() {
            try {
                const keys = Object.keys(localStorage);
                const bundleCacheKeys = keys.filter(key => key.startsWith('bundle_cache_'));

                bundleCacheKeys.forEach(key => {
                    localStorage.removeItem(key);
                });

                return true;
            } catch (error) {
                console.error('清除所有捆包缓存失败:', error);
                return false;
            }
        },

        /**
         * 清除指定车辆的捆包缓存
         */
        clearVehicleBundleCache(carNumber) {
            try {
                const keys = Object.keys(localStorage);
                const vehicleCacheKeys = keys.filter(key =>
                    key.startsWith('bundle_cache_') && key.includes(`_${carNumber}_`)
                );

                vehicleCacheKeys.forEach(key => {
                    localStorage.removeItem(key);
                });

                return true;
            } catch (error) {
                console.error(`清除车辆 ${carNumber} 的捆包缓存失败:`, error);
                return false;
            }
        },

        /**
         * 清除指定车辆和装卸点的捆包缓存（避免不同装卸点间的缓存冲突）
         */
        clearVehicleHandPointBundleCache(carNumber, oldHandPointId, newHandPointId) {
            try {
                if (oldHandPointId === newHandPointId) {
                    return true;
                }

                const keys = Object.keys(localStorage);
                // 清除该车辆在旧装卸点的所有缓存
                const oldCacheKeys = keys.filter(key =>
                    key.startsWith('bundle_cache_') &&
                    key.includes(`_${carNumber}_`) &&
                    key.includes(`_${oldHandPointId}`)
                );

                oldCacheKeys.forEach(key => {
                    localStorage.removeItem(key);
                });

                return true;
            } catch (error) {
                console.error('清除车辆装卸点缓存失败:', error);
                return false;
            }
        },

        /**
         * 数据一致性检查
         */
        validateCacheConsistency() {
            const cacheKeys = ['handPointId', 'carNumber', 'carTraceNo', 'allocateVehicleNo', 'targetHandPointId'];
            let hasInconsistency = false;

            cacheKeys.forEach(key => {
                const cacheValue = localStorage.getItem(key);
                const componentValue = this[key];

                // 判断值是否为空（undefined, null, 空字符串, 字符串"undefined"）
                const isCacheEmpty = !cacheValue || cacheValue === 'undefined' || cacheValue === '';
                const isComponentEmpty = !componentValue || componentValue === '';

                // 如果两个值都为空，认为是一致的
                if (isCacheEmpty && isComponentEmpty) {
                    return; // 跳过这个键的检查
                }

                // 如果一个为空一个不为空，或者两个都不为空但不相等，则不一致
                if (isCacheEmpty !== isComponentEmpty ||
                    (!isCacheEmpty && !isComponentEmpty && cacheValue !== componentValue)) {
                    console.warn(`数据不一致: ${key}, 组件值: "${componentValue}", 缓存值: "${cacheValue}"`);
                    hasInconsistency = true;
                }
            });

            return !hasInconsistency;
        },

        /**
         * 安全的数据更新方法
         */
        updateCarInfo(carData) {
            // 创建备份
            const backup = {
                carNumber: this.carNumber,
                carTraceNo: this.carTraceNo,
                allocateVehicleNo: this.allocateVehicleNo,
                handPointId: this.handPointId,
                targetHandPointId: this.targetHandPointId,
            };

            try {
                // 检查是否需要清理捆包缓存
                const isCarNumberChanged = backup.carNumber && backup.carNumber !== carData.vehicleNo;
                const isHandPointChanged = backup.handPointId && backup.handPointId !== carData.handPointId;

                // 如果车牌号改变，清除旧车辆的所有捆包缓存
                if (isCarNumberChanged) {
                    this.clearVehicleBundleCache(backup.carNumber);
                }

                // 如果同一车辆但装卸点改变，清除旧装卸点的缓存
                if (!isCarNumberChanged && isHandPointChanged && backup.carNumber) {
                    this.clearVehicleHandPointBundleCache(backup.carNumber, backup.handPointId, carData.handPointId);
                }

                // 更新组件状态
                this.carNumber = carData.vehicleNo || '';
                this.carTraceNo = carData.carTraceNo || '';
                this.allocateVehicleNo = carData.allocateVehicleNo || '';
                this.handPointId = carData.handPointId || this.handPointId;
                this.targetHandPointId = carData.handPointName || this.targetHandPointId;

                // 更新缓存
                const cacheData = {
                    carNumber: this.carNumber,
                    carTraceNo: this.carTraceNo,
                    allocateVehicleNo: this.allocateVehicleNo,
                    handPointId: this.handPointId,
                    targetHandPointId: this.targetHandPointId,
                };

                const success = this.setCacheData(cacheData);
                if (!success) {
                    throw new Error('缓存更新失败');
                }

                return true;
            } catch (error) {
                console.error('数据更新失败:', error);
                // 回滚操作
                Object.assign(this, backup);
                this.$toast('数据更新失败，已回滚');
                return false;
            }
        },

        /**
         * 清理所有车辆相关数据
         */
        clearCarData() {
            try {
                // 清理当前车辆的捆包缓存
                if (this.carNumber) {
                    this.clearVehicleBundleCache(this.carNumber);
                }

                // 清理组件状态
                this.carNumber = '';
                this.carTraceNo = '';
                this.allocateVehicleNo = '';
                this.driverTel = '';
                this.driverName = '';
                this.radioCarNumber = '';
                this.carNumberList = [];
                this.carInPoint = [];
                this.isPendCall = false;

                // 清理缓存
                const keysToRemove = ['carNumber', 'carTraceNo', 'allocateVehicleNo'];
                return this.clearCacheData(keysToRemove);
            } catch (error) {
                console.error('清理车辆数据失败:', error);
                return false;
            }
        },

        onClickLeft() {
            this.$router.replace('/index');
        },

        /**
         * 强制叫号勾选车辆
         * @param item 
         */
        selectMandatory(item) {
            this.radio = item.vehicleNo;
            this.carTraceNo = item.carTraceNo;
            this.carNumber = item.vehicleNo;
        },

        async onSelect(currentData, index) {
            if (index != 0) {
                Dialog.alert({
                    message: '只能选择第一个车辆',
                })
                return;
            }

            // 记录旧的车辆和装卸点信息，用于缓存清理
            const oldCarNumber = this.carNumber;
            const oldHandPointId = this.handPointId;

            // 使用统一的数据更新方法
            const success = this.updateCarInfo(currentData);
            if (!success) {
                return;
            }

            this.driverTel = currentData.driverTel || '';
            this.driverName = currentData.driverName || '';
            this.status = currentData.status || '';
            this.radioCarNumber = currentData.vehicleNo || '';
            this.handPonitRadio = currentData.handPointId || this.handPointId;

            // 调用0162接口获取司机手机号
            await this.getDriverInfo();

            // 如果是待叫号车辆, 查询该车辆需要作业的装卸点
            if (this.isPendCall) {
                await this.getPendCallCarPoint();
            }
        },

        /**
         * 调用0162接口获取司机信息
         */
        async getDriverInfo() {
            if (!this.carNumber || !this.carTraceNo) {
                return;
            }

            const params = {
                serviceId: 'S_LI_RL_0162',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };

            try {
                let res = await post(params);
                if (res && res.list && res.list.length > 0) {
                    const driverData = res.list[0];
                    // 从接口返回的数据中获取司机手机号
                    if (driverData.driverTel) {
                        this.driverTel = driverData.driverTel;
                    }
                    if (driverData.driverName) {
                        this.driverName = driverData.driverName;
                    }
                }
            } catch (error) {
                console.error('获取司机信息失败:', error);
            }
        },

        /**
         * 待叫号车辆, 查询该车辆需要作业的装卸点
         */
        async getPendCallCarPoint() {
            const params = {
                serviceId: 'S_LI_RL_0162',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            let res = await post(params);

            if (!res || !res.list || res.list.length == 0 || res.__sys__.status == -1) {
                return;
            }
            const resList = res.list;
            if (resList.length == 1) {
                const currentData = resList[0];

                // 记录旧的装卸点信息
                const oldHandPointId = this.handPointId;

                // 统一更新装卸点信息
                const pointData = {
                    handPointId: currentData.handPointId,
                    handPointName: currentData.handPointName,
                    allocateVehicleNo: currentData.allocateVehicleNo,
                };

                // 如果装卸点发生变化，清除旧装卸点的缓存
                if (oldHandPointId && oldHandPointId !== pointData.handPointId && this.carNumber) {
                    this.clearVehicleHandPointBundleCache(this.carNumber, oldHandPointId, pointData.handPointId);
                }

                this.handPonitRadio = pointData.handPointId;
                this.targetHandPointId = pointData.handPointName;
                this.handPointId = pointData.handPointId;
                this.allocateVehicleNo = pointData.allocateVehicleNo;

                // 更新缓存
                const cacheData = {
                    handPointId: pointData.handPointId,
                    allocateVehicleNo: pointData.allocateVehicleNo,
                    targetHandPointId: pointData.handPointName,
                };
                this.setCacheData(cacheData);

                // 从车辆排队一览过来的自动启动叫号
                if (this.statusType == 5) {
                    await this.startCallNumber();
                }
                return;
            }
            this.statusType = 3;
            this.isShowPonit = true;
            this.pointList = resList;
        },

        /**
         * 查询其他车辆
         */
        async getOtherCar() {
            this.carNumberList = [];
            const param = {
                serviceId: 'S_LI_RL_0055',
                handPointId: this.handPonitRadio,
                factoryArea: localStorage.getItem('factoryArea'),
            };
            const res = await post(param);
            if (!res) {
                return;
            }

            if (res.list.length == 0) {
                Dialog.alert({ message: '没有作业中车辆' });
                return;
            }
            this.isPendCall = false;
            this.carNumberList = res.list;
        },

        /**
         * 查询待叫号车辆
         */
        async getPendCall() {
            this.carNumberList = [];
            const param = {
                serviceId: 'S_LI_RL_0161',
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                handPointId: this.handPointId,
            };
            const res = await post(param);
            if (!res) {
                return;
            }

            if (res.list.length == 0) {
                Dialog.alert({ message: '没有待叫号车辆' });
                return;
            }

            this.isPendCall = true;
            this.carNumberList = res.list;
        },

        /**
         * 查询已叫号车辆
         */
        async getCall() {
            this.carNumberList = [];
            const param = {
                serviceId: 'S_LI_RL_0167',
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                handPointId: this.handPointId,
            };
            const res = await post(param);
            if (!res) {
                return;
            }

            if (res.list.length == 0) {
                Dialog.alert({ message: '没有已叫号车辆' });
                return;
            }

            this.carNumberList = res.list;
        },

        /**
         * 确定选择装卸点
         * @param action 
         * @param done 
         */
        async closeHandPonit(action, done) {
            if (action != 'confirm' && this.statusType === 1) {
                done();
                return;
            } else if (action != 'confirm' && this.statusType === 2) {
                this.statusType === 1;
                done();
                return;
            }

            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.handPonitRadio) {
                Dialog.alert({ message: '请选择装卸点' });
                done(false);
                return;
            }

            // 强制叫号的装卸点弹出框
            if (this.statusType == 4) {
                await this.confirmMandatoryCall(action, done, this.handPonitRadio);
                return;
            }

            if (this.statusType == 3) {
                const currentData = this.pointList.find(p => p.handPointId == this.handPonitRadio);
                if (!currentData) {
                    Dialog.alert({ message: '装卸点数据异常' });
                    done(false);
                    return;
                }

                // 记录旧的装卸点信息
                const oldHandPointId = this.handPointId;

                // 如果装卸点发生变化，清除旧装卸点的缓存
                if (oldHandPointId && oldHandPointId !== currentData.handPointId && this.carNumber) {
                    this.clearVehicleHandPointBundleCache(this.carNumber, oldHandPointId, currentData.handPointId);
                }

                // 统一更新装卸点信息
                this.targetHandPointId = currentData.handPointName;
                this.handPointId = currentData.handPointId;
                this.allocateVehicleNo = currentData.allocateVehicleNo;

                // 更新缓存
                const cacheData = {
                    handPointId: currentData.handPointId,
                    targetHandPointId: this.targetHandPointId,
                    allocateVehicleNo: this.allocateVehicleNo,
                };
                this.setCacheData(cacheData);

                if (this.isStartCall) {
                    await this.startCallNumber();
                }
                done(true);
                return;
            }

            if (this.statusType === 1) {
                const currentData = this.pointList.find(p => p.handPointId == this.handPonitRadio);
                if (!currentData) {
                    Dialog.alert({ message: '装卸点数据异常' });
                    done(false);
                    return;
                }

                // 记录旧的装卸点信息
                const oldHandPointId = this.handPointId;

                // 如果装卸点发生变化，清除旧装卸点的缓存
                if (oldHandPointId && oldHandPointId !== currentData.handPointId && this.carNumber) {
                    this.clearVehicleHandPointBundleCache(this.carNumber, oldHandPointId, currentData.handPointId);
                }

                // 统一更新装卸点信息
                this.targetHandPointId = currentData.handPointName;
                this.handPointId = currentData.handPointId;

                // 更新缓存
                const cacheData = {
                    handPointId: currentData.handPointId,
                    targetHandPointId: this.targetHandPointId,
                };
                this.setCacheData(cacheData);

                // 查询车牌号
                await this.openCarAction();
                done(true);
            } else {
                done(false);
                // 更改卸货点 再加个提示框确定是不是要更改
                Dialog.confirm({
                    title: '提示',
                    message: '确定更改卸货点'
                }).then(() => {
                    done(true);
                    this.changeCallNumbeCon();
                }).catch(() => {
                    done(true);
                    this.statusType = 1;
                    this.isShowPonit = false;
                });
            }
        },

        /**
        * 查询车牌号
        */
        async openCarAction() {
            const params = {
                serviceId: 'S_LI_RL_0093',
                handPointId: this.handPointId,
            };
            let result = await post(params);

            // 清理之前的车辆数据（这里会清理旧车辆的捆包缓存）
            this.clearCarData();

            if (!result.list || result.list.length == 0) {
                Dialog.alert({
                    message: '未查到待作业的车辆，可点击【作业中车辆】查看正在作业的车辆',
                });
                return;
            }

            this.carInPoint = result.list;
            this.carNumberList = result.list;

            // if (result.list.length == 1) {
            //     //选中单选按钮
            //     this.radioCarNumber = result.list[0].vehicleNo
            //     this.onSelect(result.list[0], 0);
            // } else {
            this.radioCarNumber = "";
            // }
            // return;
        },

        /**
         * 查询装卸点
         */
        async openHandPoint() {
            const params = {
                serviceId: 'S_LI_RL_0051',
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            const result = await post(params);
            if (!result.list || result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到装卸点信息',
                });
                return;
            }
            this.pointList = result.list;
            this.isShowPonit = true;
        },

        /**
        * 启动叫号
        */
        async startCallNumber() {
            if (!this.validateCacheConsistency()) {
                Dialog.alert({ message: '数据异常，请重新选择' });
                return;
            }

            const params = {
                serviceId: 'S_LI_RL_0110',
                segNo: localStorage.getItem('segNo'),
                handPointId: this.handPointId,
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                driverName: this.driverName,
                driverTel: this.driverTel,
                recRevisor: localStorage.getItem('userId'),
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                recRevisorName: localStorage.getItem('userName'),
                factoryArea: localStorage.getItem('factoryArea'),
                allocateVehicleNo: this.allocateVehicleNo
            };
            const result = await post(params);
            this.isStartCall = false;
            if (result.__sys__.status === 1) {
                this.$toast(result.__sys__.msg);
            }
        },

        //更改叫号装卸点
        async changeCallNumber() {
            if (!this.targetHandPointId) {
                this.$toast('请先选择原装卸点!');
                return;
            }
            if (!this.carNumber) {
                this.$toast('请选择车牌号');
                return;
            }

            // 更改叫号装卸点查询所有的厂区装卸点
            const params = {
                serviceId: 'S_LI_RL_0051',
                warehouseCode: localStorage.getItem('warehouseCode'),
            };
            const result = await post(params);
            if (!result.list || result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到装卸点信息',
                });
                return;
            }
            this.pointList = result.list;
            this.oldHandPointId = this.handPointId;
            this.statusType = 2;
            this.isShowPonit = true;
        },

        // 确定更改卸货点
        async changeCallNumbeCon() {
            const params = {
                serviceId: 'S_LI_RL_0079',
                segNo: localStorage.getItem('segNo'),
                oldHandPointId: this.handPointId,
                handPointId: this.handPonitRadio,
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                factoryArea: localStorage.getItem('factoryArea'),
                userId: localStorage.getItem('userId'),
                recRevisor: localStorage.getItem('userId'),
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                recRevisorName: localStorage.getItem('userName'),
            };
            const result = await post(params);
            if (result.__sys__.status === 1) {
                this.$toast(result.__sys__.msg);
                this.statusType = 1;
                this.isShowPonit = false;

                this.clearCarData();
                this.handPointId = '';
                this.targetHandPointId = '';
            }
        },

        // 出库确认
        outboundConfirmation() {
            this.$router.togo({
                name: 'select-car',
            });
        },

        /**
         * 开始装卸货
         * @param type 
         */
        async toUnload(type) {
            if (!this.carNumber) {
                this.$toast('请选择车牌号');
                return;
            }

            // 数据一致性检查
            if (!this.validateCacheConsistency()) {
                Dialog.alert({ message: '数据异常，请重新选择车辆' });
                return;
            }

            const loadName = type == 2 ? "卸" : '装';
            const query = {
                serviceId: 'S_LI_RL_0080',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                currentHandPointId: this.handPointId,
                status: this.status,
                factoryArea: localStorage.getItem('factoryArea'),
            };
            const resultData = await post(query);
            if (resultData.__sys__.status == -1) {
                return;
            }

            // 统一更新缓存
            const cacheData = {
                loadingType: loadName,
                currentHandPointId: resultData.currentHandPointId,
                loadId: resultData.loadId,
            };
            this.setCacheData(cacheData);

            this.$router.togo({
                name: loadName.includes('卸') ? "scan-store-cq" : 'in-storage',
                params: {
                    carNumber: this.carNumber,
                    carTraceNo: this.carTraceNo,
                    handPointId: this.handPointId,
                    currentHandPointId: resultData.currentHandPointId,
                    loadId: resultData.loadId,
                },
            });
        },

        /**
         * 打开叫号页面弹框
         */
        async openCall() {
            const params = {
                serviceId: 'S_LI_RL_0165',
            };
            const result = await post(params);

            if (!result || !result.list) {
                return;
            }
            if (result.list.length == 0) {
                this.$toast('无车牌号数据');
                return;
            }

            this.callCarList = result.list;
            if (this.callCarList.length == 1) {
                this.radio = 0;
            }
            this.isShowCall = true;
        },

        /**
         * 强制叫号
         */
        async mandatoryCall(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.radio || this.radio == 0) {
                Dialog.alert({ message: '请选择强制叫号车辆' });
                done(false);
                return;
            }

            const params = {
                serviceId: 'S_LI_RL_0162',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };

            let res = await post(params);
            if (!res || !res.list || res.__sys__.status == -1) {
                done(false);
                return;
            }

            if (res.list.length == 0) {
                Dialog.alert({ message: '当前厂区厂房下, 未查询到该车辆待去的装卸点信息, 请更换厂区厂房之后再查询' });
                done(false);
                return;
            }

            const resList = res.list;
            if (resList.length == 1) {
                const currentData = resList[0];
                await this.confirmMandatoryCall(action, done, currentData.handPointId);
                return;
            }

            // 如果有多个装卸点弹框选择
            this.statusType = 4;
            this.isShowPonit = true;
            this.pointList = resList;
            done(true);
        },

        /**
         * 确认强制叫号
         */
        async confirmMandatoryCall(action, done, handPointId) {
            const data = this.callCarList.find(c => c.vehicleNo == this.radio);
            if (!data) {
                Dialog.alert({ message: '车辆数据异常' });
                done(false);
                return;
            }

            const params = {
                serviceId: 'S_LI_RL_0166',
                vehicleNo: data.vehicleNo,
                carTraceNo: data.carTraceNo,
                handPointId,
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                done(false);
                return;
            }

            this.$toast(result.__sys__.msg);

            // 清理当前车辆数据
            this.clearCarData();
            done(true);
        },

        /**
         * 结束装卸货
         */
        async endLoad() {
            // 数据一致性检查
            if (!this.validateCacheConsistency()) {
                Dialog.alert({ message: '数据异常，请重新选择车辆' });
                return;
            }

            const params = {
                serviceId: "S_LI_RL_0010",
                recCreator: localStorage.getItem("userId"),
                recCreatorName: localStorage.getItem("userName"),
                carTraceNo: this.carTraceNo,
                vehicleNo: this.carNumber,
                currentHandPointId: this.handPointId,
            };

            const result = await post(params);
            if (!result || result.__sys__.status != 1) {
                return;
            }
            this.$router.togo({
                name: "end-load",
                params: {
                    vehicleNo: this.carNumber,
                    carTraceNo: this.carTraceNo,
                    handPointId: this.handPointId,
                },
            });
        },

        // 取消叫号
        async cancelCalling() {
            if (!this.carNumber || !this.carTraceNo) {
                this.$toast('请先选择车辆');
                return;
            }

            const params = {
                serviceId: "S_LI_RL_0078",
                segNo: localStorage.getItem('segNo'),
                handPointId: this.handPointId,
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                userId: localStorage.getItem("userId"),
                recRevisor: localStorage.getItem('userId'),
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                recRevisorName: localStorage.getItem('userName'),
            };

            const result = await post(params);
            if (!result || result.__sys__.status != 1) {
                return;
            }
            this.$toast(result.__sys__.msg);

            // 取消叫号后清理车辆数据
            this.clearCarData();
        },

        /**
         * 未装离厂查询车辆
         */
        async uninstallFactory() {
            const params = {
                serviceId: 'S_LI_RL_0105',
            };
            const result = await post(params);

            if (!result || !result.list) {
                return;
            }
            if (result.list.length == 0) {
                this.$toast('无车牌号数据');
                return;
            }

            this.uninstallList = result.list;
            if (this.uninstallList.length == 1) {
                this.uninstallRadio = this.uninstallList[0].vehicleNo;
            }

            this.isShowUninstall = true;
        },

        /**
         * 确认未装离厂
         */
        async confirmUninstall(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.uninstallRadio) {
                Dialog.alert({ message: '请选择未装离厂车辆' });
                done(false);
                return;
            }

            const data = this.uninstallList.find(c => c.vehicleNo == this.uninstallRadio);
            if (!data) {
                Dialog.alert({ message: '车辆数据异常' });
                done(false);
                return;
            }

            const params = {
                serviceId: 'S_LI_RL_0106',
                recCreator: localStorage.getItem('userId'),
                recCreatorName: localStorage.getItem('userName'),
                ...data,
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                done(false);
                return;
            }

            this.$toast('未装离厂成功');
            done(true);
        },


    },
};
</script>
<style>
.van-cell__value {
    color: #323233;
}

.foot-sticky-unload {
    margin-top: 1em;
    width: 100%;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload {
    width: calc(50% - 4px) !important;
    margin-bottom: 0.8em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
    font-size: 12px !important;
    font-weight: bold !important;
    flex: none !important;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload:nth-child(odd):last-child {
    margin-right: auto !important;
}

.foot-sticky-unload .button-container {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    gap: 8px !important;
    padding: 0 2% !important;
}

.wrap-text {
    white-space: normal;
    word-wrap: break-word;
    width: 100%;
    /* 如果需要可以添加以下属性 */
    overflow-wrap: break-word;
}


</style>