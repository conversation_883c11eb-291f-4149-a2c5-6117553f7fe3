<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">点检计划</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>
        <div class="in-content">
            <van-field name="radio" required label="点检性质" size="large">
                <template #input>
                    <van-radio-group v-model="checkPlan.spotCheckNature" direction="horizontal">
                        <van-radio name="10">操作点检</van-radio>
                        <van-radio name="20">专业点检</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field label="设备名称" required readonly v-model="checkPlan.equipmentName" size="large"
                @click="serachEquipment" />
            <van-field label="分部设备代码" required size="large" @keyup.enter.native="serachPlan">
                <template #input>
                    <input type="search" class="new-field" placeholder="请输入或扫描分部设备" ref="baleref"
                        v-model="checkPlan.deviceCode" />
                </template>
                <template #right-icon>
                    <van-icon name="search" size="30" @click="serachPlan" />
                </template>
            </van-field>
            <van-field label="分部设备名称" readonly v-model="checkPlan.deviceName" size="large" />
            <van-field label="点检计划号" readonly v-model="checkPlan.checkPlanId" size="large" />
            <van-field label="点检计划子项号" readonly v-model="checkPlan.checkPlanSubId" size="large" />
            <van-field label="点检内容" type="textarea" readonly v-model="checkPlan.spotCheckContent" autosize
                size="large" />
            <van-field label="判断标准" type="textarea" readonly v-model="checkPlan.judgmentStandard" autosize
                size="large" />
            <van-field required name="radio2" label="是否异常" size="large">
                <template #input>
                    <van-radio-group v-model="checkPlan.isNormal" direction="horizontal">
                        <van-radio name="0">正常</van-radio>
                        <van-radio name="1">异常</van-radio>
                    </van-radio-group>
                </template>
            </van-field>
            <van-field size="large" required v-model="checkPlan.actualsRemark" autosize label="点检实绩" type="textarea"
                placeholder="请输入点检实绩" />
            <van-field size="large" name="uploader" label="图片上传" :required="checkPlan.isPicture == '1'">
                <template #input>
                    <van-uploader v-model="uploaderList" :upload-icon="uploadIcon" @oversize="onOversize" :max-count="9"
                        :before-read="beforeImageRead" accept="image/*" :max-size="10 * 1024 * 1024" />
                </template>
            </van-field>
        </div>
        <div class="mui-input-row" style="margin: 0" @click="updateActual">
            <button type="button" class="mui-btn">
                确&nbsp; &nbsp; &nbsp;&nbsp;定
            </button>
        </div>
        <van-dialog v-model="isShowList" title="选择点检计划" show-cancel-button :beforeClose="closeList">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in planList" :key="index" :title="item.checkPlanSubId"
                            :label="item.spotCheckContent" @click="selectedId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
        <!-- 选择设备 -->
        <van-dialog v-model="isShowEquipment" title="选择设备" show-cancel-button :beforeClose="closeEquipment">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedEquipmentId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in equipmentList" :key="index"
                            :title="item.eArchivesNo" :label="item.equipmentName"
                            @click="selectedEquipmentId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
        <!-- 选择分部设备 -->
        <van-dialog v-model="isShowDevice" title="选择分部设备" show-cancel-button :beforeClose="closeDevice">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedDeviceId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in deviceList" :key="index" :title="item.deviceCode"
                            :label="item.deviceName" @click="selectedDeviceId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>
<script>
import { post } from '../../api/base-service';
import uploadIcon from '@/assets/imgs/upload-icon.png';
import { Dialog } from 'vant';
export default {
    data() {
        return {
            uploadIcon: uploadIcon,
            uploaderList: [],
            checkPlan: {},
            oldSpotCheckNature: '',
            isShowList: false,
            selectedId: '',
            planList: [],
            isShowEquipment: false,
            equipmentList: [],
            selectedEquipmentId: '',
            isShowDevice: false,
            deviceList: [],
            selectedDeviceId: ''
        }
    },
    methods: {
        //校验上传图片大小
        onOversize(file) {
            this.$toast.fail("文件大小不能超过10MB，请重新选择！");
        },
        onClickLeft() {
            this.$router.replace('/index');
        },
        // 查找计划
        serachPlan() {
            if (!this.checkPlan.spotCheckNature || this.checkPlan.spotCheckNature === '') {
                Dialog.alert({ message: '请选择点检性质' });
                return;
            }
            if (!this.checkPlan.deviceCode || this.checkPlan.deviceCode === '') {
                Dialog.alert({ message: '请扫描或输入分部设备代码' });
                return;
            }
            this.getPlanList();
        },
        serachEquipment() {
            if (!this.checkPlan.spotCheckNature || this.checkPlan.spotCheckNature === '') {
                Dialog.alert({ message: '请选择点检性质' });
                return;
            }
            this.getEquipmentList();
        },
        // 获取点检计划列表
        async getPlanList() {
            const params = {
                serviceId: 'S_VG_DM_0001',
                deviceCode: this.checkPlan.deviceCode,
                spotCheckNature: this.checkPlan.spotCheckNature
            };

            const result = await post(params);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到点检计划',
                });
                return;
            }
            this.planList = result.list;
            this.isShowList = true;
        },
        // 获取设备列表
        async getEquipmentList() {
            const params = {
                serviceId: 'S_VG_DM_0009',
                factoryArea: localStorage.getItem("factoryAreaTrue"),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                spotCheckNature: this.checkPlan.spotCheckNature
            };
            // console.log(params);
            const result = await post(params);
            // console.log(result);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到需点检的设备信息',
                });
                return;
            }
            this.equipmentList = result.list;
            this.isShowEquipment = true;
        },
        // 获取分部设备列表
        async getDeviceList(autoFlag = false) {
            const params = {
                serviceId: 'S_VG_DM_0010',
                eArchivesNo: this.checkPlan.eArchivesNo,
                spotCheckNature: this.checkPlan.spotCheckNature
            };
            this.deviceList = [];
            const result = await post(params);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                if (!autoFlag) {
                    Dialog.alert({
                        message: '未查询到需点检分部设备信息',
                    });
                } else {
                    Dialog.alert({
                        message: this.checkPlan.equipmentName + '点检完成',
                    });
                    this.checkPlan.deviceCode = '';
                    this.clearData();
                }
                this.deviceList = [];
                return;
            }
            this.deviceList = result.list;
        },
        // 关闭选择点检计划弹窗
        closeList(action, done) {
            // console.log(this.selectedId);
            if (action === 'confirm') {
                if (!this.selectedId) {
                    Dialog.alert({
                        message: '请选择点检计划',
                    });
                    done(false);
                    return;
                }
                const currentData = this.planList.find(p => p.uuid == this.selectedId);
                this.clearData();
                this.checkPlan.eArchivesNo = currentData.eArchivesNo;
                this.checkPlan.equipmentName = currentData.equipmentName;
                this.checkPlan.deviceCode = currentData.deviceCode;
                this.checkPlan.deviceName = currentData.deviceName;
                this.checkPlan.uuid = currentData.uuid;
                this.checkPlan.checkPlanId = currentData.checkPlanId;
                this.checkPlan.checkPlanSubId = currentData.checkPlanSubId;
                this.checkPlan.spotCheckContent = currentData.spotCheckContent;
                this.checkPlan.judgmentStandard = currentData.judgmentStandard;
                this.checkPlan.isPicture = currentData.isPicture;
                this.oldSpotCheckNature = this.checkPlan.spotCheckNature;
            }
            this.isShowList = false;
            done();
        },
        // 关闭选择设备弹窗
        closeEquipment(action, done) {
            if (action === 'confirm') {
                if (!this.selectedEquipmentId) {
                    Dialog.alert({
                        message: '请选择设备',
                    });
                    done(false);
                    return;
                }
                const currentData = this.equipmentList.find(p => p.uuid == this.selectedEquipmentId);
                this.checkPlan.eArchivesNo = currentData.eArchivesNo;
                this.checkPlan.equipmentName = currentData.equipmentName;
                this.getDeviceList();
                this.isShowDevice = true;
            }
            this.isShowEquipment = false;
            done();
        },
        // 关闭选择分部设备弹窗
        closeDevice(action, done) {
            if (action === 'confirm') {
                if (!this.selectedDeviceId) {
                    Dialog.alert({
                        message: '请选择分部设备',
                    });
                    done(false);
                    return;
                }
                const currentData = this.deviceList.find(p => p.uuid == this.selectedDeviceId);
                this.checkPlan.deviceCode = currentData.deviceCode;
                this.checkPlan.deviceName = currentData.deviceName;
                this.serachPlan();
            }
            this.isShowDevice = false;
            done();
        },
        // 校验数据
        checkData() {
            if (!this.checkPlan.uuid) {
                Dialog.alert({
                    message: '请选择点检计划',
                });
                return false;
            }
            if (!this.checkPlan.isNormal) {
                Dialog.alert({
                    message: '请选择是否异常',
                });
                return false;
            }
            if (!this.checkPlan.actualsRemark) {
                Dialog.alert({
                    message: '请输入点检实绩',
                });
                return false;
            }
            if (this.checkPlan.isPicture == '1' && !this.uploaderList.length) {
                Dialog.alert({
                    message: '请上传图片',
                });
                return false;
            }
            return true;
        },
        // 更新点检实绩
        async updateActual() {
            // console.log(this.checkPlan);
            if (!this.checkData()) {
                return;
            }
            const params = {
                serviceId: 'S_VG_DM_0002',
                uuid: this.checkPlan.uuid,
                isNormal: this.checkPlan.isNormal,
                actualsRemark: this.checkPlan.actualsRemark,
                userName: localStorage.getItem("userName"),
                fileList: this.uploaderList.map(item => item.content)
            };
            // console.log(params);
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                this.$toast(result.__sys__.msg);
                return;
            }
            if (this.planList.length > 1) {
                Dialog.confirm({
                    message: '上传成功，是否继续点检此分部设备?',
                }).then(() => {
                    this.clearData();
                    this.selectedId = '';
                    this.getPlanList();
                }).catch(() => {
                    this.clearData();
                });
            } else {
                // 设备信息查询
                Dialog.confirm({
                    message: '上传成功，是否继续点检此设备?',
                }).then(async () => {
                    const temEArchivesNo = this.checkPlan.eArchivesNo;
                    const temEquipmentName = this.checkPlan.equipmentName;
                    // console.log('设备1：'+temEArchivesNo + temEquipmentName);
                    this.clearData();
                    this.checkPlan.eArchivesNo = temEArchivesNo;
                    this.checkPlan.equipmentName = temEquipmentName;
                    // console.log('设备2：'+this.checkPlan.eArchivesNo + this.checkPlan.equipmentName);
                    this.selectedId = '';
                    // 分部设备
                    await this.getDeviceList(true);
                    if (this.deviceList.length > 0) {
                        this.isShowDevice = true;
                    } else {
                        this.clearData();
                        this.$toast(temEquipmentName + '点检完成');
                    }
                }).catch(() => {
                    this.clearData();
                });
            }
        },
        // 清除数据
        clearData() {
            this.checkPlan.uuid = '';
            this.checkPlan.eArchivesNo = '';
            this.checkPlan.equipmentName = '';
            this.checkPlan.deviceName = '';
            this.checkPlan.checkPlanId = '';
            this.checkPlan.checkPlanSubId = '';
            this.checkPlan.spotCheckContent = '';
            this.checkPlan.judgmentStandard = '';
            this.checkPlan.isNormal = '';
            this.checkPlan.actualsRemark = '';
            this.checkPlan.isPicture = '';
            this.uploaderList = [];
        },
        // 检查并请求摄像头权限
        async checkAndRequestPermission() {
            try {
                // 检查是否支持permissions API
                if (navigator.permissions && navigator.permissions.query) {
                    const result = await navigator.permissions.query({ name: 'camera' });
                    if (result.state === 'denied' || result.state === 'prompt') {
                        await this.requestCameraAccess();
                    }
                    // 监听权限变化
                    result.addEventListener('change', () => {
                        if (result.state === 'denied') {
                            Dialog.alert({
                                message: '摄像头权限已被禁用，部分功能可能无法使用。',
                            });
                        }
                    });
                } else {
                    // 如果不支持permissions API,直接尝试请求摄像头
                    await this.requestCameraAccess();
                }
            } catch (err) {
                Dialog.alert({
                    message: '权限检查失败:' + err,
                });
                await this.requestCameraAccess();
            }
        },
        // 新增方法处理摄像头访问
        async requestCameraAccess() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                // 获得权限后立即停止摄像头
                stream.getTracks().forEach(track => track.stop());
            } catch (err) {
                Dialog.alert({
                    message: '请授权摄像头权限以使用此功能。' + err,
                });
            }
        },

        async beforeImageRead(file) {
            return this.compressImage(file);
        },

        /**
         * 压缩图片
         * @param {File} file 原始图片文件
         * @param {number} quality 压缩质量 0-1
         * @param {number} maxWidth 最大宽度
         * @param {number} maxHeight 最大高度
         * @returns {Promise<File>} 压缩后的文件
         */
        compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    // 计算压缩后的尺寸
                    let { width, height } = img;

                    if (width > maxWidth || height > maxHeight) {
                        const ratio = Math.min(maxWidth / width, maxHeight / height);
                        width *= ratio;
                        height *= ratio;
                    }

                    // 设置画布尺寸
                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片到画布
                    ctx.drawImage(img, 0, 0, width, height);

                    // 将画布内容转换为Blob
                    canvas.toBlob((blob) => {
                        // 创建新的File对象
                        const compressedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now()
                        });

                        // console.log(`图片压缩完成: ${(file.size / 1024 / 1024).toFixed(2)}MB -> ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
                        resolve(compressedFile);
                    }, file.type, quality);
                };

                img.onerror = () => {
                    console.error('图片加载失败，使用原始文件');
                    resolve(file);
                };

                // 加载图片
                img.src = URL.createObjectURL(file);
            });
        },

    },
    mounted() {
        this.checkPlan = {
            deviceCode: "",
            checkPlanId: "",
            checkPlanSubId: "",
            deviceName: "",
            spotCheckContent: "",
            judgmentStandard: "",
            isNormal: "",
            uuid: "",
            spotCheckNature: "",
            isPicture: "",
            actualsRemark: ""
        };
        // 检查并请求权限
        // this.checkAndRequestPermission();
    },
    watch: {
        'checkPlan.isNormal': {
            handler(newVal) {
                if (newVal === '0') {
                    this.checkPlan.actualsRemark = this.checkPlan.judgmentStandard;
                } else if (newVal === '1') {
                    this.checkPlan.actualsRemark = '';
                }
            }
        },
        'checkPlan.spotCheckNature': {
            handler(newVal, oldVal) {
                // 首次赋值,只记录不弹窗
                if (!this.oldSpotCheckNature) {
                    this.oldSpotCheckNature = newVal;
                    return;
                }
                // 只在与记录的旧值不同时才弹窗
                if (newVal !== this.oldSpotCheckNature && this.checkPlan.uuid) {
                    Dialog.confirm({
                        title: '提示',
                        message: '修改点检性质会清空现有数据，是否继续?',
                    }).then(() => {
                        this.clearData();
                        this.oldSpotCheckNature = newVal;
                    }).catch(() => {
                        this.checkPlan.spotCheckNature = this.oldSpotCheckNature;
                    });
                }
            }
        }
    }
}
</script>
<style></style>
