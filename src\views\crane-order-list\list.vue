<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">行车作业清单</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <div style="height: 90vh;overflow-y: auto;">
                <div v-if="list && list.length > 0">

                    <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="fetchData">
                        <van-cell-group inset v-for="(item, index) in list" :key="index" style="margin-bottom: 16px;">
                            <van-cell title="行车" :value="item.craneName" />
                            <van-cell title="抓取区域" :value="item.startAreaName" />
                            <van-cell title="释放区域" :value="item.endAreaName" />
                            <van-cell title="捆包号" :value="item.packId" />
                            <van-cell title="重量" :value="item.weight" />
                            <van-cell title="创建时间" :value="item.recCreateTime" />
                            <van-cell title="行车作业清单号" :value="item.craneOrderId" />
                        </van-cell-group>
                    </van-list>
                </div>
                <div v-else>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';

export default {
    data() {
        return {
            list: [],
            offset: 0,
            loading: false,
            finished: false,
            queryCondition: {},
        };
    },
    created() {
        this.queryCondition = this.$route.params.queryCondition;
    },

    methods: {

        onClickLeft() {
            this.$router.goBack();
        },

        // 获取行车作业清单
        async fetchData() {

            const params = {
                serviceId: "S_LI_DS_0010",
                craneId: this.queryCondition.craneId,
                listSource: this.queryCondition.listSource,
                recCreateTimeStart: this.queryCondition.recCreateTimeStart,
                recCreateTimeEnd: this.queryCondition.recCreateTimeEnd,
                machineCode: this.queryCondition.machineCode,
                packId: this.queryCondition.packId,
                voucherNum: this.queryCondition.voucherNum,
                offset: this.offset,
                limit: 10
            };

            if (this.offset == 0) {
                this.list = [];
            }

            const result = await post(params);
            this.loading = false;
            if (!result || !result.craneOrderList || result.craneOrderList.length == 0) {
                this.finished = true;
                return;
            }

            this.list = [
                ...this.list,
                ...result.craneOrderList
            ];
            this.offset += 10;
            this.finished = this.list.length >= result.pageQ.total;
            
        },
    },
    mounted() {
        this.fetchData();
    }
};
</script>

<style scoped>
.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>