/**
 * 引入fetch
 * @param params
 * @returns {*}
 */
import { fetch, fetchIMC } from '../apiconfig/service';
// 全部
export async function post(params) {
  params = {
    segNo: localStorage.getItem('segNo'),
    accessToken: localStorage.getItem("accessToken"),
    userId: localStorage.getItem("userId"),
    userName: localStorage.getItem("userName"),
    ...params,
  };
  const result = await fetch(params);
  if (!result || !result.data) {
    return;
  }
  return result.data;
}

// 调用imc接口
export async function postIMC(params) {
  params = {
    segNo: localStorage.getItem('segNo'),
    // accessToken: localStorage.getItem("accessToken"),
    userId: localStorage.getItem("userId"),
    userName: localStorage.getItem("userName"),
    ...params,
  };
  const result = await fetchIMC(params);
  if (!result || !result.data) {
    return;
  }
  return result.data;
}