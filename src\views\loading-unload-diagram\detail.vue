<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">车辆装卸示意图</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div style="margin-top: 12px;">
      <van-cell-group inset>
        <van-cell :value="list.vehicleNo">
          <template #title>
            <span class="custom-title">车牌号</span>
            <van-tag type="primary">{{ list.allocTypeName }}</van-tag>
          </template>
        </van-cell>
        <van-cell title="捆包统计">
          <template #right-icon>
            <van-badge :content="numLenght" style="margin-right: 12px;">
              <van-tag plain type="primary" size="large">{{ listTitle }}</van-tag>
            </van-badge>
            <van-badge :content="itemLenght">
              <van-tag plain type="primary" size="large">{{ listSum }}</van-tag>
            </van-badge>
          </template>
        </van-cell>
      </van-cell-group>

      <van-tabs v-model="active" style="margin-top: 12px;" color="#007aff" title-active-color="#007aff">
        <van-tab :title="activeTitle0">
          <van-cell-group inset style="margin-top: 8px;">
            <van-cell title="捆包号" v-for="(item, index) in loadingList" :key="index" :value="item.packId" />
          </van-cell-group>

        </van-tab>
        <van-tab :title="activeTitle1">
          <van-cell-group inset style="margin-top: 8px;">
            <van-cell title="捆包号" v-for="(item, index) in loadedList" :key="index" :value="item.packId" />
          </van-cell-group>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
      list: {},
      numLenght: 0,
      itemLenght: 0,
      loadedList: [],
      loadingList: [],
      listTitle: '',
      listSum: '',
      activeTitle0: '',
      activeTitle1: '',
      active: 0,
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
  },
  mounted() {
    this.list = this.$route.params.list;
    this.numLenght = this.list.list.length;
    console.log(this.list);
    if (this.list.allocTypeName == '装货') {
      this.listTitle = '总装货捆包';
      this.listSum = '已装货量';
      this.activeTitle0 = '已装货';
      this.activeTitle1 = '未装货';
    } else {
      this.listTitle = '总卸货捆包';
      this.listSum = '已卸货量';
      this.activeTitle0 = '已卸货';
      this.activeTitle1 = '未卸货';
    }

    this.loadedList = this.list.list.filter((item) => {
      return item.allocType == "10";
    });
    this.loadingList = this.list.list.filter((item) => {
      return item.allocType == "20";
    });
    if (this.list.allocTypeName == "装货") {
      const data = this.list.list.filter((item) => {
        return item.allocType == "20";
      });
      this.itemLenght = data.length;
    } else if (this.list.allocTypeName == "卸货") {
      const data = this.list.list.filter((item) => {
        return item.allocType == "20";
      });
      this.itemLenght = data.length;
    }
  },
  watch: {},
};
</script>
<style scoped>
.child {
  width: 40px;
  height: 40px;
  background: #f2f3f5;
  border-radius: 4px;
}

.van-cell__value {
  color: #323233;
}
</style>