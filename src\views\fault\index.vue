<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">设备故障</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zuoji<PERSON>ou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>
        <div class="in-content" style="height: 80vh;overflow-y: auto;">
            <van-field label="设备名称" required readonly placeholder="请选择设备" v-model="fault.equipmentName" size="large"
                @click="serachEquipment" />
            <van-field label="分部设备名称" required readonly v-model="fault.deviceName" size="large" />
            <van-field label="故障描述" required type="textarea" placeholder="请输入故障描述" v-model="fault.faultDesc"
                autosize size="large" />
        </div>
        <div class="mui-input-row" style="margin: 0" @click="insertFault">
            <button type="button" class="mui-btn">
                确&nbsp; &nbsp; &nbsp;&nbsp;定
            </button>
        </div>
        <!-- 选择设备 -->
        <van-dialog v-model="isShowEquipment" title="选择设备" show-cancel-button :beforeClose="closeEquipment">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedEquipmentId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in equipmentList" :key="index"
                            :title="item.eArchivesNo" :label="item.equipmentName"
                            @click="selectedEquipmentId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
        <!-- 选择分部设备 -->
        <van-dialog v-model="isShowDevice" title="选择分部设备" show-cancel-button :beforeClose="closeDevice">
            <div style="max-height: 320px; overflow-y: auto;">
                <van-radio-group v-model="selectedDeviceId">
                    <van-cell-group>
                        <van-cell clickable v-for="(item, index) in deviceList" :key="index" :title="item.deviceCode"
                            :label="item.deviceName" @click="selectedDeviceId = item.uuid">
                            <template #right-icon>
                                <van-radio :name="item.uuid" />
                            </template>
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
        </van-dialog>
    </div>
</template>
<script>
import { post } from '../../api/base-service';
import uploadIcon from '@/assets/imgs/upload-icon.png';
import { Dialog } from 'vant';
export default {
    data() {
        return {
            fault: {},
            isShowEquipment: false,
            equipmentList: [],
            selectedEquipmentId: '',
            isShowDevice: false,
            deviceList: [],
            selectedDeviceId: ''
        }
    },
    methods: {
        onClickLeft() {
            this.$router.replace('/index');
        },
        serachEquipment() {
            this.getEquipmentList();
        },
        // 获取设备列表
        async getEquipmentList() {
            const params = {
                serviceId: 'S_VG_DM_0016'
            };
            // console.log(params);
            const result = await post(params);
            // console.log(result);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到需检修的设备信息',
                });
                return;
            }
            this.equipmentList = result.list;
            this.isShowEquipment = true;
        },
        // 获取分部设备列表
        async getDeviceList() {
            const params = {
                serviceId: 'S_VG_DM_0017',
                eArchivesNo: this.fault.eArchivesNo
            };
            const result = await post(params);
            if (!result.list) {
                Dialog.alert({
                    message: result.__sys__.msg,
                });
                return;
            }
            if (result.list.length == 0) {
                Dialog.alert({
                    message: '未查询到分部设备信息',
                });
                return;
            }
            this.deviceList = result.list;
        },
        // 关闭选择设备弹窗
        closeEquipment(action, done) {
            if (action === 'confirm') {
                if (!this.selectedEquipmentId) {
                    Dialog.alert({
                        message: '请选择设备',
                    });
                    done(false);
                    return;
                }
                const currentData = this.equipmentList.find(p => p.uuid == this.selectedEquipmentId);
                this.fault.eArchivesNo = currentData.eArchivesNo;
                this.fault.equipmentName = currentData.equipmentName;
                this.getDeviceList();
                this.isShowDevice = true;
            }
            this.isShowEquipment = false;
            done();
        },
        // 关闭选择分部设备弹窗
        closeDevice(action, done) {
            if (action === 'confirm') {
                if (!this.selectedDeviceId) {
                    Dialog.alert({
                        message: '请选择分部设备',
                    });
                    done(false);
                    return;
                }
                const currentData = this.deviceList.find(p => p.uuid == this.selectedDeviceId);
                this.fault.deviceCode = currentData.deviceCode;
                this.fault.deviceName = currentData.deviceName;
            }
            this.isShowDevice = false;
            done();
        },
        // 校验数据
        checkData() {
            // console.log(this.fault);
            if (!this.fault.eArchivesNo) {
                Dialog.alert({
                    message: '请选择设备',
                });
                return false;
            }
            if (!this.fault.deviceCode) {
                Dialog.alert({
                    message: '请选择分部设备',
                });
                return false;
            }
            if (!this.fault.faultDesc) {
                Dialog.alert({
                    message: '请输入故障描述',
                });
                return false;
            }
            return true;
        },
        // 新增故障信息
        async insertFault() {
            // console.log(this.fault);
            if (!this.checkData()) {
                return;
            }
            const params = {
                serviceId: 'S_VG_DM_0018',
                userName: localStorage.getItem("userName"),
                ...this.fault
            };
            // console.log(params);
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                this.$toast(result.__sys__.msg);
                return;
            }
            this.clearData();
            this.$toast('新增成功');
        },
        // 清除数据
        clearData() {
            this.fault.uuid = '';
            this.fault.eArchivesNo = '';
            this.fault.equipmentName = '';
            this.fault.deviceCode = '';
            this.fault.deviceName = '';
            this.fault.faultDesc = '';
        }
    },
    mounted() {
        this.fault = {
            eArchivesNo: "",
            equipmentName: "",
            deviceCode: "",
            deviceName: "",
            faultDesc: "",
            uuid: ""
        };
    }
}
</script>
<style></style>
