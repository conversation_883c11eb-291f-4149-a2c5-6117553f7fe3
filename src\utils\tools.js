

/*
 * 判断是否字符串
 */
export const isString = (obj) => {
  return type(obj) === 'string';
};

/*
 * 判断是否boolean类型
 */
export const isBoolean = (obj) => {
  return type(obj) === 'boolean';
};

/*
 * 判断是否function类型
 */
export const isFunction = (obj) => {
  return type(obj) === 'function';
};

/*
 * 判断是否Promise类型
 */
export const isPromise = (obj) => {
  return !!obj && isFunction(obj.then);
};

/*
 * 判断是否date类型
 */
export const isDate = (obj) => {
  return type(obj) === 'date';
};

/*
 * 判断是否正则
 */
export const isRegExp = (obj) => {
  return type(obj) === 'regexp';
};

/*
 * 判断是否array类型
 */
export const isArray = (obj) => {
  return Array.isArray(obj);
};

/*
 * 判断是否是window
 */
export const isWindow = (obj) => {
  return obj != null && typeof (window) !== 'undefined' && obj === window;
};

/*
 * 判断是否object类型
 */
export const isPlainObject = (obj) => {
  if (!obj || type(obj) !== 'object' || obj.nodeType || isWindow(obj)) {
    return false;
  };
  let hasOwn = Object.prototype.hasOwnProperty;
  try {
    if (obj.constructor && !hasOwn.call(obj, 'constructor') && !hasOwn.call(obj.constructor.prototype,
      'isPrototypeOf')) {
      return false;
    };
  } catch (e) {
    return false;
  };
  for (var key in obj) { };
  return key === undefined || hasOwn.call(obj, key);
};

/*
 * 是否是空对象
 */
export const isEmptyObject = (obj) => {
  return isPlainObject(obj) && Object.keys(obj).length == 0;
};

/*
 * 判断是否是空
 */
export const isEmpty = (str) => {
  return str == 'null' || str == null || str == undefined || str == 'undefined' || str == '' || JSON.stringify(str) ==
    '{}';
};
/*
 * 比较两个值大小
 */
export const compare = (version1, version2) => { // 新版本，旧版本
  let v1 = version1.split('.');
  let v2 = version2.split('.');
  v1 = v1.map(item => Number.parseInt(item));
  v2 = v2.map(item => Number.parseInt(item));
  let len = Math.max(v1.length, v2.length);
  for (let i = 0; i < len; i++) {
    v1[i] || v1.push(0);
    v2[i] || v2.push(0);
    if (v1[i] > v2[i]) {
      return 1;
    } else if (v1[i] < v2[i]) {
      return -1;
    }
  }
  return 0;
};
