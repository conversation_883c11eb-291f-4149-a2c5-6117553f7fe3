<template>
    <div>
        <ErrorHandler ref="errorHandler" />
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">上料区信息补录</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
                <!-- <template #right>
                    <span class="iconfont icon-sousuo" @click="onClickRight"></span>
                </template> -->
            </van-nav-bar>
        </div>

        <div class="in-content" style="height: 70%;overflow-y: auto;padding-bottom: 0;">
            <div>
                <van-field required label="捆包号" v-model="packId" placeholder="请输入捆包号，按回车查询"
                    @keyup.enter.native="searchByPackId" ref="packRef">
                    <template #button>
                        <QRScanner @scan-success="onScanSuccess" />
                    </template>
                </van-field>
                <van-field label="机组" readonly v-model="machineName" placeholder="自动查询显示" />
                <van-field label="工单" readonly v-model="processOrderId" placeholder="自动查询显示" />
            </div>
            <div v-if="showData">
                <div class="title">机组当前作业信息</div>
                <div v-if="dataList && dataList.length > 0">
                    <van-list finished-text="没有更多了">
                        <van-cell-group inset v-for="(item, index) in dataList" :key="index"
                            style="margin-bottom: 16px;">
                            <van-cell title="工单" :value="item.processOrderId" />
                            <van-cell title="捆包" :value="item.packId" />
                            <van-cell title="状态" :value="dealPackStatus(item)" />
                            <van-cell title="开始时间" :value="item.recCreateTime" />
                            <van-button type="danger" size="small" @click="forceEnd(item)">手工结束</van-button>
                        </van-cell-group>
                    </van-list>
                </div>
                <div v-else>
                    <div class="title2">暂无数据</div>
                </div>
            </div>
        </div>

        <div class="foot-sticky-unload">
            <div class="button-container">
                <van-button type="info" size="large" class="foot-sticky-btn-unload" @click="upPack(false)">
                    确定(上料前)
                </van-button>
                <van-button type="warning" size="large" class="foot-sticky-btn-unload" @click="upPack(true)">
                    确定(已上料)
                </van-button>
                <van-button type="danger" size="large" class="foot-sticky-btn-unload" @click="backPack">
                    退&nbsp; &nbsp; &nbsp;&nbsp;料
                </van-button>
            </div>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';
import { Dialog } from 'vant';
import QRScanner from '../../components/QRScanner.vue';
import ErrorHandler from '../../components/ErrorHandler.vue';

export default {
    components: {
        QRScanner,
        ErrorHandler
    },
    data() {
        return {
            machineCode: '',
            machineName: '',
            packId: '',
            processOrderId: '',
            dataList: [],
            showData: false,
        };
    },
    created() {
        this.$nextTick((x) => {
            this.$refs.packRef.focus();
        });
    },

    methods: {
        onClickLeft() {
            this.$router.replace('/index');
        },

        // 扫码成功处理
        onScanSuccess(data) {
            this.packId = data.text;
            // 自动触发查询
            this.searchByPackId();
        },
        // 根据捆包号查询机组和工单
        searchByPackId: function () {
            var self = this;
            try {
                if (!this.packId) {
                    Dialog.alert({ message: '请输入捆包号' });
                    return;
                }
                var queryMap = {
                    serviceId: 'S_LI_DS_0027', // 假设这是根据捆包号查询的服务ID，需要根据实际情况调整
                    packId: this.packId,
                    factoryArea: localStorage.getItem("factoryAreaTrue"),
                    factoryBuilding: localStorage.getItem('factoryBuilding'),
                };

                post(queryMap).then(function (res) {
                    if (!res || !res.__sys__ || !res.__sys__.status || res.__sys__.status == -1) {
                        self.$toast(res.__sys__.msg || '查询失败');
                        return;
                    }
                    self.showData = true;
                    var result = res.result;
                    if (result.machineCode && result.machineName) {
                        self.machineCode = result.machineCode;
                        self.machineName = result.machineName;
                    }
                    if (result.processOrderId) {
                        self.processOrderId = result.processOrderId;
                    }
                    if (!result.machineCode && !result.processOrderId) {
                        Dialog.alert({ message: '未找到捆包[' + self.packId + ']对应的机组和工单信息' });
                    } else {
                        self.fetchData();
                    }
                }).catch(function (error) {
                    console.error('查询失败:', error);
                    self.$toast('网络请求失败，请重试');
                    if (self.$refs.errorHandler) {
                        self.$refs.errorHandler.handleError(error);
                    }
                });
            } catch (error) {
                console.error('searchByPackId方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
        fetchData() {
            var self = this;
            try {
                if (!this.machineCode) {
                    Dialog.alert({ message: '机组不能为空' });
                    return;
                }
                var queryMap = {
                    serviceId: 'S_VG_DM_0023',
                    machineCode: this.machineCode,
                };
                post(queryMap).then(function (res) {
                    if (!res || !res.__sys__ || !res.__sys__.status || res.__sys__.status == -1) {
                        self.$toast(res.__sys__.msg || '查询失败');
                        return;
                    }
                    var result = res.list;
                    console.log(result);
                    self.dataList = result;
                }).catch(function (error) {
                    console.error('查询失败:', error);
                    self.$toast('网络请求失败，请重试');
                    if (self.$refs.errorHandler) {
                        self.$refs.errorHandler.handleError(error);
                    }
                });
            } catch (error) {
                console.error('fetchData方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
        // 处理状态
        dealPackStatus: function (item) {
            let startStr = '';
            if (item.packStatus == '0') {
                startStr = '上料';
            } else if (item.packStatus == '1') {
                startStr = '加工中';
            }
            let endStr = '';
            if (item.endType == '1') {
                endStr = '-退料';
            } else if (item.endType == '2') {
                endStr = '-余料';
            } else if (item.endType == '3') {
                endStr = '-换刀';
            } else if (item.endType == '4') {
                endStr = '-尾包';
            } else if (item.endType == '5') {
                endStr = '-并包';
            }
            return startStr + endStr
        },

        // 上料补录
        upPack: function (flag = false) {
            var self = this;
            try {
                if (!this.packId) {
                    Dialog.alert({
                        message: '请输入捆包号',
                    });
                    return false;
                }
                if (!this.machineCode) {
                    Dialog.alert({
                        message: '机组不能为空',
                    });
                    return false;
                }
                var params = {
                    serviceId: 'S_LI_DS_0020',
                    factoryArea: localStorage.getItem("factoryAreaTrue"),
                    factoryBuilding: localStorage.getItem('factoryBuilding'),
                    machineCode: this.machineCode,
                    machineName: this.machineName,
                    packId: this.packId,
                    processOrderId: this.processOrderId,
                    autoStart: flag ? '1' : '0'
                };

                post(params).then(function (result) {
                    if (!result || !result.__sys__ || !result.__sys__.status || result.__sys__.status == -1) {
                        // self.$toast(result.__sys__.msg);
                        Dialog.alert({
                            message: result.__sys__.msg,
                        });
                        return false;
                    } else {
                        self.$toast('补录完成');

                        // 清空表单
                        self.packId = '';
                        self.machineCode = '';
                        self.machineName = '';
                        self.processOrderId = '';
                        self.showData = false;
                    }
                }).catch(function (error) {
                    Dialog.alert({
                        message: error.message,
                    });
                    // console.error(error);
                    // self.$toast('网络请求失败，请重试');
                    // if (self.$refs.errorHandler) {
                    //    self.$refs.errorHandler.handleError(error);
                    // }
                });
            } catch (error) {
                console.error('upPack方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
        // 退料
        backPack: function () {
            var self = this;
            try {
                if (!this.packId) {
                    Dialog.alert({
                        message: '请输入捆包号',
                    });
                    return false;
                }
                var params = {
                    serviceId: 'S_LI_DS_0029',
                    factoryArea: localStorage.getItem("factoryAreaTrue"),
                    factoryBuilding: localStorage.getItem('factoryBuilding'),
                    machineCode: this.machineCode,
                    machineName: this.machineName,
                    packId: this.packId,
                    processOrderId: this.processOrderId,
                };
                post(params).then(function (result) {
                    if (!result || result.__sys__.status == -1) {
                        self.$toast(result.__sys__.msg);
                        return false;
                    } else {
                        self.$toast('退料完成');
                        // 清空表单
                        self.packId = '';
                        self.machineCode = '';
                        self.machineName = '';
                        self.processOrderId = '';
                        self.showData = false;
                    }
                }).catch(function (error) {
                    console.error('退料失败:', error);
                    self.$toast('网络请求失败，请重试');
                    if (self.$refs.errorHandler) {
                        self.$refs.errorHandler.handleError(error);
                    }
                });
            } catch (error) {
                console.error('backPack方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
        // 手工结束
        forceEnd: function (item) {
            var self = this;
            try {
                if (!item.uuid) {
                    Dialog.alert({
                        message: '数据异常，请重新查询',
                    });
                    return false;
                }
                var params = {
                    serviceId: 'S_VG_DM_0024',
                    uuid: item.uuid,
                };
                post(params).then(function (result) {
                    if (!result || result.__sys__.status == -1) {
                        self.$toast(result.__sys__.msg);
                        return false;
                    } else {
                        self.$toast('手工结束完成');
                        self.fetchData();
                    }
                }).catch(function (error) {
                    console.error('手工结束失败:', error);
                    self.$toast('网络请求失败，请重试');
                    if (self.$refs.errorHandler) {
                        self.$refs.errorHandler.handleError(error);
                    }
                });
            } catch (error) {
                console.error('forceEnd方法异常:', error);
                if (this.$refs.errorHandler) {
                    this.$refs.errorHandler.handleError(error);
                }
            }
        },
    },

    mounted: function () {
        var self = this;
        // 页面初始化时设置错误处理
        try {
            // 检查基础环境
            if (typeof Promise === 'undefined') {
                throw new Error('您的设备不支持Promise，请更新浏览器版本');
            }

            // 页面初始化完成
            console.log('上料区信息补录页面初始化完成');
        } catch (error) {
            console.error('页面初始化异常:', error);
            if (this.$refs.errorHandler) {
                this.$refs.errorHandler.handleError(error);
            }
        }
    }
};
</script>

<style scoped>
.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}

.foot-sticky-unload {
    margin-top: 1em;
    width: 100%;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload {
    width: calc(50% - 4px) !important;
    margin-bottom: 0.8em !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
    font-size: 12px !important;
    font-weight: bold !important;
    flex: none !important;
}

.foot-sticky-unload .button-container .foot-sticky-btn-unload:nth-child(odd):last-child {
    margin-right: auto !important;
}

.foot-sticky-unload .button-container {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    gap: 8px !important;
    padding: 0 2% !important;
}

.title {
    font-size: 14px;
    padding: 10px 10px;
}

.title2 {
    color: #969799;
    font-size: 14px;
    padding: 10px 10px;
    text-align: center;
}
</style>