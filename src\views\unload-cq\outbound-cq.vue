<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">IMC出库</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <van-cell-group inset style="margin-top: 16px;">
                <van-cell :value="carNumber">
                    <template #title>
                        <span class="custom-title">车牌号</span>
                        <!-- <van-tag type="primary">{{ list.allocTypeName }}</van-tag> -->
                    </template>
                </van-cell>
                <van-cell title="捆包统计">
                    <template #right-icon>
                        <van-badge :content="warehousePackList.length" style="margin-right: 12px;">
                            <van-tag plain type="primary" size="large">仓库已扫描</van-tag>
                        </van-badge>
                        <van-badge :content="cranePackList.length">
                            <van-tag plain type="primary" size="large">行车已扫描</van-tag>
                        </van-badge>
                    </template>
                </van-cell>
                <van-cell title="比对结果" v-model="comparison" />
            </van-cell-group>


            <van-tabs v-model="active" style="margin-top: 12px;height: 46vh;overflow-y: auto;" color="#007aff"
                title-active-color="#007aff">
                <van-tab title="仓库扫描">
                    <van-cell-group inset style="margin-top: 8px;">
                        <van-cell title="捆包号" v-for="(item, index) in warehousePackList" :key="index"
                            :value="item.packId" :class="{ 'error-pack': inSNotInH.includes(item.packId) }" />
                    </van-cell-group>
                </van-tab>
                <van-tab title="行车扫描">
                    <van-cell-group inset style="margin-top: 8px;">
                        <van-cell title="捆包号" v-for="(item, index) in cranePackList" :key="index" :value="item"
                            :class="{ 'error-pack': inHNotInS.includes(item) }" />
                    </van-cell-group>
                </van-tab>
            </van-tabs>


            <div class="foot-sticky">
                <van-button type="info" class="foot-sticky-btn" @click="onConfrim">出库确认</van-button>
            </div>

            <van-dialog v-model="isShowCar" title="选择车牌号" show-cancel-button :beforeClose="mandatoryCar">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="radio">
                        <van-cell-group>
                            <van-cell clickable :title="item.vehicleNo" v-for="(item, index) in carList" :key="index"
                                :label="item.driverName" @click="radio = item.vehicleNo">
                                <template #right-icon>
                                    <van-radio :name="item.vehicleNo" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>
        </div>
    </div>
</template>

<script>
import { post, postIMC } from '../../api/base-service';
import { Dialog, Notify } from "vant";
export default {
    data() {
        return {
            carNumber: localStorage.getItem('carNumber'),
            isShowCar: false,
            radio: '',
            carList: [],
            packList: [],
            carTraceNo: '',
            packId: '',
            warehousePackList: [],
            active: 0,
            itemLenght: 0,
            cranePackList: [],
            comparison: '', //比对结果
            inHNotInS: [], // 不存在仓库中的捆包
            inSNotInH: [], // 不存在行车中的捆包
        };
    },

    async created() {
        this.warehousePackList = this.$route.params.packList;
        this.carTraceNo = localStorage.getItem('carTraceNo');
        this.carNumber = localStorage.getItem('carNumber');
        await this.getCarTracePackList();
    },

    methods: {
        onClickLeft() {
            this.$router.goBack();
        },

        /**
         * 查询车牌号
         */
        async getCarNumberList() {
            const params = {
                serviceId: 'S_LI_RL_0087',
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                this.$toast('无车牌号信息');
                return;
            }

            if (result.list.length == 1) {
                this.carNumber = result.list[0].vehicleNo;
                this.carTraceNo = result.list[0].carTraceNo;
                return;
            }

            this.carList = result.list;
            this.isShowCar = true;
        },

        /**
         * 关闭选择车辆弹出框
         */
        mandatoryCar(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }


            if (!this.radio) {
                Dialog.alert({ message: '请选择车辆' });
                done(false);
                return;
            }

            const data = this.carList.find(c => c.vehicleNo == this.radio);
            this.carNumber = data.vehicleNo;
            this.carTraceNo = data.carTraceNo;
            done(true);
        },

        /**
         * 查询行车实绩捆包
         */
        async getCarTracePackList() {
            const params = {
                serviceId: 'S_LI_RL_0155',
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                rowList: this.warehousePackList,
            };
            const result = await post(params);
            if (!result) {
                return;
            }
            this.cranePackList = result.packIdH; // 行车中的捆包

            // 保存接口返回的不一致捆包信息
            this.inHNotInS = result.inHNotInS || []; // 不存在仓库中的捆包
            this.inSNotInH = result.inSNotInH || []; // 不存在行车中的捆包

            // 前端自己计算额外的捆包
            const warehousePackIds = this.warehousePackList.map(item => item.packId);
            const cranePackIds = this.cranePackList || [];

            // 仓库中有但行车中没有的额外捆包
            const extraInWarehouse = warehousePackIds.filter(id => !cranePackIds.includes(id));
            // 行车中有但仓库中没有的额外捆包  
            const extraInCrane = cranePackIds.filter(id => !warehousePackIds.includes(id));

            // 合并所有需要标红的捆包
            this.inSNotInH = [...new Set([...this.inSNotInH, ...extraInWarehouse])];
            this.inHNotInS = [...new Set([...this.inHNotInS, ...extraInCrane])];

            if (result.inHNotInS.length == 0 && result.inSNotInH.length == 0) {
                // 即使接口返回一致，但如果有额外捆包，仍需提示
                if (extraInWarehouse.length > 0 || extraInCrane.length > 0) {
                    this.comparison = '比对结果不一致，请检查捆包';
                } else {
                    this.comparison = '校验一致';
                }
            } else {
                this.comparison = '比对结果不一致，请检查捆包';
            }
        },

        /**
         * 出库确认
         */
        async onConfrim() {
            if (!this.comparison.includes('校验一致')) {
                Dialog.confirm({
                    title: '提示',
                    message: '校验结果不一致, 请仔细排查。是否确认继续出库',
                }).then(async () => {
                    await this.packOutbound();
                });
                return;
            }
            await this.packOutbound();
        },

        async packOutbound() {
            let allocateVehicleNo = localStorage.getItem('allocateVehicleNo');
            if (!allocateVehicleNo) {
                allocateVehicleNo = '';
            }
            const params = {
                serviceId: 'S_LI_RL_0145',
                rowList: this.warehousePackList.map(r => ({
                    ...r,
                    carTraceNo: this.carTraceNo,
                })),
                warehouseCode: localStorage.getItem('warehouseCode'),
                warehouseName: localStorage.getItem('warehouseName'),
                vehicleNo: this.carNumber,
                carTraceNo: this.carTraceNo,
                factoryArea: localStorage.getItem('factoryAreaTrue'),
                factoryBuilding: localStorage.getItem('factoryBuilding'),
                allocateVehicleNo, // 配单号
            };
            const result = await post(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            localStorage.setItem('billLading', '');
            localStorage.setItem('allocateVehicleNo', '');
            localStorage.setItem('carNumber', '');
            localStorage.setItem('carTraceNo', '');
            // 添加打印提示弹出框
            Dialog.confirm({
                title: '提示',
                message: '出库成功，是否需要打印单据？',
                confirmButtonText: '是',
                cancelButtonText: '否',
            }).then(async () => {
                // 用户点击"是"，执行打印操作
                await this.printBill(result);
                // 打印完成后返回精细化页面
                this.$router.replace('/unload-cq');
            }).catch(() => {
                // 用户点击"否"，直接返回精细化页面
                this.$router.replace('/unload-cq');
            });
        },

        async printBill(data) {
            const params = {
                putoutIdList: data.putoutIdList,
                serviceId: "S_UC_PR_2306226",
            };
            const result = await postIMC(params);
            if (!result || result.__sys__.status == -1) {
                return;
            }
            console.log(result);

        },

    },
};
</script>
<style>
.van-cell__value {
    color: #323233;
}

.error-pack {
    background-color: #ffe6e6 !important;
}

.error-pack .van-cell__value {
    color: #ff4444 !important;
    font-weight: bold;
}

.error-pack .van-cell__title {
    color: #ff4444 !important;
}
</style>
