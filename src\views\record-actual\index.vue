<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">实绩列表</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
                </template>
                <template #right>
                    <span @click="refreshData" style="color: #FFFFFF;">查询</span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <div>
                <van-cell size="large" title="行车名称" is-link v-model="trainName" @click="getLoadTruckOperatorList" />
                <van-field label="库位代码" placeholder="请输入或扫描库位代码" ref="labelsecond" v-model="locationId"
                    class="all-font-size" @keyup.enter.native="searchLocation" enterkeyhint="enter" input-align="right"
                    style="font-size: 16px;">
                    <template #right-icon>
                        <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE"
                            @click="searchLocation"></span>
                    </template>
                </van-field>

                <van-cell title="异常状态" size="large">
                    <template #right-icon>
                        <van-radio-group v-model="plateRollFlag" direction="horizontal">
                            <van-radio name="0">无异常</van-radio>
                            <van-radio name="1">有异常</van-radio>
                        </van-radio-group>
                    </template>
                </van-cell>

                <div class="date-picker-row">
                    <date-picker v-model="startTime" type="datetime" :show-second="true" value-type="format"
                        format="YYYY-MM-DD HH:mm:ss" placeholder="选择起始时间" :disabled-date="disabledStartDate" />
                    <span>
                        至
                    </span>
                    <date-picker v-model="endTime" type="datetime" :show-second="true" value-type="format"
                        format="YYYY-MM-DD HH:mm:ss" placeholder="选择截止时间" :disabled-date="disabledEndDate" />
                </div>
            </div>

            <van-divider content-position="left">实绩内容</van-divider>

            <div ref="scrollContainer" style="height: 60vh;overflow-y: auto;">
                <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
                    <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad"
                        :immediate-check="false" :offset="100" :scroller="scrollContainer">
                        <van-cell-group inset v-for="(item, index) in list" :key="index" @click="toInfo(item)"
                            style="margin-bottom: 16px;">
                            <van-cell title="行车实绩清单号" :value="item.craneResultId" />
                            <van-cell title="行车编号" :value="item.craneId" />
                            <van-cell title="行车名称" :value="item.craneName" />

                            <van-cell title="捆包重量" :value="item.craneOperationWeight" />
                            <van-cell title="抓取时间" :value="formatTime(item.grabSysUpTime)" />
                            <van-cell title="抓取区域" :value="item.startAreaName" />
                            <van-cell title="释放时间" :value="formatTime(item.releaseSysUpTime)" />
                            <van-cell title="释放区域" :value="item.endAreaName" />
                        </van-cell-group>
                    </van-list>
                </van-pull-refresh>
            </div>

            <van-dialog v-model="isShowTruck" title="行车" show-cancel-button :beforeClose="confirmTruck">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="truckRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.craneName" v-for="(item, index) in loadTruckOperatorList"
                                :key="index" @click="truckRadio = item.craneId">
                                <template #right-icon>
                                    <van-radio :name="item.craneId" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>

            <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
                show-cancel-button>
                <div class="dialog-content" style="max-height: 200px;overflow-y: auto;">
                    <van-list @load="searchLocation">
                        <van-radio-group v-model="radio2">
                            <van-cell-group>
                                <van-cell v-for="(item, index) in locationList" :key="index" clickable
                                    @click="isChecked(index, item)">
                                    <template #title>
                                        <div class="ware-title">{{ item.locationId }}</div>
                                    </template>
                                    <template #label>
                                        <div>
                                            库位名称：{{ item.locationName }}</div>
                                    </template>
                                    <template #right-icon>
                                        <van-radio :name="index"><template #icon="props">
                                                <span class="iconfont"
                                                    :class="props.checked ? activeIcon : inactiveIcon"></span>
                                            </template>
                                        </van-radio>
                                    </template>
                                </van-cell>
                            </van-cell-group>
                        </van-radio-group>
                    </van-list>
                </div>
            </van-dialog>
        </div>
    </div>
</template>

<script>
import { post } from '../../api/base-service';

export default {
    data() {
        return {
            localtionIdShow: false,
            locationList: [],
            date: '',
            radio2: '',
            locationName: '',
            locationId: '',
            checkLocaltion: '',
            list: [],
            show: false,
            trainName: '',
            craneId: '',
            isShowTruck: false,
            truckRadio: '',
            loadTruckOperatorList: [],
            needRefresh: false,
            plateRollFlag: '1',
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            now: new Date(),
            startTime: null,
            endTime: null,
            showStartPicker: false,
            showEndPicker: false,
            refreshing: false,
            loading: false,
            finished: false,
            currentPage: 0,
            pageSize: 10,
            scrollContainer: null,
        };
    },
    mounted() {
        this.scrollContainer = this.$refs.scrollContainer;

        this.$nextTick((x) => {
            this.$refs.labelsecond.focus();
        });
    },
    methods: {
        formatTime(timeStr) {
            if (!timeStr || timeStr.length !== 14) return '';
            return `${timeStr.substr(0, 4)}/${timeStr.substr(4, 2)}/${timeStr.substr(6, 2)} ` +
                `${timeStr.substr(8, 2)}:${timeStr.substr(10, 2)}:${timeStr.substr(12, 2)}`;
        },

        formatDate(date) {
            return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
        },

        // 日期禁用规则
        disabledStartDate(date) {
            return date.getTime() > this.now.getTime() || (this.endTime && date.getTime() > new Date(this.endTime).getTime());
        },
        disabledEndDate(date) {
            // 获取当前日期的0点时间戳
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 获取选择日期的0点时间戳
            const selectedDate = new Date(date);
            selectedDate.setHours(0, 0, 0, 0);

            // 如果选择日期是今天，则允许选择
            if (selectedDate.getTime() === today.getTime()) {
                return false;
            }

            // 其他情况：不能选择未来日期，且不能选择早于开始时间的日期
            return date.getTime() > this.now.getTime() ||
                (this.startTime && date.getTime() < new Date(this.startTime).getTime());
        },

        // 接口需要的格式转换
        convertToApiFormat(timeStr) {
            return timeStr ? timeStr.replace(/[^0-9]/g, '').substr(0, 14) : '';
        },

        onClickLeft() {
            this.$router.replace({ name: 'index' });
        },

        toInfo(item) {
            this.$router.push({
                name: "record-actual-info",
                params: {
                    data: item,
                    abnormalFlag: this.plateRollFlag,
                },
            });
        },

        isChecked(index, item) {
            this.radio2 = index;
            this.checkLocaltion = item;
        },

        onCancelItem() {
            this.locationList = [];
            this.radio2 = -1;
            this.localtionIdShow = false;
        },

        /**
 * 库位确认事件
 */
        onConfirmItem() {
            this.locationId = this.checkLocaltion.locationId;
            this.locationName = this.checkLocaltion.locationName;
            this.radio2 = -1;
            this.checkLocaltion = '';
            this.locationList = [];
        },

        // 查询库位
        async searchLocation() {
            this.localtionIdShow = true;
            const params = {
                serviceId: "S_LI_RL_0146",
                warehouseCode: localStorage.getItem('warehouseCode'),
                locationId: this.locationId,
            };
            const result = await post(params);
            this.locationList = [];
            if (!result || !result.result) {
                return;
            }

            if (result.result.length == 1) {
                this.localtionIdShow = false;
                this.isChecked(0, result.result[0]);
                this.onConfirmItem();
                return;
            }
            this.locationList = result.result;
        },

        async fetchData() {
            const params = {
                serviceId: "S_LI_DS_0008",
                craneId: this.craneId,
                locationId: this.locationId,
                startTime: this.convertToApiFormat(this.startTime),
                endTime: this.convertToApiFormat(this.endTime),
                abnormalFlag: this.plateRollFlag,
                offset: this.currentPage,
                limit: this.pageSize,
            };

            const result = await post(params);

            if (!result || !result.craneResultBody) {
                if (this.refreshing) {
                    this.list = [];
                }
                return;
            }

            if (this.refreshing) {
                // 下拉刷新时，重置列表
                this.list = result.craneResultBody;
                this.currentPage = 0;
                this.finished = result.craneResultBody.length < this.pageSize;
            } else {
                // 加载更多时，追加到列表
                this.list = this.list.concat(result.craneResultBody);
                this.finished = result.craneResultBody.length < this.pageSize;
            }
        },

        // 重新查询数据（点击查询按钮）
        refreshData() {
            this.currentPage = 0;
            this.finished = false;
            this.refreshing = true;
            this.fetchData().then(() => {
                this.refreshing = false;
            }).catch(() => {
                this.refreshing = false;
            });
        },

        // 下拉刷新
        onRefresh() {
            this.currentPage = 0;
            this.finished = false;
            this.fetchData().then(() => {
                this.refreshing = false;
            }).catch(() => {
                this.refreshing = false;
            });
        },

        // 加载更多
        onLoad() {
            if (this.finished) return;

            this.currentPage++;
            this.fetchData().then(() => {
                this.loading = false;
            }).catch(() => {
                this.loading = false;
                this.currentPage--; // 加载失败时恢复页码
            });
        },

        async confirmTruck(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.truckRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.loadTruckOperatorList.find(p => p.craneId == this.truckRadio);
            if (!currentData) {
                done();
                return;
            }

            this.trainName = currentData.craneName;
            this.craneId = currentData.craneId;
            // const result = await this.fetchData();

            done(true);
        },


        async getLoadTruckOperatorList() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }
            this.loadTruckOperatorList = result.list;
            this.isShowTruck = true;
        },
    }
};
</script>

<style scoped>
.activeColor {
    color: #007aff;
}

.list-item {
    padding: 10px 0;
}

.item-row {
    margin: 8px 0;
    font-size: 14px;
}

.label {
    color: #666;
    font-weight: bold;
    min-width: 60px;
    display: inline-block;
}

.van-cell__title {
    overflow: hidden;
    text-overflow: ellipsis;
}

.date-picker-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>