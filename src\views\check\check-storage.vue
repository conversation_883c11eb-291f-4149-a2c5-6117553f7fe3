<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">盘库单</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div>
      <van-cell title="无单盘库" to="withoutStorage" class="all-font-size">
        <template #right-icon>
          <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
        </template>
      </van-cell>
    <van-cell title="选择盘库单" to="checkList"  class="all-font-size">
        <template #right-icon>
          <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
        </template>
      </van-cell>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  created(){
  },
  methods: {
    onClickLeft() {
      this.$router.replace('/index');
    },
    testUrl() {
        window.location.href = 'http://localhost:8081/#/check';

    },

  },
};
</script>

<style lang="less" scoped>
  
</style>
