<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">盘库单详情列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div
      class="in-content"
      v-if="
        checkDetailList !== undefined &&
        checkDetailList != null &&
        checkDetailList.length > 0
      "
    >
      <van-cell-group v-for="(item, index) in checkDetailList" :key="index">
        <van-cell>
          <template #title>
            <div>{{ item.packId }}</div>
            <div>
              {{
                item.stockCheckResult == "00"
                  ? "未执行"
                  : item.stockCheckResult == "10"
                  ? "信息在库(实物在库)"
                  : item.stockCheckResult == "11"
                  ? "信息在库(实物不在库)"
                  : item.stockCheckResult== "20"
                  ? "信息不在库(实物不在库)"
                  : item.stockCheckResult == "21"
                  ? "信息不在库(实物在库)"
                  : ""
              }}
            </div>
          </template>
          <template #label>
            <div>{{ item.matInnerId }}</div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div v-else>
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      v-show="isShowBottom"
      @click="goCheckCell"
    >
      <button id="block_button" type="button" class="mui-btn">扫描捆包</button>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      stockCheckNum: "",
      checkDetailList: [],
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  created() {
    this.stockCheckNum = this.$route.query.stockCheckNum;
    this.getCheckList();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goCheckCell() {
      this.$router.togo("/scanCheck?stockCheckNum=" + this.stockCheckNum);
    },
    //获取盘库列表
    async getCheckList() {
      const params = {
        serviceId: "S_LI_RL_0127",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        stockCheckNum: this.stockCheckNum,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.checkDetailList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
</style>
