<template>
    <div>
        <div>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">库位变更</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </div>

        <div>
            <van-cell title="行车" size="large" is-link v-model="craneName" @click="showCrane" style="font-size: 16px;" />
            <van-field label="库位代码" size="large" placeholder="请输入或扫描库位代码" ref="labelsecond" v-model="locationId"
                class="all-font-size" @keyup.enter.native="searchLocation" enterkeyhint="enter" input-align="right"
                style="font-size: 16px;">
                <template #right-icon>
                    <span class="iconfont icon-sousuo search-icon"
                        @click="searchLocation"></span>
                </template>
            </van-field>
            <van-field readonly label="库位名称" size="large" v-model="locationName" input-align="right"
                style="font-size: 16px;" />

            <van-field v-model="packId" ref="packRef" label="捆包号" placeholder="请扫描捆包" input-align="right" size="large"
                @keyup.enter.native="getPack" enterkeyhint="enter" style="font-size: 16px;" />

            <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
                show-cancel-button>
                <div class="dialog-content" style="max-height: 200px;overflow-y: auto;">
                    <div v-if="locationList && locationList.length > 0">
                        <van-list @load="searchLocation">
                            <van-radio-group v-model="radio2">
                                <van-cell-group>
                                    <van-cell v-for="(item, index) in locationList" :key="index" clickable
                                        @click="isChecked(index, item)">
                                        <template #title>
                                            <div class="ware-title">{{ item.locationId }}</div>
                                        </template>
                                        <template #label>
                                            <div>
                                                库位名称：{{ item.locationName }}</div>
                                        </template>
                                        <template #right-icon>
                                            <van-radio :name="index"><template #icon="props">
                                                    <span class="iconfont"
                                                        :class="props.checked ? activeIcon : inactiveIcon"></span>
                                                </template>
                                            </van-radio>
                                        </template>
                                    </van-cell>
                                </van-cell-group>
                            </van-radio-group>
                        </van-list>
                    </div>
                    <div v-else>
                    </div>
                </div>
            </van-dialog>

            <van-dialog v-model="isShowCrane" title="行车" show-cancel-button :beforeClose="confirmCrane">
                <div style="max-height: 320px; overflow-y: auto;">
                    <van-radio-group v-model="craneRadio">
                        <van-cell-group>
                            <van-cell clickable :title="item.craneName" v-for="(item, index) in craneList" :key="index"
                                @click="craneRadio = item.craneId">
                                <template #right-icon>
                                    <van-radio :name="item.craneId" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-radio-group>
                </div>
            </van-dialog>
        </div>

        <div style="height: 50%;">
            <div class="detail_textarea">
                <div class="detail_text" style="padding-bottom: 10px">
                    <div class="fourline-blue"></div>
                    <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                        已扫捆包合计 : <span class="span-count">{{ packList.length }}</span>
                    </div>
                </div>
            </div>

            <div style="height: 80%; overflow-y: auto;">
                <van-swipe-cell v-for="(item, index) in packList" :key="index">
                    <van-cell :border="false">
                        <template #title>
                            <div class="custom-title">{{ item.labelId }}</div>
                            <div class="custom-title">{{ item.packId }}</div>
                        </template>
                        <template #label>
                            <div class="container">
                                <!-- 重/数量行 -->
                                <div class="row">
                                    <div class="label">规格：</div>
                                    <div class="value">{{ item.specsDesc }}</div>
                                </div>

                                <!-- 规格&老库位行 -->
                                <div class="row dual-column">
                                    <div class="column">
                                        <div class="label">重/数量：</div>
                                        <div class="value">{{ item.netWeight }}/{{ item.pieceNum }}</div>
                                    </div>
                                    <div class="column">
                                        <div class="label">新/旧库位：</div>
                                        <div class="value">{{ item.newLocationId }} / {{ item.locationId }}</div>
                                    </div>
                                </div>
                            </div>
                        </template>

                    </van-cell>
                    <template #right>
                        <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                            <span class="swiper-text">删除</span>
                        </button>
                    </template>
                </van-swipe-cell>
            </div>
        </div>


        <div class="foot-sticky">
            <van-button type="info" size="large" class="foot-sticky-btn" @click="confirmToStore">确认</van-button>
        </div>

    </div>
</template>

<script>
import { Dialog } from "vant";
import { post } from '../../api/base-service';

export default {
    name: "InStorage",
    data() {
        return {
            packId: '',
            locationId: '',
            localtionIdShow: false,
            locationList: [],
            plateRollFlag: '1',
            radio2: '',
            activeIcon: "icon-31xuanzhong activeColor",
            inactiveIcon: "icon-weixuanzhong",
            packWeight: '',
            packQuality: '',
            locationName: '',
            moldFlag: '1',
            storageType: '0',
            specsDesc: '',
            craneName: '请选择行车',
            isShowCrane: false,
            craneList: [],
            craneId: '',
            craneRadio: '',
            packList: [],
            warehouseCode: localStorage.getItem('warehouseCode'),
            warehouseName: localStorage.getItem('warehouseName'),
        };
    },
    created() {
        // this.changfouce();
        this.$nextTick((x) => {
            this.$refs.labelsecond.focus();
        });
    },
    methods: {
        // 聚焦
        changfouce() {
            this.$nextTick((x) => {
                this.$refs.packRef.focus();
            });
        },

        deleteListItem(index) {
            //删除数组中值
            this.$delete(this.packList, index);
        },

        onClickLeft() {
            this.$router.replace({
                name: 'index',
            });
        },

        isChecked(index, item) {
            this.radio2 = index;
            this.checkLocaltion = item;
        },

        onCancelItem() {
            this.locationList = [];
        },

        /**
         * 库位确认事件
         */
        onConfirmItem() {
            this.locationId = this.checkLocaltion.locationId;
            this.locationName = this.checkLocaltion.locationName;
            this.radio2 = -1;
            this.checkLocaltion = [];
            this.locationList = [];
            this.localtionIdShow = false;
            this.$refs.packRef.focus();
        },

        // 确认行车
        confirmCrane(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.craneRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.craneList.find(p => p.craneId == this.craneRadio);
            if (!currentData) {
                done();
                return;
            }

            this.craneName = currentData.craneName;
            this.craneId = currentData.craneId;
            done(true);
        },

        async showCrane() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }

            if (result.list.length == 1) {
                this.craneId = result.list[0].craneId;
                this.craneName = result.list[0].craneName;
                return;
            }

            this.craneList = result.list;
            this.isShowCrane = true;
        },

        // 查询库位
        async searchLocation() {
            const params = {
                serviceId: "S_LI_RL_0146",
                warehouseCode: this.warehouseCode,
                locationId: this.locationId,
                startSize: this.offset,
                length: 10
            };
            const result = await post(params);
            this.locationList = [];
            if (!result || !result.result) {
                return;
            }

            if (result.result.length == 1) {
                this.localtionIdShow = false;
                this.isChecked(0, result.result[0]);
                this.onConfirmItem();
                return;
            }
            this.locationList = result.result;
            this.localtionIdShow = true;
        },

        /**
         * 查询捆包
         */
        async getPack() {
            if (!this.locationName) {
                Dialog.alert({ message: '请选择库位' });
                return;
            }

            if (!this.packId) {
                Dialog.alert({ message: '请扫描捆包' });
                return;
            }

            const repeatList = this.packList.find(p => p.packId == this.packId);
            if (repeatList) {
                Dialog.alert({ message: '捆包已扫描' });
                return;
            }

            const params = {
                serviceId: "S_LI_RL_0124",
                segCname: localStorage.getItem("segName"),
                factoryArea: localStorage.getItem("factoryAreaTrue"),
                factoryAreaName: localStorage.getItem("factoryName"),
                packId: this.packId,
                warehouseCode: this.warehouseCode,
                warehouseName: this.warehouseName,
                userName: localStorage.getItem('userName'),
                invertedFlag: "1",
            };
            let res = await post(params);
            if (!res || !res.result) {
                return;
            }

            const result = res.result;
            if (result.length == 0) {
                this.$toast('未查询到捆包数据');
                return;
            }

            this.packList.unshift({
                ...result[0],
                newLocationId: this.locationId,
                newLocationName: this.locationName,
            });
            this.packId = '';
            this.$refs.packRef.focus();
        },

        /**
         * 库位变更确认
         */
        async confirmToStore() {
            if (this.packList.length == 0) {
                Dialog.alert({ message: '请扫描捆包' });
                return;
            }

            // 检查是否所有捆包都设置了新库位
            const unsetPacks = this.packList.filter(pack => !pack.newLocationId);
            if (unsetPacks.length > 0) {
                Dialog.alert({ message: '请为所有捆包设置新库位' });
                return;
            }

            const params = {
                dList: this.packList,
                warehouseName: this.warehouseName,
                warehouseCode: this.warehouseCode,
                factoryArea: localStorage.getItem("factoryAreaTrue"),
                factoryAreaName: localStorage.getItem("factoryName"),
                serviceId: 'S_LI_DS_0011',
                craneId: this.craneId,
                factoryBuilding: localStorage.getItem('factoryBuilding'),
            };
            const res = await post(params);
            if (res.__sys__.status != 1) {
                return;
            }
            this.packList = [];
            this.craneId = '';
            this.craneName = '请选择行车';
            this.$toast(res.__sys__.msg);
        },

        // 确认行车
        confirmCrane(action, done) {
            if (action != 'confirm') {
                done();
                return;
            }

            if (!this.craneRadio) {
                Dialog.alert({ message: '请选择行车' });
                done(false);
                return;
            }

            const currentData = this.craneList.find(p => p.craneId == this.craneRadio);
            if (!currentData) {
                done();
                return;
            }

            this.craneName = currentData.craneName;
            this.craneId = currentData.craneId;
            done(true);
        },

        async showCrane() {
            let queryMap = {
                serviceId: 'S_LI_DS_0006',
            };

            const result = await post(queryMap);
            if (!result || result.__sys__.status == -1) {
                return;
            }

            if (result.list.length == 0) {
                return;
            }

            if (result.list.length == 1) {
                this.craneId = result.list[0].craneId;
                this.craneName = result.list[0].craneName;
                return;
            }

            this.craneList = result.list;
            this.isShowCrane = true;
        },

    },
};
</script>
<style>
.van-cell__value {
    color: #323233;
}

.empty-des {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 32px 0;
}

.load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
}

.activeColor {
    color: #007aff;
}

.container {
    width: 100%;
}

/* 通用行样式 */
.row {
    display: flex;
    align-items: baseline;
    /* 基线对齐 */
    margin-bottom: 8px;
}

/* 标签统一样式 */
.label {
    min-width: 50px;
    /* 统一标签宽度 */
    /* text-align: right; */
    padding-right: 12px;
    color: #666;
}

/* 双列容器 */
.dual-column {
    justify-content: space-between;
    width: 100%;
}

.column {
    display: flex;
    align-items: baseline;
    flex: 1;
    /* 自动分配剩余空间 */
}

/* 值域样式 */
.value {
    flex: 1;
    color: #333;
}

.swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
}

.swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
}

.location-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #007aff !important;
    cursor: pointer;
}

.location-arrow {
    margin-left: 8px;
    color: #007aff;
}

.location-select:active {
    background-color: #f0f9ff;
}

/* 搜索图标样式 */
.search-icon {
    color: #BEBEBE;
    font-size: 16px;
    padding: 6px 8px;
    margin-left: 6px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.search-icon:hover {
    background-color: #f5f5f5;
    color: #007aff;
}

.search-icon:active {
    background-color: #e6f7ff;
    transform: scale(0.95);
}

/* 修复van-field整行对齐问题 */
.van-field {
    display: flex !important;
    align-items: center !important;
    min-height: 56px !important;
}

.van-field__label {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    line-height: normal !important;
}

.van-field__body {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
}

.van-field__control {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    line-height: normal !important;
}

.van-field__right-icon {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
}
</style>
