<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">账套选择</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div v-if="segInfoList && segInfoList.length > 0">
      <div class="in-content">
        <van-radio-group v-model="radio">
          <van-cell-group>
            <van-cell v-for="(item, index) in segInfoList" :key="index" clickable @click="isChecked(item, index)">
              <template #title>
                <div class="seg-title">{{ item.segNo }}</div>
              </template>
              <template #label>
                <div class="seg-name">{{ item.segFullName }}</div>
              </template>
              <template #right-icon>
                <van-radio :name="item.segNo"><template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
      <div class="mui-input-row" style="margin: 0" @click="goFactory" v-show="isShowBottom">
        <button type="button" class="mui-btn">
          确&nbsp; &nbsp; &nbsp;&nbsp;定
        </button>
      </div>

    </div>
    <div v-else>
    </div>
  </div>
</template>

<script>
import { post } from '../../api/base-service';
import userPreferencesManager from '../../utils/userPreferences';

export default {
  data() {
    return {
      radio: '',
      segInfoList: [],
      orgVal: "",
      check: false,
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      pathUrl: "",
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.pathUrl = from.fullPath;
    });
  },
  mounted() {
    if (this.pathUrl === "/index") {
      this.getOrganization();
    } else {
      this.segInfoList = this.$route.params.segInfoList;
    }
    
    // 恢复已选择的账套
    this.loadExistingSelection();
    
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    // 加载已有的账套选择
    loadExistingSelection() {
      const existingSegNo = localStorage.getItem("segNo");
      if (existingSegNo) {
        this.radio = existingSegNo;
        this.check = true;
      }
    },
    
    onClickLeft() {
      this.$router.goBack();
    },

    async goFactory() {
      if (this.check) {
        // 只有重庆账套才查询菜单权限
        const segNo = localStorage.getItem("segNo");
        if (segNo === "JC000000") {
          await this.queryMenuPermissions();
        }
        
        this.$router.push("/select-factory");
      } else {
        this.$toast("请选择业务单元！");
      }
    },
    isChecked(item, index) {
      this.radio = item.segNo;
      this.check = true;
      localStorage.setItem("segNo", item.segNo);
      localStorage.setItem("segName", item.segName);
      
      // 保存当前用户的偏好设置
      const currentUserId = localStorage.getItem("userId");
      if (currentUserId) {
        userPreferencesManager.saveCurrentPreferencesToUser(currentUserId);
      }
    },


    async getOrganization() {
      const params = {
        serviceId: "S_LI_RL_0052",
      };
      const res = await post(params);
      if (!res || res.segInfoList.length == 0) {
        return;
      }
      this.segInfoList = res.segInfoList;
    },

    // 查询菜单权限
    async queryMenuPermissions() {
      try {
        const params = {
          serviceId: "S_LI_RL_0666",
        };
        const res = await post(params);
        console.log('菜单权限查询结果:', res);
        
        if (res && res.__sys__ && res.__sys__.status === 1) {
          // 将菜单权限保存到localStorage中
          localStorage.setItem("menuPermissions", JSON.stringify(res.menuList || []));
          // 标记权限查询成功
          localStorage.setItem("menuPermissionsLoaded", "true");
        } else {
          // 接口返回失败，清除权限数据，菜单不显示
          localStorage.removeItem("menuPermissions");
          localStorage.setItem("menuPermissionsLoaded", "false");
          console.warn('菜单权限查询失败，菜单将不显示');
        }
      } catch (error) {
        // 接口异常，清除权限数据，菜单不显示
        localStorage.removeItem("menuPermissions");
        localStorage.setItem("menuPermissionsLoaded", "false");
        console.error('菜单权限查询异常，菜单将不显示:', error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.search-list {
  margin-top: 8px;
}

.activeColor {
  color: #007aff;
}

.seg-title {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #007aff;
  line-height: 20px;
}

.seg-name {
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
}
</style>
