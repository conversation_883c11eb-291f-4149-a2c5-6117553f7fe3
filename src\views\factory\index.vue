<template>
  <div class="factory-body">
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">选择厂区/仓库</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="deep-van">
      <van-dropdown-menu active-color="#0000ff">
        <van-dropdown-item ref="item">
          <template #title>
            <div>
              {{ checkFactoryName }}
            </div>
          </template>
          <div v-if="factoryOption && factoryOption.length > 0">
            <van-radio-group v-model="factoryArea">
              <van-cell-group>
                <van-cell v-for="(item, index) in factoryOption" :key="index" clickable
                  @click="isChecked2(index, item)">
                  <template #title>
                    <div class="seg-title">{{ item.factoryAreaName + `${segNo.includes('JC') ?
                      `-${item.factoryBuildingName}` : ''}` }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.factoryArea + `${segNo.includes('JC') ?
                      `-${item.factoryBuildingName}` : ''}`">
                      <template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>
          <div v-else>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
      <div style="height: 12px"></div>
      <van-dropdown-menu class="select-menu" active-color="#0000ff">
        <van-dropdown-item :title="checkWareName" @open="openItem" ref="item2">
          <div v-if="warehouseOption && warehouseOption.length > 0">
            <van-radio-group v-model="radio2">
              <van-cell-group>
                <van-cell v-for="(item, index) in warehouseOption" :key="index" clickable
                  @click="isChecked(index, item)">
                  <template #title>
                    <div class="ware-title">{{ item.warehouseCode }}</div>
                  </template>
                  <template #label>
                    <div class="ware-name">{{ item.warehouseName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.warehouseCode"><template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>
          <div v-else>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>
    <div class="mui-input-row" style="margin: 0" @click="onClick">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div>
  </div>
</template>

<script>
import { post } from '../../api/base-service';
import userPreferencesManager from '../../utils/userPreferences';

export default {
  data() {
    return {
      radio2: "",
      show: false,
      checkFactoryName: "选择厂区",
      checkWareName: "选择仓库",
      factoryArea: "",
      factoryName: "",
      wareName: "",
      factoryOption: [],
      warehouseOption: [],
      testclass: "",
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      factoryBuilding: '', // 厂房代码
      factoryBuildingName: '', // 厂房名称
      segNo: localStorage.getItem('segNo'),
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.pathUrl = from.fullPath;
    });
  },
  async created() {
    await this.getFactoryList();
    this.loadExistingSelections();
  },
  methods: {
    // 加载已有的选择
    loadExistingSelections() {
      const existingFactoryName = localStorage.getItem("factoryName");
      const existingWarehouseName = localStorage.getItem("warehouseName");
      const existingFactoryBuildingName = localStorage.getItem("factoryBuildingName");

      if (existingFactoryName && existingFactoryBuildingName) {
        const displayName = this.segNo.includes('JC')
          ? `${existingFactoryName}-${existingFactoryBuildingName}`
          : existingFactoryName;
        this.checkFactoryName = displayName;
        this.factoryName = existingFactoryName;
        this.factoryBuildingName = existingFactoryBuildingName;
      }

      if (existingWarehouseName) {
        this.checkWareName = existingWarehouseName;
        this.wareName = existingWarehouseName;
        this.radio2 = localStorage.getItem("warehouseCode");
      }
    },
    //查询厂区列表
    async getFactoryList() {
      const params = {
        serviceId: "S_LI_RL_0008",
        userId: localStorage.getItem("userId"),
        segNo: localStorage.getItem("segNo"),
        segName: localStorage.getItem("segName"),
        accessToken: localStorage.getItem("accessToken"),
      };

      const result = await post(params);
      if (!result) {
        return;
      }

      this.factoryOption = result.list;

    },
    //查询仓库列表
    async getStoreList() {
      // 重庆需要截取
      let factoryArea = this.factoryArea;
      if (this.segNo.includes('JC')) {
        factoryArea = this.factoryBuilding;
      }

      const params = {
        serviceId: "S_LI_RL_0009",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        factoryArea: factoryArea,
        factoryAreaName: this.factoryName,
      };
      const result = await post(params);
      if (!result) {
        return;
      }
      this.warehouseOption = result.result;
    },
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.checkWareName = item.warehouseName;
      this.wareName = item.warehouseName;
      this.radio2 = item.warehouseCode;
      this.onClick();
      this.$refs.item2.toggle();
    },
    openItem() {
      if (this.factoryName.length == 0) {
        this.$toast("请先选择厂区");
      }
    },
    isChecked2(index, item) {
      const fName = item.factoryAreaName + `${this.segNo.includes('JC') ?
        `-${item.factoryBuildingName}` : ''}`;
      this.checkFactoryName = fName;
      this.factoryName = item.factoryAreaName;
      this.factoryBuildingName = item.factoryBuildingName; // 保存厂房名称
      this.factoryArea = item.factoryArea + `${this.segNo.includes('JC') ?
        `-${item.factoryBuildingName}` : ''}`;
      this.factoryBuilding = item.factoryBuilding;
      this.getStoreList();
      this.warehouseOption = [];
      this.checkWareName = '选择仓库';
      this.wareName = '';
      this.radio2 = '';
      this.$refs.item.toggle();
    },
    isEmptyStr(s) {
      if (s == null || s === "") {
        return true;
      }
      return false;
    },
    onClick() {
      if (this.wareName.length == 0) {
        this.$toast("请选择厂区仓库");
      } else {
        localStorage.setItem("factoryName", this.factoryName);
        localStorage.setItem("factoryBuildingName", this.factoryBuildingName); // 保存厂房名称

        // 重庆需要截取
        let factoryArea = this.factoryArea;
        if (this.segNo.includes('JC')) {
          const factoryAreaList = this.factoryArea.split('-');
          if (factoryAreaList.length == 2) {
            factoryArea = factoryAreaList[0];
          } else if (factoryAreaList.length == 4) {
            factoryArea = factoryAreaList[0] + '-' + factoryAreaList[1];
          } else {
            factoryArea = this.factoryArea;
          }
        }

        localStorage.setItem("factoryArea", this.segNo.includes('KF') ? this.factoryArea : this.factoryBuilding);
        localStorage.setItem("factoryAreaTrue", factoryArea);
        localStorage.setItem("warehouseCode", this.radio2);
        localStorage.setItem("warehouseName", this.wareName);
        localStorage.setItem("factoryBuilding", this.factoryBuilding);

        // 保存当前用户的偏好设置
        const currentUserId = localStorage.getItem("userId");
        if (currentUserId) {
          userPreferencesManager.saveCurrentPreferencesToUser(currentUserId);
        }

        //如果不是新标签页打开的则直接返回
        // this.$router.push("/index");
        if (this.pathUrl === "/") {
          this.$router.goBack();
        } else {
          //如果不是新标签页打开的则直接返回
          this.$router.toReplace("/index");
        }

      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-dropdown-menu__item {
  display: flex;
  justify-content: flex-start;
  padding-left: 10px;
}

.factory-body {
  height: 100vh;
  background-color: #f3f3f3;
}

.ware-title {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #007aff;
  line-height: 20px;
}

.ware-name {
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
}

.activeColor {
  color: #007aff;
}
</style>
