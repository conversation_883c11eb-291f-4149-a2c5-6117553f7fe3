<template>
  <div>
    <div>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择提货单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </div>

    <div class="in-content" style="padding-bottom: 20px;">
      <van-cell title="车牌号" size="large" v-model="carNumber" />
      <van-field v-model="billLading" ref="ladRef" label="提货单" placeholder="请扫描或输入提货单号" input-align="right"
        @keyup.enter.native="topSearchFunc" />
    </div>
    <div class="search-list" style="overflow-y: auto;height: 50%;" v-if="searchList && searchList.length > 0">
      <van-checkbox-group v-model="checkBoxResult">
        <van-swipe-cell v-for="(item, index) in searchList" :key="item.ladingBillId">
          <van-cell-group @click="toggle(item)">
            <van-cell>
              <template #title>
                <div class="load-number">
                  {{ item.ladingBillId }}
                  <span v-if="item.billingMethod" class="billing-method-tag" :class="getBillingMethodClass(item.billingMethod)">
                    {{ getBillingMethodText(item.billingMethod) }}
                  </span>
                </div>
                <div>仓库编号: {{ item.warehouseCode }}</div>
                <div>客户: {{ item.settleUserName }}</div>
              </template>
              <template #right-icon>
                <van-checkbox :name="item.ladingBillId" ref="checkboxes">
                  <template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>

          <template #right>
            <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
              <span class="swiper-text">删除</span>
            </button>
          </template>
        </van-swipe-cell>
      </van-checkbox-group>
    </div>

    <div v-else>
      <div class="search-list">
        <div class="empty-des">暂未查询到提单号列表</div>
      </div>
    </div>

    <div class="foot-sticky" style="position: fixed;bottom: 0;">
      <van-button type="info" size="large" class="foot-sticky-btn" @click="confirmToStore">确定</van-button>
    </div>

    <van-dialog v-model="isShowInputCode" :title="getCodeTitle()" :beforeClose="beforeCloseCode" show-cancel-button>
      <!-- 进度提示 -->
      <div v-if="isProcessingQueue" class="queue-progress">
        <div class="progress-text">
          正在处理第 {{ queueProgress.current }} / {{ queueProgress.total }} 个提单
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: getProgressPercentage() + '%' }"></div>
        </div>
      </div>
      
      <!-- 当前提单信息 -->
      <div v-if="currentProcessingBill" class="current-bill-info">
        <div class="bill-number">
          提单号: {{ currentProcessingBill.ladingBillId }}
          <span v-if="currentProcessingBill.billingMethod" class="billing-method-tag" :class="getBillingMethodClass(currentProcessingBill.billingMethod)">
            {{ getBillingMethodText(currentProcessingBill.billingMethod) }}
          </span>
        </div>
        <div class="bill-customer">客户: {{ currentProcessingBill.settleUserName }}</div>
      </div>
      
      <van-field v-model="textCode" label="验证码" placeholder="请输入验证码" />
    </van-dialog>

    <van-dialog v-model="show" title="选择提货单" :beforeClose="beforeCloseLoad" show-cancel-button>
      <van-checkbox-group v-model="checkboxDialogResult" style="height: 200px;overflow-y: auto;">
        <van-cell-group>
          <van-cell v-for="item in diaLadingList" :key="item.ladingBillId" @click="diaToggle(item)">
            <template #title>
              <div class="load-number">
                {{ item.ladingBillId }}
                <span v-if="item.billingMethod" class="billing-method-tag" :class="getBillingMethodClass(item.billingMethod)">
                  {{ getBillingMethodText(item.billingMethod) }}
                </span>
              </div>
              <div>仓库编号: {{ item.warehouseCode }}</div>
              <div>客户: {{ item.settleUserName }}</div>
            </template>
            <template #right-icon>
              <van-checkbox :name="item.ladingBillId" ref="checkboxes">
                <template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
              </van-checkbox>
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </van-dialog>
  </div>
</template>

<script>
import { post } from "../../api/base-service";
import { Dialog, Notify } from "vant";
export default {
  name: "InStorage",
  data() {
    return {
      carNumber: "", //车牌号
      carTraceNo: "", //车辆跟踪号
      isShowCarNumber: false,
      carNumberActions: [],
      carNumberList: [], // 车牌号数组
      billLading: "", // 提货单
      searchList: [], //提货单明细
      phone: "", //手机号
      handPointId: "", //装卸点id
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      checkBoxResult: [],
      isShowInputCode: false,
      textCode: '',
      resultBillData: '',
      show: false,
      checkboxDialogResult: [], // 弹出框选择的提单
      diaLadingList: [], // 弹出框的提单list
      codeTitle: '验证码',
      segNo: localStorage.getItem('segNo'),
      
      // 新增：提单处理队列相关
      pendingBillsQueue: [], // 待处理的提单队列
      currentProcessingBill: null, // 当前正在处理的提单
      isProcessingQueue: false, // 是否正在处理队列
      queueProgress: { current: 0, total: 0 }, // 队列处理进度
      isFromQueue: false, // 当前验证码是否来自队列处理
      
      // 新增：区分初始加载和扫描带出的提单
      initialBillIds: [], // 页面初始加载的提单号
      scanAddedBillIds: [], // 扫描添加的提单号
    };
  },
  async created() {
    this.carNumber = localStorage.getItem('carNumber');
    this.carTraceNo = localStorage.getItem('carTraceNo');
    this.handPointId = localStorage.getItem('handPointId');

    // 根据配单号查询提单
    if (this.segNo.includes('JC')) {
      await this.getBillListIn();
    }
    
    // 记录初始化完成后的提单号（用于区分后续扫描添加的提单）
    this.initialBillIds = [...this.checkBoxResult];
    
    this.$nextTick((x) => {
      this.$refs.ladRef.focus();
    });

  },
  beforeRouteEnter(to, from, next) {
    // 当从上个页面corporateInfo返回当前页面时，不刷新当前页面
    to.meta.keepAlive = from.name == "board-detail";
    next();
  },
  methods: {
    onClickLeft() {
      // this.$router.goBack();
      this.$router.togo({
        name: "end-load",
        params: {
          vehicleNo: this.carNumber,
          carTraceNo: this.carTraceNo,
          handPointId: this.handPointId,
        },
      });
    },

    deleteListItem(index, item) {
      const billId = item.ladingBillId;
      
      //删除数组中值
      this.$delete(this.searchList, index);
      
      // 从选中数组中移除
      const checkIndex = this.checkBoxResult.findIndex(id => id === billId);
      if (checkIndex > -1) {
        this.checkBoxResult.splice(checkIndex, 1);
      }
      
      // 从相应的分类数组中移除
      const initialIndex = this.initialBillIds.findIndex(id => id === billId);
      if (initialIndex > -1) {
        this.initialBillIds.splice(initialIndex, 1);
      }
      
      const scanIndex = this.scanAddedBillIds.findIndex(id => id === billId);
      if (scanIndex > -1) {
        this.scanAddedBillIds.splice(scanIndex, 1);
      }
    },

    //查询提货单明细
    topSearchFunc() {
      if (this.billLading) {
        this.getLadingBillList();
      } else {
        this.$toast("请扫描或输入提货单号");
      }
    },

    async confirmToStore() {
      if (this.checkBoxResult.length == 0) {
        this.$toast("请选择提货单");
        return;
      }

      // 获取扫描添加的提单号（只有用户扫描操作带出的新提单）
      const newBillIds = this.scanAddedBillIds;

      // 如果没有新扫描的提单，不需要调用接口，直接跳转
      if (newBillIds.length == 0) {
        localStorage.setItem('billLading', this.checkBoxResult.join(','));
        this.$router.togo({
          name: "in-store",
          params: {
            carNumber: this.carNumber,
            carTraceNo: this.carTraceNo,
            billLading: this.checkBoxResult,
            handPointId: this.handPointId,
          },
        });
        return;
      }

      // 有新扫描的提单，调用S_LI_RL_0190接口
      try {
        const params = {
          serviceId: "S_LI_RL_0190",
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          carNumber: this.carNumber,
          carTraceNo: this.carTraceNo,
          handPointId: this.handPointId,
          allocateVehicleNo: localStorage.getItem('allocateVehicleNo'), // 配单号
          ladingBillIds: newBillIds, // 只传递扫描带出的新的提单号
        };

        const res = await post(params);
        
        if (!res || res.__sys__.status == "-1") {
          this.$toast("提交失败，请重试");
          return;
        }

        this.$toast("提交成功");
        
        // 提交成功后，存储数据并跳转页面
        localStorage.setItem('billLading', this.checkBoxResult.join(','));
        this.$router.togo({
          name: "in-store",
          params: {
            carNumber: this.carNumber,
            carTraceNo: this.carTraceNo,
            billLading: this.checkBoxResult,
            handPointId: this.handPointId,
          },
        });
      } catch (error) {
        console.error("调用S_LI_RL_0190接口失败:", error);
        this.$toast("网络错误，请重试");
      }
    },

    async getLadingBillList() {
      const params = {
        serviceId: "S_LI_RL_0040",
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        ladingBillId: this.billLading,
      };
      const res = await post(params);

      if (!res || !res.result || res.__sys__.status == "-1") {
        return;
      }

      const resultList = res.result.filter(c => !this.searchList.some((d) => d.ladingBillId === c.ladingBillId));

      if (resultList.length == 0) {
        return;
      }

      this.billLading = '';
      
      if (resultList.length == 1) {
        await this.processSingleBill(resultList[0], false); // 扫描添加的提单
        return;
      }
      
      // 多个提单时显示选择弹出框
      this.show = true;
      this.checkboxDialogResult = [];
      this.diaLadingList = resultList;
    },

    /**
     * 是否需要输入验证码
     * @param obj 
     */
    async getIsCheckCode(obj) {
      const data = {
        serviceId: 'S_LI_RL_0107',
        ladingBillIdList: [obj.ladingBillId],
        warehouseCode: localStorage.getItem('warehouseCode'),
      };
      const res = await post(data);
      if (res && res.result && res.result.length > 0) {
        const resData = res.result[0];
        const isCheck = resData.checkStatus == '1';

        // 等于1就要输入验证码
        if (isCheck) {
          this.resultBillData = {
            ...obj,
            ...resData,
          };
          this.currentProcessingBill = this.resultBillData;
          this.codeTitle = this.isProcessingQueue ? 
            `验证码 (${this.queueProgress.current}/${this.queueProgress.total})` : 
            '验证码';
          this.isShowInputCode = true;
          this.textCode = '';
          return false;
        }
      }

      return true;
    },

    /**
     * 验证码弹出框确认按钮
     * @param action 
     * @param done 
     */
    async beforeCloseCode(action, done) {
      if (action != 'confirm') {
        // 取消时重置状态并可能询问是否继续队列
        this.resetCodeDialogState(true);
        done();
        return;
      }

      if (!this.textCode) {
        Dialog.alert({ message: '请填写验证码' });
        done(false);
        return;
      }

      const params = {
        serviceId: 'S_LI_RL_0108',
        warehouseCode: localStorage.getItem('warehouseCode'),
        rowList: [{
          ...this.resultBillData,
          ebillPassword: this.textCode,
        }],
      };
      const result = await post(params);
      if (!result || result.__sys__.status == -1) {
        done(false);
        return;
      }

      this.$toast('验证成功');
      
      // 如果在弹出框中选择的提单
      if (this.show) {
        this.checkboxDialogResult.push(this.resultBillData.ladingBillId);
      } else {
        // 添加到主列表
        // 如果是队列处理(isFromQueue=true)，则是初始加载的提单
        // 如果不是队列处理(isFromQueue=false)，则是扫描添加的提单
        this.addBillToList(this.resultBillData, this.isFromQueue);
      }

      // 重置验证码对话框状态（确认时不询问队列继续）
      this.resetCodeDialogState(false);
      done(true);

      // 如果是队列处理，继续处理下一个提单
      if (this.isFromQueue) {
        this.isFromQueue = false;
        setTimeout(() => {
          this.processNextBillInQueue();
        }, 500); // 短暂延迟提升用户体验
      }
    },

    /**
     * 重置验证码对话框状态
     * @param {boolean} isCancelled 是否是取消操作
     */
    resetCodeDialogState(isCancelled = false) {
      this.resultBillData = '';
      this.textCode = '';
      this.billLading = '';
      
      // 只有在取消时且正在处理队列时才询问是否继续
      if (isCancelled && this.isProcessingQueue && this.pendingBillsQueue.length > 0) {
        Dialog.confirm({
          title: '提示',
          message: `还有 ${this.pendingBillsQueue.length} 个提单未处理，是否继续？`,
        }).then(() => {
          // 继续处理队列
          setTimeout(() => {
            this.processNextBillInQueue();
          }, 300);
        }).catch(() => {
          // 停止处理队列
          this.stopQueueProcessing();
        });
      }
    },

    /**
     * 停止队列处理
     */
    stopQueueProcessing() {
      const remainingCount = this.pendingBillsQueue.length;
      const processedCount = this.queueProgress.current;
      
      this.isProcessingQueue = false;
      this.isFromQueue = false;
      this.currentProcessingBill = null;
      this.pendingBillsQueue = [];
      this.queueProgress = { current: 0, total: 0 };
      
      if (processedCount > 0) {
        this.$toast(`已处理 ${processedCount} 个提单，剩余 ${remainingCount} 个已跳过`);
      }
    },

    /**
 * 弹出框提单选择事件(勾选一个, 就查询是否需要调用接口)
 * @param item 
 */
    async diaToggle(item) {
      let id = item.ladingBillId;
      if (this.checkboxDialogResult.includes(id)) {
        let index = this.checkboxDialogResult.findIndex(item => item == id);
        this.checkboxDialogResult.splice(index, 1);
      } else {
        if (!this.segNo.includes('JC')) {
          const isCheck = await this.getIsCheckCode(item);
          if (!isCheck) {
            return;
          }
        }
        this.checkboxDialogResult.push(id);
      }
    },

    // 根据配车单号查询提单
    async getBillListIn() {
      const params = {
        vehicleNo: this.carNumber,
        carTraceNo: this.carTraceNo,
        allocateVehicleNo: localStorage.getItem('allocateVehicleNo'),
        serviceId: 'S_LI_RL_0153',
      };

      const res = await post(params);
      if (!res || !res.list || res.__sys__.status == "-1") {
        return;
      }

      if (res.list.length == 1) {
        await this.processSingleBill(res.list[0], true); // 初始加载的提单
        return;
      }

      // 批量处理多个提单，使用队列机制
      await this.processBillQueue(res.list);
    },

    /**
     * 处理单个提单
     * @param bill 提单数据
     * @param isInitial 是否是初始加载的提单
     */
    async processSingleBill(bill, isInitial = false) {
      this.currentProcessingBill = bill;
      
      if (!this.segNo.includes('JC')) {
        const isCheck = await this.getIsCheckCode(bill);
        if (!isCheck) {
          // 需要验证码，等待用户输入
          return;
        }
      }
      
      // 不需要验证码，直接添加
      this.addBillToList(bill, isInitial);
    },

    /**
     * 处理提单队列
     * @param bills 提单列表
     */
    async processBillQueue(bills) {
      this.pendingBillsQueue = [...bills];
      this.isProcessingQueue = true;
      this.queueProgress = { current: 0, total: bills.length };
      
      await this.processNextBillInQueue();
    },

    /**
     * 处理队列中的下一个提单
     */
    async processNextBillInQueue() {
      if (this.pendingBillsQueue.length === 0) {
        // 队列处理完成
        this.completeQueueProcessing();
        return;
      }

      const currentBill = this.pendingBillsQueue.shift();
      this.currentProcessingBill = currentBill;
      this.queueProgress.current = this.queueProgress.total - this.pendingBillsQueue.length;
      this.isFromQueue = true;

      if (!this.segNo.includes('JC')) {
        const isCheck = await this.getIsCheckCode(currentBill);
        if (!isCheck) {
          // 需要验证码，等待用户输入，验证成功后会继续处理队列
          return;
        }
      }

      // 不需要验证码，直接添加并继续处理下一个（初始加载的提单）
      this.addBillToList(currentBill, true);
      await this.processNextBillInQueue();
    },

    /**
     * 完成队列处理
     */
    completeQueueProcessing() {
      const processedCount = this.queueProgress.total;
      this.isProcessingQueue = false;
      this.isFromQueue = false;
      this.currentProcessingBill = null;
      this.queueProgress = { current: 0, total: 0 };
      this.pendingBillsQueue = [];
      
      if (processedCount > 0) {
        this.$toast(`成功处理 ${processedCount} 个提单`);
      }
    },

    /**
     * 添加提单到列表
     * @param bill 提单数据
     * @param isInitial 是否是初始加载的提单
     */
    addBillToList(bill, isInitial = false) {
      if (!this.searchList.some(item => item.ladingBillId === bill.ladingBillId)) {
        this.searchList.push(bill);
        this.checkBoxResult.push(bill.ladingBillId);
        
        // 如果是初始加载的提单，记录到初始提单号数组中
        if (isInitial) {
          this.initialBillIds.push(bill.ladingBillId);
        } else {
          // 如果不是初始加载的提单，记录到扫描添加的提单号数组中
          this.scanAddedBillIds.push(bill.ladingBillId);
        }
      }
    },

    /**
 * 选择提单框确认按钮
 * @param action 
 * @param done 
 */
    beforeCloseLoad(action, done) {
      if (action != 'confirm') {
        done();
        return;
      }

      if (action == 'confirm' && this.checkboxDialogResult.length == 0) {
        Dialog.alert({ message: '请选择提单' });
        done(false);
        return;
      }
      const checkList = this.diaLadingList.filter(d => this.checkboxDialogResult.includes(d.ladingBillId));
      
      // 添加选中的提单到列表（这些都是扫描带出的新提单，不是初始加载的）
      checkList.forEach(bill => {
        this.addBillToList(bill, false);
      });
      done();

    },

    // 选择提单
    toggle(item) {
      let id = item.ladingBillId;
      if (this.checkBoxResult.includes(id)) {
        // 取消选择
        let index = this.checkBoxResult.findIndex(item => item == id);
        this.checkBoxResult.splice(index, 1);
      } else {
        // 选择提单
        this.checkBoxResult.push(id);
      }
      
      // 注意：这里只是切换选择状态，不改变提单的分类（初始/扫描添加）
      // 因为提单的分类在添加时就已经确定了
    },

    getCodeTitle() {
      if (this.isProcessingQueue) {
        return `验证码 (${this.queueProgress.current}/${this.queueProgress.total})`;
      }
      return this.codeTitle;
    },

    getProgressPercentage() {
      if (this.queueProgress.total === 0) {
        return 0;
      }
      return (this.queueProgress.current / this.queueProgress.total) * 100;
    },

    /**
     * 根据billingMethod获取显示文本
     * @param billingMethod 
     * @returns 
     */
    getBillingMethodText(billingMethod) {
      return billingMethod === '10' ? '普通' : '形式';
    },

    /**
     * 根据billingMethod获取CSS类名
     * @param billingMethod 
     * @returns 
     */
    getBillingMethodClass(billingMethod) {
      return billingMethod === '10' ? 'billing-normal' : 'billing-formal';
    },

  },
};
</script>
<style>
.van-cell__value {
  color: #323233;
}

.empty-des {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 32px 0;
}

.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}

.activeColor {
  color: #007aff;
}

.swiper-btn-delete {
  width: 56px;
  height: 100%;
  background: #d33017;
  opacity: 1;
}

.swiper-text {
  letter-spacing: 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
}

.queue-progress {
  margin-bottom: 20px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #323233;
}

.progress-bar {
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #007aff;
}

.current-bill-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007aff;
}

.bill-number {
  font-size: 16px;
  font-weight: 600;
  color: #007aff;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.bill-number::before {
  content: "📋";
  margin-right: 6px;
  font-size: 14px;
}

.bill-customer {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.bill-customer::before {
  content: "👤";
  margin-right: 6px;
  font-size: 12px;
}

.billing-method-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  font-weight: 400;
  display: inline-block;
}

.billing-normal {
  background-color: #e8f5e8;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.billing-formal {
  background-color: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffadd2;
}
</style>