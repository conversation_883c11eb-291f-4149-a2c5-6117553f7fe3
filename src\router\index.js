import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
// 需要左方向动画的路由用this.$router.to('****')
VueRouter.prototype.togo = function (path) {
  this.isleft = true
  this.isright = false
  this.push(path)
}
VueRouter.prototype.toReplace = function (path) {
  this.isleft = true
  this.isright = false
  this.replace(path)
}
// 需要右方向动画的路由用this.$router.goRight('****')
VueRouter.prototype.goRight = function (path) {
  this.isright = true
  this.isleft = false
  this.push(path)
}
// 需要返回按钮动画的路由用this.$router.goBack()，返回上一个路由
VueRouter.prototype.goBack = function () {
  this.isright = true
  this.isleft = false
  this.go(-1)
}
// 点击浏览器返回按钮执行，此时不需要路由回退
VueRouter.prototype.togoback = function () {
  this.isright = true
  this.isleft = false
}
// 点击浏览器前进按钮执行
VueRouter.prototype.togoin = function () {
  this.isright = false
  this.isleft = true
}
const Router = new VueRouter({
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/login')
    }, {
      path: '/organization',
      name: 'organization',
      component: () => import('../views/login/organization')
    }, {
      path: '/select-factory',
      name: 'select-factory',
      component: () => import('../views/factory')
    },
    {
      path: '/index',
      name: 'index',
      meta: { auth: true },
      component: () => import('../views/index')
    }, {
      path: '/in-storage',
      name: 'in-storage',
      meta: { auth: true, keepAlive: true },
      component: () => import('../views/in-storage')
    }, {
      path: '/in-store',
      name: 'in-store',
      meta: { auth: true },
      component: () => import('../views/in-storage/in-store')
    }, {
      path: '/unload',
      name: 'unload',
      meta: { auth: true },
      component: () => import('../views/unload')
    }, {
      path: '/scan-store',
      name: 'scan-store',
      meta: { auth: true },
      component: () => import('../views/unload/scan-store')
    }, {
      path: '/end-load',
      name: 'end-load',
      meta: { auth: true },
      component: () => import('../views/public/end-load')
    }, {
      path: '/spot-check',
      name: 'spot-check',
      meta: { auth: true },
      component: () => import('../views/spot-check')
    }, {
      path: '/vehicle-queue',
      name: 'vehicle-queue',
      meta: { auth: true },
      component: () => import('../views/vehicle-queue'),
    }, {
      path: '/find-materials',
      name: 'find-materials',
      meta: { auth: true },
      component: () => import('../views/find-materials'),
    },
    // 重庆
    {
      path: '/unload-cq',
      name: 'unload-cq',
      meta: { auth: true },
      component: () => import('../views/unload-cq'),
    }, {
      path: '/loading-unload-diagram',
      name: 'loading-unload-diagram',
      meta: { auth: true },
      component: () => import('../views/loading-unload-diagram'),
    }, {
      path: '/detail',
      name: 'detail',
      meta: { auth: true },
      component: () => import('../views/loading-unload-diagram/detail.vue'),
    }, {
      path: '/vehicle-sorting',
      name: 'vehicle-sorting',
      meta: { auth: true },
      component: () => import('../views/vehicle-sorting/index.vue'),
    }, {
      path: '/checkStorage',
      name: 'check-storage',
      component: () => import('../views/check/check-storage')
    }, {
      path: '/check',
      name: 'check',
      component: () => import('../views/check')
    },
    {
      path: '/check2',
      name: 'check2',
      component: () => import('../views/check/index2')
    }, {
      path: '/checkStorage',
      name: 'check-storage',
      component: () => import('../views/check/check-storage')
    }, {
      path: '/withoutStorage',
      name: 'without-storage',
      component: () => import('../views/check/without-storage')
    },
    {
      path: '/scanCheck',
      name: 'scan-check',
      component: () => import('../views/check/scan-check')
    }, {
      path: '/checkList',
      name: 'check-list',
      component: () => import('../views/check/check-list')
    }, {
      path: '/checkListDetail',
      name: 'check-list-detail',
      component: () => import('../views/check/check-list-detail')
    }, {
      path: '/inverted-storehouse',
      name: 'inverted-storehouse',
      component: () => import('../views/inverted-storehouse'),
    }, {
      path: '/regis-reject',
      name: 'regis-reject',
      component: () => import('../views/regis-reject'),
    }, {
      path: '/mold-storage',
      name: 'mold-storage',
      component: () => import('../views/mold-storage/mold-storage'),
    }, {
      path: '/record-actual',
      name: 'record-actual',
      meta: { keepAlive: true },
      component: () => import('../views/record-actual'),
    }, {
      path: '/record-actual-info',
      name: 'record-actual-info',
      component: () => import('../views/record-actual/record-actual-info.vue'),
    }, {
      path: '/outbound-cq',
      name: 'outbound-cq',
      component: () => import('../views/unload-cq/outbound-cq.vue'),
    }, {
      path: '/crane-order-list',
      name: 'crane-order-list',
      component: () => import('../views/crane-order-list'),
    }, {
      path: '/crane-order-list-detail',
      name: 'crane-order-list-detail',
      component: () => import('../views/crane-order-list/list.vue'),
    }, {
      path: '/overhaul',
      name: 'overhaul',
      component: () => import('../views/overhaul'),
    }, {
      path: '/scan-pack-store',
      name: 'scan-pack-store',
      component: () => import('../views/scan-pack-store'),
    }, {
      path: '/regional-position',
      name: 'regional-position',
      component: () => import('../views/regional-position'),
    }, {
      path: '/fault',
      name: 'fault',
      component: () => import('../views/fault'),
    }, {
      path: '/select-car',
      name: 'select-car',
      component: () => import('../views/unload-cq/select-car'),
    }, {
      path: '/select-bill-in-imc',
      name: 'select-bill-in-imc',
      // meta: { keepAlive: true },
      component: () => import('../views/unload-cq/select-bill-in-imc'),
    }, {
      path: '/scan-out-bound',
      name: 'scan-out-bound',
      // meta: { keepAlive: true },
      component: () => import('../views/unload-cq/scan-out-bound'),
    }, {
      path: '/scan-store-cq',
      name: 'scan-store-cq',
      component: () => import('../views/unload-cq/scan-store-cq'),
    }, {
      path: '/up-pack',
      name: 'up-pack',
      component: () => import('../views/up-pack'),
    }, {
      path: '/unload-cq/hoisting-info',
      name: 'hoisting-info',
      component: () => import('../views/unload-cq/hoisting-info.vue'),
    }, {
      path: '/unload-cq/seach-pack',
      name: 'seach-pack',
      component: () => import('../views/unload-cq/seach-pack.vue'),
    }, {
      path: '/sling',
      name: 'sling',
      component: () => import('../views/sling'),
    }, {
      path: '/print-inbound',
      name: 'print-inbound',
      meta: { auth: true },
      component: () => import('../views/print-inbound'),
    },{
      path: '/init-pack',
      name: 'init-pack',
      meta: { auth: true },
      component: () => import('../views/init-pack'),
    },


  ]
})
export default Router
