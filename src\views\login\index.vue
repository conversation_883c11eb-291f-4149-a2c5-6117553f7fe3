<template>
  <div class="login-page">
    <div class="container">
      <div class="login-head">
        <div>
          <img src="@/assets/logo/loginLogo.png" alt="" class="logo-img" />
        </div>
        <div class="login-title">IMOM-PDA</div>
      </div>
      <div class="wave"></div>
      <div class="wave1"></div>
      <div class="wave2"></div>
      <div class="wave3"></div>
    </div>

    <div class="login-content">
      <div class="login-cell-group">
        <div class="login-cell">
          <div class="cell-remix">
            <span class="iconfont icon-ren-copy" style="color: #333"></span>
            <span class="iconfont icon-shuxian icon-line"></span>
            <input type="text" placeholder="用户名" class="login-input" v-model="userName" />
          </div>
        </div>
        <div class="login-cell">
          <div class="cell-remix">
            <span class="iconfont icon-lock" style="color: #333"></span>
            <span class="iconfont icon-shuxian icon-line"></span>
            <input placeholder="密码" class="login-input" :type="type" v-model="password" @keydown="addByEnterKey" />
            <span class="iconfont icon-yanjing" @click="seePassword"
              :class="active ? 'see-password' : 'notsee-password'"></span>
          </div>
        </div>
        <div class="login-btn">
          <button class="btn" @click="goLogin">
            登&nbsp; &nbsp; &nbsp;&nbsp;录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post } from '../../api/base-service';
import userPreferencesManager from '../../utils/userPreferences';

export default {
  data() {
    return {
      active: false,
      userName: "",
      password: "",
    };
  },
  created() {
  },
  mounted() {

  },
  computed: {
    type() {
      return this.active ? "text" : "password";
    },
  },
  methods: {
    addByEnterKey(e) {
      //Enter键的代码就是13
      if (e.keyCode != 13) {
        return;
      }
      this.goLogin();
    },

    async goLogin() {
      const params = {
        serviceId: "S_LI_RL_0006",
        userName: this.userName.trim(),
        password: this.password.trim(),
      };

      const data = await post(params);
      console.log(data);
      if (!data) {
        return;
      }

      if (!data.segInfoList || data.segInfoList.length == 0) {
        this.$toast(data.__sys__.msg);
        return;
      }

      localStorage.setItem("userId", data.userId);
      localStorage.setItem("userName", data.userName);
      localStorage.setItem("accessToken", data.accessToken);
      
      // 加载该用户的偏好设置
      userPreferencesManager.loadUserPreferencesToStorage(data.userId);
      
      // 检查用户是否有完整的偏好设置
      const hasCompleteInfo = userPreferencesManager.hasCompletePreferences(data.userId);
      
      if (hasCompleteInfo) {
        // 如果已有完整信息，且为重庆账套则查询菜单权限，然后跳转到主页面
        const segNo = localStorage.getItem("segNo");
        if (segNo === "JC000000") {
          await this.queryMenuPermissions();
        }
        this.$router.push("/index");
      } else {
        // 否则进入正常的选择流程
        this.$router.push({
          name: "organization",
          params: {
            segInfoList: data.segInfoList,
          },
        });
      }
    },

    seePassword() {
      this.active = !this.active;
    },

    // 查询菜单权限
    async queryMenuPermissions() {
      try {
        const params = {
          serviceId: "S_LI_RL_0666",
        };
        const res = await post(params);
        console.log('菜单权限查询结果:', res);
        
        if (res && res.__sys__ && res.__sys__.status === 1) {
          // 将菜单权限保存到localStorage中
          localStorage.setItem("menuPermissions", JSON.stringify(res.menuList || []));
          // 标记权限查询成功
          localStorage.setItem("menuPermissionsLoaded", "true");
        } else {
          // 接口返回失败，清除权限数据，菜单不显示
          localStorage.removeItem("menuPermissions");
          localStorage.setItem("menuPermissionsLoaded", "false");
          console.warn('菜单权限查询失败，菜单将不显示');
        }
      } catch (error) {
        // 接口异常，清除权限数据，菜单不显示
        localStorage.removeItem("menuPermissions");
        localStorage.setItem("menuPermissionsLoaded", "false");
        console.error('菜单权限查询异常，菜单将不显示:', error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.login-page {
  background-color: #ffffff;
  height: 100vh;
}

.see-password {
  color: #007aff;
  padding-right: 5px;
  font-size: 22px;
}

.notsee-password {
  padding-right: 5px;
  font-size: 22px;
}

.container {
  position: relative;
  width: 100%;
  height: 200px;
  margin: 0 auto;
  overflow: hidden;
  background: linear-gradient(180deg,
      #74b1f6 2%,
      #007aff 64%,
      rgba(0, 122, 255, 0.3) 76%,
      rgba(255, 255, 255, 0) 100%);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
}

.wave,
.wave1,
.wave3,
.wave2 {
  position: absolute;
  width: 100%;
  height: 205px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-30%, 0) rotate(160deg);
}

.wave1 {
  background-image: repeating-linear-gradient(rgba(255, 255, 255, 0.1));
  transform: translate(-40%, 0) rotate(180deg);
}

.wave3 {
  background-image: repeating-linear-gradient(rgba(255, 255, 255, 0.3));
  transform: translate(55%, 0) rotate(200deg);
  top: 140px;
}

.wave2 {
  background: rgba(255, 255, 255, 0.2);
  transform: translate(40%, 0) rotate(120deg);
}

.login-head {
  height: 140px;
}

.logo-img {
  padding-top: 35px;
  width: 50px;
  height: 50px;
  display: block;
  margin: 0 auto;
}

.login-title {
  text-align: center;
  font-size: 18px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;
  line-height: 25px;
  letter-spacing: 1px;
}

.login-content {
  width: 100%;
  background-color: #ffffff;
  text-align: center;
}

.login-cell-group {
  display: inline-block;
  vertical-align: middle;
}

.login-content:after {
  /*在行内伪造一个元素，称为middle的靶子*/
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.login-cell {
  width: 100%;
  height: 48px;
  margin-top: 20px;
  background: #ffffff;
  box-shadow: 0px 4px 8px -1px rgba(30, 82, 138, 0.19);
  border-radius: 6px 6px 6px 6px;
  opacity: 1;
  border: 1px solid #007aff;
}

.cell-remix {
  margin: 10px 0px 10px 20px;
  display: flex;
  align-items: center;
}

.login-input {
  width: 190px;
  border: none;
  height: 30px;
  font-size: 16px;
}

input::-webkit-input-placeholder {
  /* WebKit browsers*/
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #999999;
  line-height: 18px;
}

.icon-line {
  margin: 0 5px 0 5px;
  color: #999999;
}

.login-btn {
  margin-top: 20px;
}

.btn {
  width: 100%;
  height: 46px;
  background: #007aff;
  border: none;
  border-radius: 6px 6px 6px 6px;
  opacity: 1;
  font-size: 15px;
  font-weight: 500;
  color: #ffffff;
  line-height: 20px;
  font-family: Noto Sans SC;
}
</style>