/**
 * 用户偏好设置管理工具
 * 为每个用户单独维护厂区、仓库、segNo等选择缓存
 */

const USER_PREFERENCES_KEY = 'userPreferences';

// 默认的用户偏好设置结构
const DEFAULT_USER_PREFERENCES = {
  segNo: '',
  segName: '',
  factoryName: '',
  factoryArea: '',
  factoryAreaTrue: '',
  factoryBuilding: '',
  factoryBuildingName: '',
  warehouseCode: '',
  warehouseName: ''
};

/**
 * 用户偏好设置管理类
 */
class UserPreferencesManager {
  
  /**
   * 获取所有用户的偏好设置
   * @returns {Object} 所有用户偏好设置
   */
  getAllUserPreferences() {
    try {
      const preferences = localStorage.getItem(USER_PREFERENCES_KEY);
      return preferences ? JSON.parse(preferences) : {};
    } catch (error) {
      console.error('获取用户偏好设置失败:', error);
      return {};
    }
  }

  /**
   * 保存所有用户的偏好设置
   * @param {Object} allPreferences 所有用户偏好设置
   */
  saveAllUserPreferences(allPreferences) {
    try {
      localStorage.setItem(USER_PREFERENCES_KEY, JSON.stringify(allPreferences));
    } catch (error) {
      console.error('保存用户偏好设置失败:', error);
    }
  }

  /**
   * 获取指定用户的偏好设置
   * @param {string} userId 用户ID
   * @returns {Object} 用户偏好设置
   */
  getUserPreferences(userId) {
    if (!userId) return { ...DEFAULT_USER_PREFERENCES };
    
    const allPreferences = this.getAllUserPreferences();
    return allPreferences[userId] || { ...DEFAULT_USER_PREFERENCES };
  }

  /**
   * 保存指定用户的偏好设置
   * @param {string} userId 用户ID
   * @param {Object} preferences 用户偏好设置
   */
  saveUserPreferences(userId, preferences) {
    if (!userId) return;
    
    const allPreferences = this.getAllUserPreferences();
    allPreferences[userId] = { ...preferences };
    this.saveAllUserPreferences(allPreferences);
  }

  /**
   * 加载用户偏好设置到localStorage
   * @param {string} userId 用户ID
   */
  loadUserPreferencesToStorage(userId) {
    const userPreferences = this.getUserPreferences(userId);
    
    // 将用户偏好设置加载到localStorage中
    Object.keys(userPreferences).forEach(key => {
      if (userPreferences[key]) {
        localStorage.setItem(key, userPreferences[key]);
      } else {
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * 从localStorage收集当前用户偏好设置
   * @returns {Object} 当前localStorage中的用户偏好设置
   */
  collectCurrentPreferences() {
    const preferences = {};
    Object.keys(DEFAULT_USER_PREFERENCES).forEach(key => {
      const value = localStorage.getItem(key);
      if (value) {
        preferences[key] = value;
      }
    });
    return preferences;
  }

  /**
   * 保存当前localStorage中的设置到指定用户
   * @param {string} userId 用户ID
   */
  saveCurrentPreferencesToUser(userId) {
    if (!userId) return;
    
    const currentPreferences = this.collectCurrentPreferences();
    this.saveUserPreferences(userId, currentPreferences);
  }

  /**
   * 检查用户是否有完整的偏好设置
   * @param {string} userId 用户ID
   * @returns {boolean} 是否有完整设置
   */
  hasCompletePreferences(userId) {
    const userPreferences = this.getUserPreferences(userId);
    const requiredKeys = ['segNo', 'segName', 'factoryName', 'factoryBuildingName', 'warehouseCode', 'warehouseName'];
    
    return requiredKeys.every(key => {
      const value = userPreferences[key];
      return value && value.trim() !== '';
    });
  }

  /**
   * 清除指定用户的偏好设置
   * @param {string} userId 用户ID
   */
  clearUserPreferences(userId) {
    if (!userId) return;
    
    const allPreferences = this.getAllUserPreferences();
    delete allPreferences[userId];
    this.saveAllUserPreferences(allPreferences);
  }

  /**
   * 清除localStorage中的用户偏好相关数据
   */
  clearStoragePreferences() {
    Object.keys(DEFAULT_USER_PREFERENCES).forEach(key => {
      localStorage.removeItem(key);
    });
  }
}

// 创建单例实例
const userPreferencesManager = new UserPreferencesManager();

export default userPreferencesManager; 